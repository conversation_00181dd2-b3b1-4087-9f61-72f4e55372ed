import { BadRequestException, Injectable } from '@nestjs/common';
import { EOAuthScope, OAuthTokenDTO, OAuthTokenResponseDTO } from '@superawesome/freekws-classic-wrapper-common';
import { plainToInstance } from 'class-transformer';

import { OauthService, secondsInADay } from './oauth.service';
import { IAppOauthClient } from '../app/types';
import { OrgEnv } from '../org-env/org-env.entity';
import { UserService } from '../user/user.service';

@Injectable()
export class OAuthTokenHandlers {
  constructor(private readonly oauthService: OauthService, private readonly userService: UserService) {}

  async handleRefreshToken(data: OAuthTokenDTO, client: IAppOauthClient): Promise<OAuthTokenResponseDTO> {
    const { refresh_token } = data;
    if (!refresh_token) {
      throw new BadRequestException('refresh_token is required for refresh_token grant_type');
    }

    const accessToken = await this.oauthService.getAccessTokenFromRefreshToken(refresh_token, data, client);
    const refreshToken = await this.oauthService.getRefreshToken(data.scope, client);

    return plainToInstance(OAuthTokenResponseDTO, {
      token_type: 'bearer',
      refresh_token: refreshToken.token,
      access_token: accessToken,
      expires_in: secondsInADay,
    });
  }

  async handleClientCredentials(data: OAuthTokenDTO, client: IAppOauthClient): Promise<OAuthTokenResponseDTO> {
    const token = await this.oauthService.getAccessToken(data, client);
    const refreshToken = await this.oauthService.getRefreshToken(data.scope, client);

    return plainToInstance(
      OAuthTokenResponseDTO,
      {
        token_type: 'bearer',
        access_token: token,
        refresh_token: refreshToken.token,
        expires_in: secondsInADay,
      },
      { excludeExtraneousValues: true },
    );
  }

  async handlePassword(data: OAuthTokenDTO, client: IAppOauthClient): Promise<OAuthTokenResponseDTO> {
    const { username, password } = data;
    if (!username || !password) {
      throw new BadRequestException('Username and password are both required');
    }

    const verifyPassword = await this.userService.verifyPassword(username, password, client.orgEnvId);
    if (!verifyPassword) {
      throw new BadRequestException('Invalid username or password');
    }

    const userScopedToken = await this.oauthService.createUserTokenFromUsername(client, username);
    const refreshToken = await this.oauthService.getRefreshToken(EOAuthScope.USER, client, username);

    return plainToInstance(
      OAuthTokenResponseDTO,
      {
        token_type: 'bearer',
        access_token: userScopedToken,
        refresh_token: refreshToken.token,
        expires_in: secondsInADay,
      },
      { excludeExtraneousValues: true },
    );
  }

  async handleAuthorizationCode(
    data: OAuthTokenDTO,
    client: IAppOauthClient,
    orgEnv: OrgEnv,
  ): Promise<OAuthTokenResponseDTO> {
    const { code, redirect_uri } = data;
    if (!code) {
      throw new BadRequestException('Missing code parameter');
    }

    if (!redirect_uri) {
      throw new BadRequestException('Missing redirect_uri parameter');
    }

    const codeJwt = await this.oauthService.getJwt(code, orgEnv.id);
    const isVerifiedCode = await this.oauthService.verifyCode(
      {
        codeJwt,
        redirect_uri,
        clientId: client.clientId,
        appId: client.appId,
        codeVerifier: data.code_verifier,
      },
      orgEnv.id,
    );

    if (!isVerifiedCode) {
      throw new BadRequestException('Code verification failed');
    }

    const userAccessToken = await this.oauthService.createUserTokenFromUserId(codeJwt.userId, client);
    const refreshToken = await this.oauthService.getRefreshTokenWithUserId(codeJwt.userId, client);

    return plainToInstance(
      OAuthTokenResponseDTO,
      {
        token_type: 'bearer',
        access_token: userAccessToken,
        refresh_token: refreshToken.token,
        expires_in: secondsInADay,
      },
      { excludeExtraneousValues: true },
    );
  }
}
