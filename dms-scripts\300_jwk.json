[{"rule-type": "selection", "rule-id": "300", "rule-name": "Include jwks table", "object-locator": {"schema-name": "public", "table-name": "jwks"}, "rule-action": "include", "filters": [{"filter-type": "source", "column-name": "deletedAt", "filter-conditions": [{"filter-operator": "null"}]}]}, {"rule-type": "transformation", "rule-id": "301", "rule-name": "<PERSON>ame jwks to jwk", "rule-action": "rename", "rule-target": "table", "object-locator": {"schema-name": "public", "table-name": "jwks"}, "value": "jwk"}, {"rule-type": "transformation", "rule-id": "302", "rule-name": "Add orgEnvId column to jwks", "rule-target": "column", "object-locator": {"schema-name": "public", "table-name": "jwks"}, "rule-action": "add-column", "value": "orgEnvId", "expression": "'org-env-id-value'", "data-type": {"type": "string", "length": 255}}, {"rule-type": "transformation", "rule-id": "310", "rule-name": "Remove deletedAt column from jwks", "rule-action": "remove-column", "rule-target": "column", "object-locator": {"schema-name": "public", "table-name": "jwks", "column-name": "deletedAt"}}]