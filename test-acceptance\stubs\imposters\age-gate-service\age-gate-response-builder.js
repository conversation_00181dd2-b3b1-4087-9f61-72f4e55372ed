function ageGateResponseBuilder(config) {
  const { dob, country = 'US' } = config.request.query;
  const defaultAgeOfDigitalConsent = 16;

  const countries = {
    AC: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    AD: { ageOfDigitalConsent: 16, ageOfMajority: 18, regions: {} },
    AE: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    AF: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    AG: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    AI: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    AL: { ageOfDigitalConsent: 16, ageOfMajority: 18, regions: {} },
    AM: { ageOfDigitalConsent: 16, ageOfMajority: 18, regions: {} },
    AO: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    AQ: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    AR: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    AS: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    AT: { ageOfDigitalConsent: 14, ageOfMajority: 18, regions: {} },
    AU: { ageOfDigitalConsent: 15, ageOfMajority: 18, regions: {} },
    AW: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    AX: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    AZ: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    BA: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    BB: { ageOfDigitalConsent: 18, ageOfMajority: 18, regions: {} },
    BD: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    BE: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    BF: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    BG: { ageOfDigitalConsent: 14, ageOfMajority: 18, regions: {} },
    BH: { ageOfDigitalConsent: 18, ageOfMajority: 18, regions: {} },
    BI: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    BJ: { ageOfDigitalConsent: 16, ageOfMajority: 18, regions: {} },
    BL: { ageOfDigitalConsent: 15, ageOfMajority: 18, regions: {} },
    BM: { ageOfDigitalConsent: 14, ageOfMajority: 18, regions: {} },
    BN: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    BO: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    BR: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    BS: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    BT: { ageOfDigitalConsent: 18, ageOfMajority: 18, regions: {} },
    BV: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    BW: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    BY: { ageOfDigitalConsent: 16, ageOfMajority: 18, regions: {} },
    BZ: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    CA: { ageOfDigitalConsent: 14, ageOfMajority: 18, regions: {} },
    CC: { ageOfDigitalConsent: 15, ageOfMajority: 18, regions: {} },
    CD: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    CF: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    CG: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    CH: { ageOfDigitalConsent: 16, ageOfMajority: 18, regions: {} },
    CI: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    CK: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    CL: { ageOfDigitalConsent: 14, ageOfMajority: 18, regions: {} },
    CM: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    CN: { ageOfDigitalConsent: 14, ageOfMajority: 18, regions: {} },
    CO: { ageOfDigitalConsent: 18, ageOfMajority: 18, regions: {} },
    CR: { ageOfDigitalConsent: 15, ageOfMajority: 18, regions: {} },
    CV: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    CW: { ageOfDigitalConsent: 16, ageOfMajority: 18, regions: {} },
    CX: { ageOfDigitalConsent: 15, ageOfMajority: 18, regions: {} },
    CY: { ageOfDigitalConsent: 14, ageOfMajority: 18, regions: {} },
    CZ: { ageOfDigitalConsent: 15, ageOfMajority: 18, regions: {} },
    DE: { ageOfDigitalConsent: 16, ageOfMajority: 18, regions: {} },
    DJ: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    DK: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    DM: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    DO: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    DZ: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    EC: { ageOfDigitalConsent: 15, ageOfMajority: 18, regions: {} },
    EE: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    EG: { ageOfDigitalConsent: 18, ageOfMajority: 18, regions: {} },
    EH: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    ER: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    ES: { ageOfDigitalConsent: 14, ageOfMajority: 18, regions: {} },
    ET: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    FI: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    FJ: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    FK: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    FM: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    FO: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    FR: { ageOfDigitalConsent: 15, ageOfMajority: 18, regions: {} },
    GA: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    GB: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    GD: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    GE: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    GF: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    GG: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    GH: { ageOfDigitalConsent: 18, ageOfMajority: 18, regions: {} },
    GI: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    GL: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    GM: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    GN: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    GP: { ageOfDigitalConsent: 15, ageOfMajority: 18, regions: {} },
    GQ: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    GR: { ageOfDigitalConsent: 15, ageOfMajority: 18, regions: {} },
    GS: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    GT: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    GU: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    GW: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    GY: { ageOfDigitalConsent: 15, ageOfMajority: 18, regions: {} },
    HK: { ageOfDigitalConsent: 18, ageOfMajority: 18, regions: {} },
    HM: { ageOfDigitalConsent: 15, ageOfMajority: 18, regions: {} },
    HN: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    HR: { ageOfDigitalConsent: 16, ageOfMajority: 18, regions: {} },
    HT: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    HU: { ageOfDigitalConsent: 16, ageOfMajority: 18, regions: {} },
    ID: { ageOfDigitalConsent: 18, ageOfMajority: 18, regions: {} },
    IE: { ageOfDigitalConsent: 16, ageOfMajority: 18, regions: {} },
    IL: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    IM: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    IN: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    IO: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    IQ: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    IS: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    IT: { ageOfDigitalConsent: 14, ageOfMajority: 18, regions: {} },
    JE: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    JM: { ageOfDigitalConsent: 18, ageOfMajority: 18, regions: {} },
    JO: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    JP: { ageOfDigitalConsent: 16, ageOfMajority: 18, regions: {} },
    KE: { ageOfDigitalConsent: 18, ageOfMajority: 18, regions: {} },
    KG: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    KH: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    KI: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    KM: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    KN: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    KR: { ageOfDigitalConsent: 14, ageOfMajority: 18, regions: {} },
    KW: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    KY: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    KZ: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    LA: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    LB: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    LC: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    LI: { ageOfDigitalConsent: 16, ageOfMajority: 18, regions: {} },
    LK: { ageOfDigitalConsent: 18, ageOfMajority: 18, regions: {} },
    LR: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    LS: { ageOfDigitalConsent: 18, ageOfMajority: 18, regions: {} },
    LT: { ageOfDigitalConsent: 14, ageOfMajority: 18, regions: {} },
    LU: { ageOfDigitalConsent: 16, ageOfMajority: 18, regions: {} },
    LV: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    LY: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    MA: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    MC: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    MD: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    ME: { ageOfDigitalConsent: 18, ageOfMajority: 18, regions: {} },
    MF: { ageOfDigitalConsent: 16, ageOfMajority: 18, regions: {} },
    MG: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    MH: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    MK: { ageOfDigitalConsent: 14, ageOfMajority: 18, regions: {} },
    ML: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    MM: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    MN: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    MO: { ageOfDigitalConsent: 18, ageOfMajority: 18, regions: {} },
    MP: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    MQ: { ageOfDigitalConsent: 15, ageOfMajority: 18, regions: {} },
    MR: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    MS: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    MT: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    MU: { ageOfDigitalConsent: 16, ageOfMajority: 18, regions: {} },
    MV: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    MW: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    MX: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    MY: { ageOfDigitalConsent: 18, ageOfMajority: 18, regions: {} },
    MZ: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    NA: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    NC: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    NE: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    NF: { ageOfDigitalConsent: 15, ageOfMajority: 18, regions: {} },
    NG: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    NI: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    NL: { ageOfDigitalConsent: 16, ageOfMajority: 18, regions: {} },
    NO: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    NP: { ageOfDigitalConsent: 18, ageOfMajority: 18, regions: {} },
    NR: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    NU: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    NZ: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    OM: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    PA: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    PE: { ageOfDigitalConsent: 14, ageOfMajority: 18, regions: {} },
    PF: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    PG: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    PH: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    PK: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    PL: { ageOfDigitalConsent: 16, ageOfMajority: 18, regions: {} },
    PM: { ageOfDigitalConsent: 15, ageOfMajority: 18, regions: {} },
    PN: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    PR: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    PS: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    PT: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    PW: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    PY: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    QA: { ageOfDigitalConsent: 18, ageOfMajority: 18, regions: {} },
    RE: { ageOfDigitalConsent: 16, ageOfMajority: 18, regions: {} },
    RO: { ageOfDigitalConsent: 16, ageOfMajority: 18, regions: {} },
    RS: { ageOfDigitalConsent: 15, ageOfMajority: 18, regions: {} },
    RU: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    RW: { ageOfDigitalConsent: 16, ageOfMajority: 18, regions: {} },
    SA: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    SB: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    SC: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    SD: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    SE: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    SG: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    SH: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    SI: { ageOfDigitalConsent: 15, ageOfMajority: 18, regions: {} },
    SJ: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    SK: { ageOfDigitalConsent: 16, ageOfMajority: 18, regions: {} },
    SL: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    SM: { ageOfDigitalConsent: 16, ageOfMajority: 18, regions: {} },
    SN: { ageOfDigitalConsent: 15, ageOfMajority: 18, regions: {} },
    SO: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    SR: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    SS: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    ST: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    SV: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    SX: { ageOfDigitalConsent: 16, ageOfMajority: 18, regions: {} },
    SZ: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    TC: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    TD: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    TF: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    TG: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    TH: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    TJ: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    TK: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    TL: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    TM: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    TN: { ageOfDigitalConsent: 18, ageOfMajority: 18, regions: {} },
    TO: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    TR: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    TT: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    TV: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    TW: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    TZ: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    UA: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    UG: { ageOfDigitalConsent: 18, ageOfMajority: 18, regions: {} },
    UM: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    US: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    UY: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    UZ: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    VA: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    VC: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    VE: { ageOfDigitalConsent: 18, ageOfMajority: 18, regions: {} },
    VG: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    VI: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    VN: { ageOfDigitalConsent: 16, ageOfMajority: 18, regions: {} },
    VU: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    WF: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    WS: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    XK: { ageOfDigitalConsent: 16, ageOfMajority: 18, regions: {} },
    YE: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    YT: { ageOfDigitalConsent: 15, ageOfMajority: 18, regions: {} },
    ZA: { ageOfDigitalConsent: 18, ageOfMajority: 18, regions: {} },
    ZM: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
    ZW: { ageOfDigitalConsent: 13, ageOfMajority: 18, regions: {} },
  };

  const userAge = new Date(new Date('2024-10-10') - new Date(dob)).getFullYear() - new Date(0).getFullYear();
  const consentAge = countries[country] ? countries[country].ageOfDigitalConsent : defaultAgeOfDigitalConsent;
  return {
    statusCode: 200,
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      response: { country, consentAge, userAge, underAgeOfDigitalConsent: userAge < consentAge },
    }),
  };
}
