import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  PrimaryColumn,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity()
@Index('token_orgEnvId', ['token', 'orgEnvId'], { unique: true })
export class RefreshToken {
  @PrimaryGeneratedColumn()
  id: number;

  @PrimaryColumn({ type: 'character varying' })
  orgEnvId: string;

  @Column({ nullable: true })
  userId?: number;

  @Column({ nullable: true })
  appId?: number;

  @Column()
  token: string;

  @Column()
  expires: Date;

  @Column()
  clientId: string;

  @Column()
  scope: string;

  @CreateDateColumn()
  createdAt?: Date;

  @UpdateDateColumn()
  updatedAt?: Date;
}
