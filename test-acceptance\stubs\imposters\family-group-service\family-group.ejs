{
    "port": 4749,
    "protocol": "http",
    "name": "family-group",
    "recordRequests": true,
    "defaultResponse": {
      "statusCode": 404,
      "body": {
        "text": "Resource Not Found"
      },
      "headers": {
        "content-type": "application/json"
      }
    },
    "stubs": [
        {
          "predicates": [ <% include family-group.predicates.ejs %> ],
          "responses": [ <% include family-group.responses.ejs %> ]
        }
    ]
  }
