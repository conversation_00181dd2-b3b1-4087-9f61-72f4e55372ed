import { BadRequestException, ForbiddenException } from '@nestjs/common';
import { TestingModule } from '@nestjs/testing';
import {
  EOAuthGrantType,
  EOAuthScope,
  OAuthAuthorisePayloadDTO,
  OAuthTokenDTO,
} from '@superawesome/freekws-classic-wrapper-common';

import { OAuthTokenHandlers } from './oauth-token-handlers';
import { OauthController } from './oauth.controller';
import { OauthService, secondsInADay } from './oauth.service';
import { TJWT } from './types';
import { IAppOauthClient } from '../app/types';
import { Testing } from '../common/utils';
import { OrgEnv } from '../org-env/org-env.entity';
import { UserService } from '../user/user.service';

describe('OauthController', () => {
  let controller: OauthController;
  let tokenHandlers: OAuthTokenHandlers;
  let userService: UserService;
  let service: OauthService;

  beforeEach(async () => {
    const module: TestingModule = await Testing.createModule({
      controllers: [OauthController],
      providers: [OAuthTokenHandlers, UserService, OauthService],
    });

    controller = module.get<OauthController>(OauthController);
    tokenHandlers = module.get<OAuthTokenHandlers>(OAuthTokenHandlers);
    userService = module.get<UserService>(UserService);
    service = module.get<OauthService>(OauthService);
  });

  describe('token', () => {
    const mockTokenResponse = {
      token_type: 'bearer',
      access_token: 'test-access-token',
      refresh_token: 'test-refresh-token',
      expires_in: secondsInADay,
    };

    beforeEach(() => {
      jest.spyOn(tokenHandlers, 'handleRefreshToken').mockResolvedValue(mockTokenResponse);
      jest.spyOn(tokenHandlers, 'handleClientCredentials').mockResolvedValue(mockTokenResponse);
      jest.spyOn(tokenHandlers, 'handlePassword').mockResolvedValue(mockTokenResponse);
      jest.spyOn(tokenHandlers, 'handleAuthorizationCode').mockResolvedValue(mockTokenResponse);
    });

    it('should call handleRefreshToken for refresh_token grant type', async () => {
      const data: OAuthTokenDTO = {
        grant_type: EOAuthGrantType.REFRESH_TOKEN,
        refresh_token: 'valid-refresh-token',
        client_id: 'test-client-id',
        client_secret: 'test-client-secret',
      };

      const client: IAppOauthClient = {
        appId: 1,
        orgEnvId: 'test-org-env-id',
        clientId: 'test-client-id',
      };

      const orgEnv = { id: 'test-org-env-id' } as OrgEnv;

      const result = await controller.token(data, client, orgEnv);

      expect(tokenHandlers.handleRefreshToken).toHaveBeenCalledWith(data, client);
      expect(result).toEqual(mockTokenResponse);
    });

    it('should call handleClientCredentials for client_credentials grant type', async () => {
      const data: OAuthTokenDTO = {
        grant_type: EOAuthGrantType.CLIENT_CREDENTIALS,
        client_id: 'test-client-id',
        client_secret: 'test-client-secret',
        scope: EOAuthScope.APP,
      };

      const client: IAppOauthClient = {
        appId: 1,
        orgEnvId: 'test-org-env-id',
        clientId: 'test-client-id',
      };

      const orgEnv = { id: 'test-org-env-id' } as OrgEnv;

      const result = await controller.token(data, client, orgEnv);

      expect(tokenHandlers.handleClientCredentials).toHaveBeenCalledWith(data, client);
      expect(result).toEqual(mockTokenResponse);
    });

    it('should call handlePassword for password grant type', async () => {
      const data: OAuthTokenDTO = {
        grant_type: EOAuthGrantType.PASSWORD,
        client_id: 'test-client-id',
        client_secret: 'test-client-secret',
        username: 'testuser',
        password: 'password123',
      };

      const client: IAppOauthClient = {
        appId: 1,
        orgEnvId: 'test-org-env-id',
        clientId: 'test-client-id',
      };

      const orgEnv = { id: 'test-org-env-id' } as OrgEnv;

      const result = await controller.token(data, client, orgEnv);

      expect(tokenHandlers.handlePassword).toHaveBeenCalledWith(data, client);
      expect(result).toEqual(mockTokenResponse);
    });

    it('should call handleAuthorizationCode for authorization_code grant type', async () => {
      const data: OAuthTokenDTO = {
        grant_type: EOAuthGrantType.AUTHORIZATION_CODE,
        client_id: 'test-client-id',
        client_secret: 'test-client-secret',
        code: 'valid-code',
        redirect_uri: 'https://example.com/callback',
      };

      const client: IAppOauthClient = {
        appId: 1,
        orgEnvId: 'test-org-env-id',
        clientId: 'test-client-id',
      };

      const orgEnv = { id: 'test-org-env-id' } as OrgEnv;

      const result = await controller.token(data, client, orgEnv);

      expect(tokenHandlers.handleAuthorizationCode).toHaveBeenCalledWith(data, client, orgEnv);
      expect(result).toEqual(mockTokenResponse);
    });

    it('should throw BadRequestException for invalid grant_type', async () => {
      const data: OAuthTokenDTO = {
        grant_type: 'invalid_grant_type' as EOAuthGrantType,
        client_id: 'test-client-id',
        client_secret: 'test-client-secret',
      };

      const client: IAppOauthClient = {
        appId: 1,
        orgEnvId: 'test-org-env-id',
        clientId: 'test-client-id',
      };

      const orgEnv = { id: 'test-org-env-id' } as OrgEnv;

      await expect(controller.token(data, client, orgEnv)).rejects.toThrow(
        new BadRequestException('Invalid grant_type parameter or parameter missing'),
      );
    });
  });

  describe('authorise', () => {
    const mockJwt = {
      clientId: 'test-client-id',
      scope: EOAuthScope.USER,
      appId: 123,
      userId: 456,
    } as TJWT;

    const mockOrgEnv = {
      id: 'test-org-env-id',
    } as OrgEnv;

    const mockAuthCode = 'generated-auth-code';

    const mockPayload = {
      client_id: 'test-client-id',
      redirect_uri: 'https://test.com/callback',
    } as OAuthAuthorisePayloadDTO;

    beforeEach(() => {
      jest.spyOn(userService, 'userActivatedForApp').mockResolvedValue(true);
      jest.spyOn(service, 'getAuthCode').mockResolvedValue(mockAuthCode);
    });

    it('should return authorization code response when all validations pass', async () => {
      const result = await controller.authorise(mockPayload, mockJwt, mockOrgEnv, 'query-state');

      expect(result).toEqual({
        code: mockAuthCode,
        redirectUri: mockPayload.redirect_uri,
        state: 'query-state',
      });
    });

    it('should use state from payload over query state', async () => {
      const result = await controller.authorise(
        { ...mockPayload, state: 'payload-state' },
        mockJwt,
        mockOrgEnv,
        'query-state',
      );

      expect(result.state).toBe('payload-state');
    });

    it('should throw ForbiddenException when client IDs do not match', async () => {
      await expect(
        controller.authorise({ ...mockPayload, client_id: 'different-client' }, mockJwt, mockOrgEnv, 'query-state'),
      ).rejects.toThrow(new ForbiddenException('Client ID mismatch between JWT and request'));
    });

    it('should throw ForbiddenException when scope is not user', async () => {
      const jwtWithWrongScope = { ...mockJwt, scope: EOAuthScope.APP } as TJWT;

      await expect(controller.authorise(mockPayload, jwtWithWrongScope, mockOrgEnv, 'query-state')).rejects.toThrow(
        new ForbiddenException('Token provided must be of scope "user"'),
      );
    });

    it('should throw ForbiddenException when appId is missing', async () => {
      const jwtWithoutAppId = { ...mockJwt, appId: undefined } as TJWT;

      await expect(controller.authorise(mockPayload, jwtWithoutAppId, mockOrgEnv, 'query-state')).rejects.toThrow(
        new ForbiddenException('Provided auth code bearer token when user token expected'),
      );
    });

    it('should throw ForbiddenException when user is not activated for app', async () => {
      jest.spyOn(userService, 'userActivatedForApp').mockResolvedValue(false);

      await expect(controller.authorise(mockPayload, mockJwt, mockOrgEnv, 'query-state')).rejects.toThrow(
        new ForbiddenException('User is not activated for this app'),
      );

      expect(userService.userActivatedForApp).toHaveBeenCalledWith(mockJwt.userId, mockJwt.appId, mockOrgEnv.id);
    });
  });
});
