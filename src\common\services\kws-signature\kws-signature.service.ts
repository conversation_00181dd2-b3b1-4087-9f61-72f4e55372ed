import { Injectable, NotFoundException } from '@nestjs/common';
import { createHmac } from 'node:crypto';

import { KwsSignatureServiceSignatureFormatError } from './types';
import {
  endpointTriggeredWebhookNames,
  EndpointTriggeredWebhookNames,
  WebhookNames,
  OrgLevelWebhookPayload,
} from '../../../webhook/types';
import { CallbackService } from '../callback/callback.service';

@Injectable()
export class KwsSignatureService {
  constructor(private readonly callbackService: CallbackService) {}

  async verify(signature: string, body: OrgLevelWebhookPayload, orgEnvId: string): Promise<boolean> {
    const { name } = body;

    if (!this.isEndpointTriggeredWebhookName(name)) {
      throw new NotFoundException(
        'Unsupported webhook name. Unable to verify signature. ' +
          'Available webhook names: ' +
          endpointTriggeredWebhookNames.join(', '),
      );
    }
    const secret = await this.getSecretForWebhook(name, orgEnvId);

    const { timestamp, sourceSignature } = this.extractTimestampAndSignature(signature);

    const generatedSignature = this.generateSignature(timestamp, JSON.stringify(body), secret);
    return sourceSignature === generatedSignature;
  }

  private async getSecretForWebhook(webhookName: string, orgEnvId: string) {
    // All webhooks are expected to be configured at the org level on FreeKWS
    const webhook = await this.callbackService.getWebhookAtOrgLevel(webhookName, orgEnvId);

    if (webhook?.secrets?.length === 0) {
      throw new NotFoundException('No secrets for webhook not found. Unable to verify signature.');
    }
    const secret = webhook.secrets[0].value;
    if (!secret) {
      throw new NotFoundException('Secret for webhook not found. Unable to verify signature.');
    }
    return secret;
  }

  isEndpointTriggeredWebhookName(name: WebhookNames) {
    return endpointTriggeredWebhookNames.includes(name as EndpointTriggeredWebhookNames);
  }

  private extractTimestampAndSignature(input: string): { timestamp: number; sourceSignature: string } {
    const timeStampAndSignatureCapturePattern = /t=(\d+),v1=([\da-f]+)/i;
    const match = input.match(timeStampAndSignatureCapturePattern);

    if (!match) {
      throw new KwsSignatureServiceSignatureFormatError();
    }

    const [, timestampString, sig] = match;
    const timestamp: number = +timestampString;
    return { timestamp, sourceSignature: sig };
  }

  private generateSignature(timestamp: number, body: string, secretKey: string): string {
    const dataToSign = `${timestamp}.${body}`;
    return createHmac('sha256', secretKey).update(dataToSign).digest('hex');
  }
}
