import { FastifyRequest, RawRequestDefaultExpression, RawServerDefault, RouteGenericInterface } from 'fastify';

import { OrgLevelWebhookPayload } from '../../../webhook/types';

export type WebhookFastifyRequest = FastifyRequest<
  RouteGenericInterface & {
    Headers: { 'x-kws-signature': string };
    Params: object & { orgEnvId: string };
    Body: OrgLevelWebhookPayload;
  },
  RawServerDefault,
  RawRequestDefaultExpression
>;
