import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { JwkModule } from './jwk.module';
import { OAuthTokenHandlers } from './oauth-token-handlers';
import { <PERSON>authController } from './oauth.controller';
import { OauthService } from './oauth.service';
import { RefreshToken } from './refresh-token.entity';
import { App } from '../app/app.entity';
import { AppModule } from '../app/app.module';
import { CommonModule } from '../common/common.module';
import { AgeGateModule } from '../common/services/age-gate/age-gate.module';
import { SettingsModule } from '../common/services/settings/settings.module';
import { OrgEnv } from '../org-env/org-env.entity';
import { OrgEnvModule } from '../org-env/org-env.module';
import { Activation } from '../user/activation.entity';
import { User } from '../user/user.entity';
import { UserModule } from '../user/user.module';

@Module({
  imports: [
    AppModule,
    JwkModule,
    OrgEnvModule,
    TypeOrmModule.forFeature([RefreshToken, User, App, OrgEnv, Activation]),
    UserModule,
    SettingsModule,
    AgeGateModule,
    CommonModule,
  ],
  controllers: [OauthController],
  providers: [OauthService, OAuthTokenHandlers],
})
export class OauthModule {}
