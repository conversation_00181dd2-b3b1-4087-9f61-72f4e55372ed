import { createParamDecorator, ExecutionContext, SetMetadata } from '@nestjs/common';

import { OauthFastifyRequest } from './types';
import { IAppOauthClient } from '../../../app/types';

export const CLIENT_OAUTH_TOKEN = 'freekws/inject-client-oauth';

export const UseClientOauth = () => SetMetadata(CLIENT_OAUTH_TOKEN, true);

export const ClientOauth = createParamDecorator((_data: void, ctx: ExecutionContext): IAppOauthClient => {
  const request = ctx.switchToHttp().getRequest<OauthFastifyRequest>();
  return request.raw.oauthClient;
});
