import { MigrationInterface, QueryRunner } from "typeorm";

export class MakeOrgEnvRemainingConstraints1739199154693 implements MigrationInterface {
    name = 'MakeOrgEnvRemainingConstraints1739199154693'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "jwk"
                RENAME COLUMN "algorythm" TO "algorithm"
        `);
        await queryRunner.query(`
            ALTER TABLE "jwk" DROP CONSTRAINT "PK_b8bf5700bbb0af854f57892382e"
        `);
        await queryRunner.query(`
            ALTER TABLE "jwk"
            ADD CONSTRAINT "PK_f7e463dc212c93c426852157839" PRIMARY KEY ("id", "orgEnvId")
        `);
        await queryRunner.query(`
            ALTER TABLE "jwk" DROP CONSTRAINT "FK_94f6862e99cb2f6b181e65c0a1d"
        `);
        await queryRunner.query(`
            ALTER TABLE "jwk"
            ALTER COLUMN "orgEnvId"
            SET NOT NULL
        `);
        await queryRunner.query(`
            ALTER TABLE "jwk"
            ADD CONSTRAINT "FK_94f6862e99cb2f6b181e65c0a1d" FOREIGN KEY ("orgEnvId") REFERENCES "org_env"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "jwk" DROP CONSTRAINT "FK_94f6862e99cb2f6b181e65c0a1d"
        `);
        await queryRunner.query(`
            ALTER TABLE "jwk"
            ALTER COLUMN "orgEnvId" DROP NOT NULL
        `);
        await queryRunner.query(`
            ALTER TABLE "jwk"
            ADD CONSTRAINT "FK_94f6862e99cb2f6b181e65c0a1d" FOREIGN KEY ("orgEnvId") REFERENCES "org_env"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "jwk" DROP CONSTRAINT "PK_f7e463dc212c93c426852157839"
        `);
        await queryRunner.query(`
            ALTER TABLE "jwk"
            ADD CONSTRAINT "PK_b8bf5700bbb0af854f57892382e" PRIMARY KEY ("id")
        `);
    }

}
