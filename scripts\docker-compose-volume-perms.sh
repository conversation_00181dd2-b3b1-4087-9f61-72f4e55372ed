#!/bin/bash -ex

set -o pipefail
if ! VOLUMES=$(yq e '.services[].volumes[]' ${COMPOSE_FILE:-./docker-compose.yml} | grep -o -- '^\./.*' | cut -d' ' -f2 | cut -d':' -f1); then
  VOLUMES=$(yq r ${COMPOSE_FILE:-./docker-compose.yml} 'services[*].volumes[*]' | grep -o -- '- ./.*' | cut -d' ' -f2 | cut -d':' -f1)
fi

if [ -n "${VOLUMES}" ]; then

  # Remove ./test/redis.conf from the volumes list if it exists
  VOLUMES=$(echo "${VOLUMES}" | tr ' ' '\n' | grep -v "^./test/redis.conf$" | tr '\n' ' ')

  if [ -n "${VOLUMES}" ]; then
    echo "Changing permissions on volumes: ${VOLUMES}"
    mkdir -p ${VOLUMES}
    chmod -R a+rw ${VOLUMES}
  fi

fi