import { Injectable } from '@nestjs/common';
import { <PERSON><PERSON>, AuditSubscriber } from '@superawesome/freekws-nestjs-audit';
import config from 'config';
import { shuffle } from 'lodash';
import { DataSourceOptions } from 'typeorm';

import type {
  TAppConfig,
  TAppHttpConfig,
  TCircuitManagerOptions,
  TDbConfig,
  TIntervalHealthCheck,
  TServiceConfig,
  TKeycloakConfig,
  TCacheableConfig,
  TKafkaConfig,
  TAnalyticConfig,
  TAppWebhooksConfig,
  TEncryptionConfig,
  TEmailServiceConfig,
  TLinksConfig,
  TCommonApiClientConfig,
} from './types';

@Injectable()
export class ConfigService {
  config: TAppConfig;
  constructor() {
    this.config = this.getConfig();
  }

  getConfig() {
    return {
      http: this.buildHttpConfig(),
      typeormConfig: this.buildTypeormConfig(),
      intervalHealthCheck: this.buildHealthcheckConfig(),
      ageGateCircuitBreakerConfig: this.buildCircuitBreakerConfig('ageGateCircuitBreakerConfig'),
      ageGateService: this.buildServiceConfig('ageGateService'),
      keycloak: this.buildKeycloakConfig(),
      cacheable: this.buildCacheableConfig(),
      kafka: this.buildKafkaConfig(),
      analytic: this.buildAnalyticConfig(),
      settingsService: this.buildServiceConfig('settingsBackend'),
      settingsServiceCircuitBreakerConfig: this.buildCircuitBreakerConfig('settingsBackendCircuitBreakerConfig'),
      familyService: this.buildServiceConfig('familyService'),
      familyServiceCircuitBreakerConfig: this.buildCircuitBreakerConfig('familyServiceCircuitBreakerConfig'),
      preVerificationService: this.buildServiceConfig('preVerificationService'),
      preVerificationServiceCircuitBreakerConfig: this.buildCircuitBreakerConfig(
        'preVerificationServiceCircuitBreakerConfig',
      ),
      appWebhooks: this.buildAppWebhooksConfig(),
      encryption: this.buildEncryptionConfig(),
      devPortalService: this.buildServiceConfig('devPortalBackend'),
      devPortalServiceCircuitBreakerConfig: this.buildCircuitBreakerConfig('devPortalBackendCircuitBreakerConfig'),
      callbackService: this.buildServiceConfig('callbackBackend'),
      callbackServiceCircuitBreakerConfig: this.buildCircuitBreakerConfig('callbackServiceCircuitBreakerConfig'),
      badger: {
        apiKey: config.get<string>('badger.apiKey'),
        baseURL: config.get<string>('badger.baseURL'),
      },
      talon: {
        apiKey: config.get<string>('talon.apiKey'),
        flowId: config.get<string>('talon.flowId'),
      },
      environment: config.get<string>('environment'),
      emailService: this.buildEmailServiceConfig(),
      links: this.buildLinks(),
      brandingApiClient: this.generateCommonApiClientConfig('brandingApiClient'),
      analyticsServiceAuthString: config.get<string>('analyticsService.authString'),
      amplitude: {
        apiKey: config.get<string>('amplitude.apiKey'),
        serverUrl: config.get<string>('amplitude.serverUrl'),
      },
    };
  }

  getHealthcheckConfig(): TIntervalHealthCheck {
    return this.config.intervalHealthCheck;
  }

  getHttpConfig(): TAppHttpConfig {
    return this.config.http;
  }

  getTypeormConfig(): DataSourceOptions {
    return this.config.typeormConfig;
  }

  getKeycloak(): TKeycloakConfig {
    return this.config.keycloak;
  }

  getCallbackApiClient() {
    return this.config.callbackService;
  }

  getCallbackApiCircuitBreakerConfig() {
    return this.config.callbackServiceCircuitBreakerConfig;
  }

  getAgeGateCircuitBreakerConfig(): TCircuitManagerOptions {
    return this.config.ageGateCircuitBreakerConfig;
  }

  getAgeGateService(): TServiceConfig {
    return this.config.ageGateService;
  }

  getCacheable(): TCacheableConfig {
    return this.config.cacheable;
  }

  getKafka(): TKafkaConfig {
    return this.config.kafka;
  }

  getAnalytic(): TAnalyticConfig {
    return this.config.analytic;
  }

  getSettingsService(): TServiceConfig {
    return this.config.settingsService;
  }

  getSettingsServiceCircuitBreakerConfig(): TCircuitManagerOptions {
    return this.config.settingsServiceCircuitBreakerConfig;
  }

  getFamilyService(): TServiceConfig {
    return this.config.familyService;
  }

  getFamilyServiceCircuitBreakerConfig(): TCircuitManagerOptions {
    return this.config.familyServiceCircuitBreakerConfig;
  }

  getPreVerificationService(): TServiceConfig {
    return this.config.preVerificationService;
  }

  getPreVerificationServiceCircuitBreakerConfig(): TCircuitManagerOptions {
    return this.config.preVerificationServiceCircuitBreakerConfig;
  }

  getAppWebhooksConfig(): TAppWebhooksConfig {
    return this.config.appWebhooks;
  }

  getEncryptionConfig(): TEncryptionConfig {
    return this.config.encryption;
  }

  getDevPortalService(): TServiceConfig {
    return this.config.devPortalService;
  }

  getDevPortalServiceCircuitBreakerConfig(): TCircuitManagerOptions {
    return this.config.devPortalServiceCircuitBreakerConfig;
  }

  getEmailService(): TEmailServiceConfig {
    return this.config.emailService;
  }

  getLinks(): TLinksConfig {
    return this.config.links;
  }

  getBrandingApiClient(): TCommonApiClientConfig {
    return this.config.brandingApiClient;
  }

  private buildHttpConfig(): TAppHttpConfig {
    return {
      port: Number(config.get<string>('http.port')),
      cors: {
        origin: config.get('http.cors.origin'),
      },
    };
  }

  private buildTypeormConfig(): DataSourceOptions {
    const { masterUrl, slaveUrls, maxConnectionLimit, ...origConfig } = config.util.toObject(
      config.get<TDbConfig>('database'),
    );
    return {
      ...origConfig,
      replication: {
        master: {
          url: masterUrl,
        },
        slaves: [{ url: masterUrl }, ...slaveUrls.split(/,/).map((s: string) => ({ url: s }))],
      },
      entities: [...origConfig.entities, Audit],
      subscribers: [...origConfig.subscribers, AuditSubscriber],
      extra: {
        max: Number(maxConnectionLimit), // maximum number of clients the connection pool should contain
      },
      migrationsTransactionMode: 'each',
    };
  }

  private buildHealthcheckConfig(): TIntervalHealthCheck {
    return {
      intervalMs: Number(config.get<string>('intervalHealthCheck.intervalMs')),
    };
  }

  private buildKeycloakConfig(): TKeycloakConfig {
    return {
      realm: config.get('keycloak.realm'),
      clientId: config.get('keycloak.clientId'),
      secret: config.get('keycloak.secret'),
      authServerUrl: config.get('keycloak.authServerUrl'),
      audience: config.get('keycloak.audience'),
      timeoutMs: Number(config.get<string>('keycloak.timeoutMs')),
      expirationTime: Number(config.get<string>('keycloak.expirationTime')),
      realmUrl: config.get<string>('keycloak.realmUrl'),
      additionalTrustedIssuers: config.get<string>('keycloak.additionalTrustedIssuers')
        ? config.get<string>('keycloak.additionalTrustedIssuers').split(/,/)
        : [],
    };
  }

  private buildCircuitBreakerConfig(configName: string): TCircuitManagerOptions {
    return {
      errorThresholdPercentage: Number(config.get(`${configName}.errorThresholdPercentage`)),
      resetTimeoutMs: Number(config.get(`${configName}.resetTimeoutMs`)),
      timeoutMs: Number(config.get(`${configName}.timeoutMs`)),
    };
  }

  private buildServiceConfig(configName: string): TServiceConfig {
    return {
      baseURL: config.get(`${configName}.baseURL`),
      timeoutMs: Number(config.get(`${configName}.timeoutMs`)),
      retries: Number(config.get<string>(`${configName}.retries`)),
      initialRetryDelay: Number(config.get<string>(`${configName}.initialRetryDelay`)),
      bailOnStatus: config.get<string>(`${configName}.bailOnStatus`).split(',').map(Number),
    };
  }

  private buildCacheableConfig(): TCacheableConfig {
    return {
      enabled: config.get<string>('cacheable.enabled') === 'true',
      defaultTtlSecs: Number(config.get<string>('cacheable.defaultTtlSecs')),
      maxCacheSizeEntries: Number(config.get<string>('cacheable.maxCacheSizeEntries')),
      metricsNamespace: config.get<string>('cacheable.metricsNamespace'),
    };
  }

  private buildKafkaConfig(): TKafkaConfig {
    return {
      kafkaHost: shuffle(config.get<string>('kafka.kafkaHost').split(',')).join(','),
      outOfOrderTopic: config.get<string>('kafka.outOfOrderTopic'),
    };
  }

  private buildAnalyticConfig(): TAnalyticConfig {
    return {
      baseURL: config.get<string>('analyticService.baseURL'),
      upstream: 'freekws-analytic-service',
      authorizationHeader: config.get<string>('analyticService.authHeader'),
    };
  }

  private buildAppWebhooksConfig(): TAppWebhooksConfig {
    return {
      topicName: config.get('appWebhooks.topicName'),
      sendWebhooks: config.get('appWebhooks.sendWebhooks') === 'true',
    };
  }

  private buildEncryptionConfig(): TEncryptionConfig {
    return {
      secrets: config.get<string>('encryption.secrets').split(','),
      secretVersion: Number(config.get<string>('encryption.secretVersion')),
    };
  }

  getBadgerConfig() {
    return this.config.badger;
  }

  private buildEmailServiceConfig(): TEmailServiceConfig {
    return {
      kafkaTopic: config.get('emailService.kafkaTopic'),
      footer: {
        logoSrc: config.get('emailService.footer.logoSrc'),
      },
      header: {
        headerSrc: config.get('emailService.header.headerSrc'),
      },
    };
  }

  private buildLinks(): TLinksConfig {
    return {
      kwsFaqLink: config.get('links.kwsFaqLink'),
      kwsPrivacyPolicyLink: config.get('links.kwsPrivacyPolicyLink'),
      kwsTermsLink: config.get('links.kwsTermsLink'),
      kwsHelpCentreLink: config.get('links.kwsHelpCentreLink'),
    };
  }

  private generateCommonApiClientConfig(path: string): TCommonApiClientConfig {
    return {
      baseURL: config.get(`${path}.baseURL`),
      timeoutMs: Number(config.get<string>(`${path}.timeoutMs`)),
      retries: Number(config.get<string>(`${path}.retries`)),
      initalRetryDelay: Number(config.get<string>(`${path}.initalRetryDelay`)),
      bailOnStatus: config.get<string>(`${path}.bailOnStatus`).split(',').map(Number),
    };
  }
}
