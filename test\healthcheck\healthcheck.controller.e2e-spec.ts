import { INestApplication } from '@nestjs/common';
import request from 'supertest';

import { Utils } from '../utils';

describe('HealthcheckController (e2e)', () => {
  let app: INestApplication;

  beforeAll(async () => {
    app = await Utils.createTestServer();
  });

  jest.setTimeout(50000);

  afterAll(async () => {
    await Utils.stopTestServer(app);
    jest.clearAllMocks();
  });

  describe('GET /healthcheck', () => {
    it('should return the expected response', async () => {
      const result = await request(app.getHttpServer()).get('/healthcheck').expect(200);
      Utils.assertApiBody(result.body, {
        name: 'Success',
        message: 'What a day to be alive!',
      });
    });
  });
});
