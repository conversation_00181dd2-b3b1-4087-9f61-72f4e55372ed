# AWS DMS Transformation Rules Builder

## Project Structure

```
.
├── 000_base.json
├── 100_user.json
├── 200_app.json
├── 300_jwk.json
├── 400_activation.json
├── 500_webhook.json
├── 600_refreshToken.json
├── 700_appTranslation.json
├── build.sh
```

## Overview

This project provides a structured approach to building and maintaining AWS Database Migration Service (DMS) transformation rules. It uses a modular file structure and a bash script to combine and customize the rules.

## File Structure

### Base File
- `000_base.json`: Contains the initial structure for the transformation rules.

### Table-Specific Rule Files
- Files named `[100-700]_*.json`: Each file contains rules for a specific table or set of related tables.
- The numeric prefix in the filename corresponds to the range of rule IDs used within that file.

### Build Script
- `build.sh`: Bash script that combines all rule files and applies custom transformations.

### Output File
- `transformation-rules.json`: The final, combined set of transformation rules.

## Why Multiple Files?

1. **Modularity**: Each file focuses on rules for a specific table or set of related tables, making the rules easier to manage and understand.
2. **Maintainability**: Changes to rules for one table don't affect others, reducing the risk of unintended modifications.
3. **Collaboration**: Different team members can work on different sets of rules simultaneously.
4. **Version Control**: Easier to track changes and manage conflicts in version control systems.

## Numeric Prefixes in Filenames

The numeric prefixes (e.g., 100_, 200_) serve multiple purposes:
1. They define the order in which files are processed.
2. They provide a range for rule IDs within each file, helping to avoid ID conflicts.
3. They make it easier to locate rules for specific tables or functionalities.

## Build Script Explanation

The `build.sh` script performs the following operations:

1. Combines all numbered JSON files into a single set of transformation rules.
2. Replaces a placeholder value in the `expression` field of certain rules with a provided value.

### Usage

```
./build.sh "replacement-value"
```

### Placeholder Replacement

- The script defines a `ORGENVID` variable (default: `'org-env-id-value'`).
- During the build process, all occurrences of this placeholder in the `expression` field of transformation rules are replaced with the value provided as an argument to the script.
- This allows for dynamic customization of certain rules without modifying the individual JSON files.

## Maintenance and Updates

1. To add rules for a new table, create a new JSON file with an appropriate numeric prefix.
2. To modify existing rules, edit the corresponding numbered JSON file.
3. To change the placeholder value or its replacement process, modify the `build.sh` script.

## Best Practices

1. Keep rule IDs within the range specified by the file's numeric prefix.
2. Regularly run the build script to ensure all changes are properly integrated.
3. Review the `transformation-rules.json` file after building to verify the results.
