import {
  BadRequestException,
  Body,
  Controller,
  ForbiddenException,
  HttpCode,
  HttpStatus,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiExtraModels, ApiOkResponse, ApiOperation, ApiTags } from '@nestjs/swagger';
import {
  EOAuthScope,
  OAuthAuthorisePayloadDTO,
  OAuthAuthorizationCodeResponseDTO,
  OAuthTokenDTO,
  OAuthTokenResponseDTO,
} from '@superawesome/freekws-classic-wrapper-common';
import { MetricsService } from '@superawesome/freekws-metrics-nestjs-service';
import { Public } from '@superawesome/freekws-nestjs-guards';
import { plainToInstance } from 'class-transformer';

import { JWKService } from './jwk.service';
import { OAuthTokenHandlers } from './oauth-token-handlers';
import { OauthService } from './oauth.service';
import { TJWT } from './types';
import { IAppOauthClient } from '../app/types';
import { ClientOauth, UseClientOauth } from '../common/guards/inject-client-oauth/inject-client-oauth.decorator';
import { InjectClientOauthGuard } from '../common/guards/inject-client-oauth/inject-client-oauth.guard';
import { ExtractOrgEnv } from '../common/guards/inject-org-env/inject-org-env.decorator';
import { InjectOrgEnvGuard } from '../common/guards/inject-org-env/inject-org-env.guard';
import { JWT } from '../common/guards/oauth/jwt.decorator';
import { OauthGuard } from '../common/guards/oauth/oauth.guard';
import { EAPITags } from '../common/types';
import { OrgEnv } from '../org-env/org-env.entity';
import { UserService } from '../user/user.service';

@Controller('oauth')
@ApiTags(EAPITags.Oauth)
@ApiExtraModels(OAuthTokenDTO, OAuthTokenResponseDTO)
export class OauthController {
  constructor(
    private readonly service: OauthService,
    private readonly userService: UserService,
    private readonly tokenHandlers: OAuthTokenHandlers,
    private readonly metricsService: MetricsService,
  ) {}

  @ApiOperation({})
  @ApiTags(EAPITags.Oauth)
  @ApiOkResponse({
    type: OAuthTokenResponseDTO,
  })
  @Post('token')
  @HttpCode(HttpStatus.OK)
  @Public()
  @UseGuards(InjectClientOauthGuard, InjectOrgEnvGuard)
  @UseClientOauth()
  async token(
    @Body() data: OAuthTokenDTO,
    @ClientOauth() client: IAppOauthClient,
    @ExtractOrgEnv() orgEnv: OrgEnv,
  ): Promise<OAuthTokenResponseDTO> {
    this.metricsService.increment('sa.classicwrapper.oauth', 1, [
      `client_id:${client.clientId}`,
      `grant_type:${data.grant_type}`,
    ]);
    switch (data.grant_type) {
      case 'refresh_token': {
        return this.tokenHandlers.handleRefreshToken(data, client);
      }

      case 'client_credentials': {
        return this.tokenHandlers.handleClientCredentials(data, client);
      }

      case 'password': {
        return this.tokenHandlers.handlePassword(data, client);
      }

      case 'authorization_code': {
        return this.tokenHandlers.handleAuthorizationCode(data, client, orgEnv);
      }

      default: {
        throw new BadRequestException('Invalid grant_type parameter or parameter missing');
      }
    }
  }

  @ApiOperation({})
  @ApiTags(EAPITags.Oauth)
  @ApiOkResponse({
    type: OAuthTokenResponseDTO,
  })
  @Post('authorise')
  @HttpCode(HttpStatus.OK)
  @UseGuards(OauthGuard, InjectOrgEnvGuard)
  async authorise(
    @Body() data: OAuthAuthorisePayloadDTO,
    @JWT() jwt: TJWT,
    @ExtractOrgEnv() orgEnv: OrgEnv,
    @Query('state') queryState: string,
  ) {
    if (jwt.clientId !== JWKService.TOKEN_ISSUER && data.client_id !== jwt.clientId) {
      throw new ForbiddenException('Client ID mismatch between JWT and request');
    }

    if (jwt.scope !== EOAuthScope.USER) {
      throw new ForbiddenException('Token provided must be of scope "user"');
    }

    if (!jwt.appId) {
      throw new ForbiddenException('Provided auth code bearer token when user token expected');
    }

    const isActivatedForApp = await this.userService.userActivatedForApp(jwt.userId, jwt.appId, orgEnv.id);
    if (!isActivatedForApp) {
      throw new ForbiddenException('User is not activated for this app');
    }

    const authCode = await this.service.getAuthCode(data, jwt, orgEnv);

    return plainToInstance(OAuthAuthorizationCodeResponseDTO, {
      code: authCode,
      redirectUri: data.redirect_uri,
      state: data.state ?? queryState,
    } as OAuthAuthorizationCodeResponseDTO);
  }
}
