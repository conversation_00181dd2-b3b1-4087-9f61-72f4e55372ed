[{"rule-type": "selection", "rule-id": "200", "rule-name": "Include apps table", "object-locator": {"schema-name": "public", "table-name": "apps"}, "rule-action": "include", "filters": [{"filter-type": "source", "column-name": "deletedAt", "filter-conditions": [{"filter-operator": "null"}]}, {"filter-type": "source", "column-name": "productId", "filter-conditions": [{"filter-operator": "notnull"}]}, {"filter-type": "source", "column-name": "productEnvId", "filter-conditions": [{"filter-operator": "notnull"}]}]}, {"rule-type": "transformation", "rule-id": "201", "rule-name": "Rename apps to app", "rule-action": "rename", "rule-target": "table", "object-locator": {"schema-name": "public", "table-name": "apps"}, "value": "app"}, {"rule-type": "transformation", "rule-id": "202", "rule-name": "Add orgEnvId column to apps", "rule-target": "column", "object-locator": {"schema-name": "public", "table-name": "apps"}, "rule-action": "add-column", "value": "orgEnvId", "expression": "'org-env-id-value'", "data-type": {"type": "string", "length": 255}}, {"rule-type": "transformation", "rule-id": "210", "rule-name": "Remove isPrimary column from apps", "rule-action": "remove-column", "rule-target": "column", "object-locator": {"schema-name": "public", "table-name": "apps", "column-name": "isPrimary"}}, {"rule-type": "transformation", "rule-id": "211", "rule-name": "Remove verificationRequired column from apps", "rule-action": "remove-column", "rule-target": "column", "object-locator": {"schema-name": "public", "table-name": "apps", "column-name": "verificationRequired"}}, {"rule-type": "transformation", "rule-id": "212", "rule-name": "Remove mainContainerBgColor column from apps", "rule-action": "remove-column", "rule-target": "column", "object-locator": {"schema-name": "public", "table-name": "apps", "column-name": "mainContainerBgColor"}}, {"rule-type": "transformation", "rule-id": "213", "rule-name": "Remove oauthImplicitEnabled column from apps", "rule-action": "remove-column", "rule-target": "column", "object-locator": {"schema-name": "public", "table-name": "apps", "column-name": "oauthImplicitEnabled"}}, {"rule-type": "transformation", "rule-id": "214", "rule-name": "Remove parentEmailRequired column from apps", "rule-action": "remove-column", "rule-target": "column", "object-locator": {"schema-name": "public", "table-name": "apps", "column-name": "parentEmailRequired"}}, {"rule-type": "transformation", "rule-id": "216", "rule-name": "Remove areAnonAccountsEnabled column from apps", "rule-action": "remove-column", "rule-target": "column", "object-locator": {"schema-name": "public", "table-name": "apps", "column-name": "areAnonAccountsEnabled"}}, {"rule-type": "transformation", "rule-id": "218", "rule-name": "Remove displayNameRequired column from apps", "rule-action": "remove-column", "rule-target": "column", "object-locator": {"schema-name": "public", "table-name": "apps", "column-name": "displayNameRequired"}}, {"rule-type": "transformation", "rule-id": "220", "rule-name": "Remove rotatedApi<PERSON>ey column from apps", "rule-action": "remove-column", "rule-target": "column", "object-locator": {"schema-name": "public", "table-name": "apps", "column-name": "rotated<PERSON><PERSON><PERSON><PERSON>"}}]