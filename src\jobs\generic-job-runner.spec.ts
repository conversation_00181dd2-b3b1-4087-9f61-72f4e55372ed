import { NestApplication } from '@nestjs/core';

import { GenericJobRunner } from './generic-job-runner';

jest.mock('./delete-old-token-refresh-entries/delete-old-token-refresh-entries');

describe('GenericJobRunner', () => {
  let job: GenericJobRunner;
  const mockApp: NestApplication = {} as NestApplication;

  beforeEach(async () => {
    job = new GenericJobRunner(mockApp);
  });

  it('should throw when jobType does not exist', async () => {
    await expect(job.runJob('nothing', false)).rejects.toThrow(`No job with that name present: nothing`);
  });
});
