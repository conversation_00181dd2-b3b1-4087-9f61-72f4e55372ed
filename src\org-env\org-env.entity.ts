import { Column, CreateDateColumn, Entity, Index, OneToMany, PrimaryColumn, UpdateDateColumn } from 'typeorm';

import { App } from '../app/app.entity';
import { JWK } from '../oauth/jwk.entity';
import { User } from '../user/user.entity';
import { Webhook } from '../webhook/webhook.entity';

@Entity()
export class OrgEnv {
  @PrimaryColumn()
  id: string;

  @Column({ unique: true })
  orgId: string;

  @Column()
  clientId: string;

  @Column()
  clientSecret: string;

  @Index()
  @Column()
  host: string;

  @OneToMany(() => App, (app) => app.orgEnv)
  apps: App[];

  @OneToMany(() => User, (user) => user.orgEnv)
  users: User[];

  @OneToMany(() => Webhook, (webhook) => webhook.orgEnv)
  webhooks: Webhook[];

  @OneToMany(() => JWK, (jwk) => jwk.orgEnv)
  jwks: JWK[];

  @CreateDateColumn()
  createdAt?: Date;

  @UpdateDateColumn()
  updatedAt?: Date;
}
