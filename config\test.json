{"http": {"cors": {"origin": "https://cors-origin.com"}}, "database": {"masterUrl": "***************************************/postgres", "slaveUrls": "***************************************/postgres"}, "keycloak": {"secret": "c3a5909b-5434-52af-9411-8099a2dac73c", "authServerUrl": "http://keycloak:8080/auth"}, "kafka": {"kafkaHost": "brokerhost1.test:9999,brokerhost2.test:9999", "outOfOrderTopic": "out-of-order-topic"}, "appWebhooks": {"sendWebhooks": "true"}, "ageGateCircuitBreakerConfig": {"timeoutMs": "60000", "errorThresholdPercentage": "100", "resetTimeoutMs": "1"}, "settingsBackendCircuitBreakerConfig": {"timeoutMs": "60000", "errorThresholdPercentage": "100", "resetTimeoutMs": "1"}, "familyServiceCircuitBreakerConfig": {"timeoutMs": "60000", "errorThresholdPercentage": "100", "resetTimeoutMs": "1"}, "preVerificationServiceCircuitBreakerConfig": {"timeoutMs": "60000", "errorThresholdPercentage": "100", "resetTimeoutMs": "1"}, "devPortalBackendCircuitBreakerConfig": {"timeoutMs": "60000", "errorThresholdPercentage": "100", "resetTimeoutMs": "1"}, "callbackServiceCircuitBreakerConfig": {"timeoutMs": "60000", "errorThresholdPercentage": "100", "resetTimeoutMs": "1"}, "emailService": {"footer": {"logoSrc": "test.jpg"}, "header": {"headerSrc": "test.jpg"}, "kafkaTopic": "test-email-topic"}, "brandingApiClient": {"baseURL": "https://branding.api.client", "timeoutMs": 500, "retries": 0, "initalRetryDelay": 0, "bailOnStatus": "400,401,403,404"}, "analyticsService": {"authString": "SomeTestAuthString"}, "amplitude": {"apiKey": "amplitude-api-key", "serverUrl": "https://amplitude-server-url.com"}}