import { Abstract, InjectionToken, ModuleMetadata, Type } from '@nestjs/common';
import { Test } from '@nestjs/testing';
import { KeycloakService } from '@superawesome/freekws-auth-library';
import { ECacheServiceProviders, LocalLruCacheService } from '@superawesome/freekws-cache-decorator';
import { Producer } from '@superawesome/freekws-kafka';
import KeycloakConnect from 'keycloak-connect';
import { Repository } from 'typeorm';

import { InjectClientOauthGuard } from '../../guards/inject-client-oauth/inject-client-oauth.guard';
import { InjectOrgEnvGuard } from '../../guards/inject-org-env/inject-org-env.guard';
import { OauthGuard } from '../../guards/oauth/oauth.guard';
import { WebhookGuard } from '../../guards/webhook/webhook.guard';
import { ConfigService } from '../../services/config/config.service';
import { KAFKA_PRODUCER } from '../../services/kafka/kafka-producer.module';
import { KEYCLOAK_CONFIG, KEYCLOAK_INSTANCE, KEYCLOAK_PROVIDER } from '../../services/keycloak/keycloak.module';

export class Testing {
  private static readonly config = new ConfigService();

  static async createModule(metadata: ModuleMetadata) {
    const module = await Test.createTestingModule(metadata)
      .useMocker((token: InjectionToken) => {
        switch (token) {
          case ConfigService:
          case ECacheServiceProviders.Config: {
            return Testing.config;
          }
          case ECacheServiceProviders.Metric: {
            return {
              metrics: {
                increment: jest.fn(),
                gauge: jest.fn(),
              },
              increment: jest.fn(),
            };
          }
          case ECacheServiceProviders.Cache: {
            return new LocalLruCacheService(Testing.config);
          }
          case KAFKA_PRODUCER: {
            return this.getClassStub(Producer);
          }
          case KEYCLOAK_PROVIDER: {
            return this.getClassStub(KeycloakService);
          }
          case KEYCLOAK_CONFIG: {
            return Testing.config;
          }
          case KEYCLOAK_INSTANCE: {
            const keycloakStub = this.getClassStub(KeycloakConnect);
            jest.spyOn(keycloakStub, 'getConfig').mockReturnValue(Testing.config.getKeycloak());
            return keycloakStub;
          }
        }

        if (
          typeof token === 'string' || typeof token === 'symbol'
            ? token.toString().endsWith('Repository')
            : token.name.endsWith('Repository')
        ) {
          return Testing.getClassStub(Repository);
        }

        if (typeof token === 'function') {
          return Testing.getClassStub(token);
        }
      })
      .overrideGuard(InjectClientOauthGuard)
      .useValue({ canActivate: () => true })
      .overrideGuard(InjectOrgEnvGuard)
      .useValue({ canActivate: () => true })
      .overrideGuard(OauthGuard)
      .useValue({ canActivate: () => true })
      .overrideGuard(WebhookGuard)
      .useValue({ canActivate: () => true })
      .compile();

    return module;
  }

  private static getClassStub(cls: Type | Abstract<unknown>) {
    return Object.getOwnPropertyNames(cls.prototype).reduce(
      (acc, key) => ({ ...acc, [key]: () => null }),
      {} as Record<string, () => unknown>,
    );
  }
}
