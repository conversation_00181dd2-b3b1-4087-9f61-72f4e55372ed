import { MigrationInterface, QueryRunner } from 'typeorm';

export class DeletedAtForUsersAndActivations1738327839555 implements MigrationInterface {
    name = 'DeletedAtForUsersAndActivations1738327839555';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "user"
                ADD "deletedAt" TIMESTAMP
        `);

        await queryRunner.query(`
            ALTER TABLE "activation"
                ADD "deletedAt" TIMESTAMP
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "activation"
                DROP COLUMN "deletedAt"
        `);

        await queryRunner.query(`
            ALTER TABLE "user"
                DROP COLUMN "deletedAt"
        `);
    }
}