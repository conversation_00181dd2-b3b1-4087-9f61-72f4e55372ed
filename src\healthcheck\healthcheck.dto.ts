import { ApiProperty } from '@nestjs/swagger';
import { IsString } from 'class-validator';

export class HealthcheckOutputDTO {
  @IsString()
  @ApiProperty({
    example: 'Success',
    description: 'The example will always be the value if the API response successfully',
  })
  name: string;

  @IsString()
  @ApiProperty({
    example: 'What a day to be alive',
    description: 'The example will always be the value if the API response successfully',
  })
  message: string;
}
