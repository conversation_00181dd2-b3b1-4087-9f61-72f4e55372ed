import { ExecutionContext } from '@nestjs/common';

import { InjectOrgEnvGuard } from './inject-org-env.guard';
import { OrgEnvService } from '../../../org-env/org-env.service';

const mockOrgEnvRepo = {
  getOrgEnvFromRequest: jest.fn(),
};

const mockGetRequest = jest.fn();

const context = {
  getHandler: jest.fn(),
  switchToHttp: () => ({
    getRequest: mockGetRequest,
  }),
} as unknown as ExecutionContext;

describe('ClientCredentialsGuard', () => {
  let guard: InjectOrgEnvGuard;

  beforeAll(() => {
    guard = new InjectOrgEnvGuard(mockOrgEnvRepo as unknown as OrgEnvService);
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  it('returns true when getting org env from request', async () => {
    mockGetRequest.mockReturnValue({ headers: {}, raw: {} });

    const result = await guard.canActivate(context);
    expect(result).toBe(true);
    expect(mockOrgEnvRepo.getOrgEnvFromRequest).toHaveBeenCalled();
  });
});
