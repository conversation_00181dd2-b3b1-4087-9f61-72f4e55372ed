import { Injectable, OnModuleDestroy, OnModuleInit } from '@nestjs/common';
import { InjectDataSource } from '@nestjs/typeorm';
import { SALogger } from '@superawesome/freekws-common-logger';
import { DataSource } from 'typeorm';

import { EHealthcheckedResources } from './types';
import { ConfigService } from '../common/services/config/config.service';

@Injectable()
export class HealthcheckService implements OnModuleInit, OnModuleDestroy {
  private healthcheckInterval: NodeJS.Timeout;
  private postgresAccessible = false;

  constructor(
    @InjectDataSource() private readonly dataSource: DataSource,
    private readonly logger: SALogger,
    private readonly configService: ConfigService,
  ) {}

  async onModuleInit() {
    const interval = this.configService.getHealthcheckConfig().intervalMs;
    if (interval > 0) {
      await this.executeIntervalChecks();
      this.healthcheckInterval = setInterval(this.executeIntervalChecks.bind(this), interval);
    } else {
      this.logger.warn(
        'HealthcheckService: Config value intervalHealthCheck.intervalMs set to an invalid value, disabling interval-based healthchecks.',
      );
      this.postgresAccessible = true;
    }
  }

  onModuleDestroy() {
    this.logger.info(`HealthcheckService is shutting down`);
    if (this.healthcheckInterval) {
      clearInterval(this.healthcheckInterval);
    }
  }

  async executeIntervalChecks() {
    await this.isPostgresHealthy();
  }

  isHealthy(): boolean {
    const unhealthy = this.unhealthyResources();
    if (unhealthy.length > 0) {
      this.logger.warn(`Healthcheck failed due to unhealthy resources: ${unhealthy.join(', ')}`);
      return false;
    }
    return true;
  }

  unhealthyResources(): EHealthcheckedResources[] {
    const unhealthy: EHealthcheckedResources[] = [];
    if (!this.postgresAccessible) {
      unhealthy.push(EHealthcheckedResources.POSTGRES);
    }
    return unhealthy;
  }

  async isPostgresHealthy(): Promise<void> {
    try {
      await this.dataSource.manager.connection.query('SELECT 1');
      this.postgresAccessible = true;
    } catch (error) {
      this.logger.error('HealthcheckService.isPostgresHealthy failed to execute', { error });
      this.postgresAccessible = false;
    }
  }
}
