import { BadRequestException, CanActivate, ExecutionContext, Injectable, UnauthorizedException } from '@nestjs/common';
import { ModuleRef, Reflector } from '@nestjs/core';
import { Span } from 'nestjs-ddtrace';

import { OAuthFastifyRequest } from './types';
import { JWKService } from '../../../oauth/jwk.service';
import { OrgEnvService } from '../../../org-env/org-env.service';
import { PoliciesGuard } from '../policies';

@Injectable()
@Span()
export class OauthGuard extends PoliciesGuard implements CanActivate {
  constructor(
    reflector: Reflector,
    moduleRef: ModuleRef,
    private readonly jwkService: JWKService,
    private readonly orgEnvService: OrgEnvService,
  ) {
    super(reflector, moduleRef);
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest<OAuthFastifyRequest>();
    const authHeader = request.headers['authorization'];
    if (!authHeader) {
      throw new BadRequestException('The access token was not found');
    }
    if (!authHeader.startsWith('Bearer')) {
      throw new BadRequestException('Malformed auth header');
    }

    const orgEnvId = await this.orgEnvService.getOrgEnvFromRequest(request);
    try {
      const accessToken = authHeader.slice(7);
      request.raw.jwt = await this.jwkService.verify(accessToken, orgEnvId.id);
    } catch (error) {
      throw new UnauthorizedException('The access token provided is invalid.', { cause: error });
    }

    return super.canActivate(context);
  }
}
