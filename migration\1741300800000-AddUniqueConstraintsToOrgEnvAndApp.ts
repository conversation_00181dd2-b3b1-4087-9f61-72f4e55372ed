import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddUniqueConstraintsToOrgEnvAndApp1741300800000 implements MigrationInterface {
    name = 'AddUniqueConstraintsToOrgEnvAndApp1741300800000';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "org_env"
                ADD CONSTRAINT "UQ_org_env_orgId"
                UNIQUE ("orgId")
        `);

        await queryRunner.query(`
            ALTER TABLE "app"
                ADD CONSTRAINT "UQ_app_orgEnvId_productId"
                UNIQUE ("orgEnvId", "productId")
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "app"
                DROP CONSTRAINT "UQ_app_orgEnvId_productId"
        `);

        await queryRunner.query(`
            ALTER TABLE "org_env"
                DROP CONSTRAINT "UQ_org_env_orgId"
        `);
    }
} 