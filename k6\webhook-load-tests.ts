import { sleep, check } from 'k6';
import {
    webhookChildAccountGraduated,
    webhookSettingsEffectiveValuesChanged,
    webhookUserRemovedFromFamily,
    webhookUserAddedToFamily,
    webhookGuardianRequestExpired,
    webhookFamiliesGroupDeleted
} from './endpoints';
import { webhookPayloads } from './webhook-payloads';
import { Options } from 'k6/options';

/**
 * This test allows for targeting specific webhook endpoints
 * Set the WEBHOOK_TYPE environment variable to one of:
 * - child-graduated
 * - settings-changed
 * - user-removed
 * - user-added
 * - guardian-expired
 * - family-deleted
 * - all (default: tests all endpoints evenly)
 *
 * Example: k6 run -e WEBHOOK_TYPE=child-graduated webhook-specific-test.ts
 */

const webhookType = __ENV.WEBHOOK_TYPE || 'all';

export const options: Options = {
    scenarios: {
        webhook_rps_test: {
            executor: 'ramping-arrival-rate',
            startRate: 0,
            timeUnit: '1s',
            preAllocatedVUs: 500,
            maxVUs: 800,
            // stages: [
            //     { target: 50, duration: '1m' },
            //     { target: 150, duration: '1m' },
            //     { target: 250, duration: '1m' },
            //     { target: 350, duration: '1m' },
            //     { target: 450, duration: '1m' },
            //     { target: 450, duration: '2m' }, // steady
            //     { target: 300, duration: '1m' },
            //     { target: 150, duration: '1m' },
            //     { target: 50, duration: '1m' },
            // ],
            stages: [
                { duration: '1m', target: 10 },
                { duration: '1m', target: 30 },
                { duration: '1m', target: 50 },
                { duration: '1m', target: 75 },
                { duration: '1m', target: 100 },
                { duration: '2m', target: 100 },
                { duration: '1m', target: 60 },
                { duration: '1m', target: 30 },
                { duration: '1m', target: 10 },
            ],
        },
    },
    thresholds: {
        http_req_duration: ['p(95)<500'], // 95% of requests should complete in < 500ms
        http_req_failed: ['rate<0.01'],   // Failures should be under 1%
    },
};

function updateRequestPayload(payload: any) {
    const innerPayload = {
        ...payload,
        payload: {
            ...payload.payload,
            userId: `550385866`
        }
    }
    return {
        ...innerPayload,
        time: Date.now(),
    };
}

export default function () {
    let response;

    let effectiveWebhookType = webhookType;
    if (webhookType === 'all') {
        const allWebhookTypes = ['child-graduated', 'settings-changed', 'user-removed',
            'user-added', 'guardian-expired', 'family-deleted'];
        // Select webhook type based on iteration, ensuring round-robin distribution
        effectiveWebhookType = allWebhookTypes[__ITER % allWebhookTypes.length];
    }

    console.log(`Testing webhook type ${effectiveWebhookType} Iteration ${__ITER}`);

    switch (effectiveWebhookType) {
        case 'child-graduated':
            response = webhookChildAccountGraduated(
                updateRequestPayload(webhookPayloads.childAccountGraduated)
            );
            check(response, {
                'Child Account Graduated webhook returns 204': (r) => r.status === 204,
            });
            break;

        case 'settings-changed':
            response = webhookSettingsEffectiveValuesChanged(
                updateRequestPayload(webhookPayloads.settingsEffectiveValuesChanged)
            );
            check(response, {
                'Settings Effective Values Changed webhook returns 204': (r) => r.status === 204,
            });
            break;

        case 'user-removed':
            response = webhookUserRemovedFromFamily(
                updateRequestPayload(webhookPayloads.userRemovedFromFamily)
            );
            check(response, {
                'User Removed From Family webhook returns 204': (r) => r.status === 204,
            });
            break;

        case 'user-added':
            response = webhookUserAddedToFamily(
                updateRequestPayload(webhookPayloads.userAddedToFamily)
            );
            check(response, {
                'User Added To Family webhook returns 204': (r) => r.status === 204,
            });
            break;

        case 'guardian-expired':
            response = webhookGuardianRequestExpired(
                updateRequestPayload(webhookPayloads.guardianRequestExpired)
            );
            check(response, {
                'Guardian Request Expired webhook returns 204': (r) => r.status === 204,
            });
            break;

        case 'family-deleted':
            response = webhookFamiliesGroupDeleted(
                updateRequestPayload(webhookPayloads.familiesGroupDeleted)
            );
            check(response, {
                'Families Group Deleted webhook returns 204': (r) => r.status === 204,
            });
            break;

        default:
            console.error(`Unknown webhook type: ${effectiveWebhookType}`);
    }

    console.log(response)

    // Add some think time between requests (0.1 to 0.5 seconds)
    sleep(Math.random() * 0.4 + 0.1);
} 