import { MigrationInterface, QueryRunner } from 'typeorm';

export class MakeUserLanguageNullable1741300800002 implements MigrationInterface {
    name = 'MakeUserLanguageNullable1741300800002';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "user"
                ALTER COLUMN "language" DROP NOT NULL,
                ALTER COLUMN "language" SET DEFAULT 'en'
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            UPDATE "user"
            SET "language" = 'en'
            WHERE
                "language" IS NULL
        `);

        await queryRunner.query(`
            ALTER TABLE "user"
                ALTER COLUMN "language" SET NOT NULL,
                ALTER COLUMN "language" SET DEFAULT 'en'
        `);
    }
}
