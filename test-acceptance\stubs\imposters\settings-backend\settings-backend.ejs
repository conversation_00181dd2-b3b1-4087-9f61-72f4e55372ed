{
  "port": 4748,
  "protocol": "http",
  "name": "settings-backend",
  "defaultResponse": {
    "statusCode": 404,
    "body": {
      "text": "Resource Not Found"
    },
    "headers": {
      "content-type": "application/json"
    }
  },
  "stubs": [
      {
        "predicates": [ <% include send-consent-email/predicates.ejs %> ],
        "responses": [ <% include send-consent-email/responses.ejs %> ]
      },
      {
        "predicates": [ <% include get-user-settings/predicates.ejs %> ],
        "responses": [ <% include get-user-settings/responses.ejs %> ]
      },
      {
        "predicates": [ <% include generate-consent-request/predicates.ejs %> ],
        "responses": [ <% include generate-consent-request/responses.ejs %> ]
      }
  ]
}