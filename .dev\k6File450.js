var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
    for (var prop in b || (b = {}))
        if (__hasOwnProp.call(b, prop))
            __defNormalProp(a, prop, b[prop]);
    if (__getOwnPropSymbols)
        for (var prop of __getOwnPropSymbols(b)) {
            if (__propIsEnum.call(b, prop))
                __defNormalProp(a, prop, b[prop]);
        }
    return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));

// webhook-load-tests.ts
import { sleep, check as check2 } from "k6";

// utils.ts
import { check } from "k6";
import crypto from "k6/crypto";
function validateResponse(res, options2 = {
    expectedStatus: 200,
    maxDuration: 500
}) {
    const { expectedStatus, maxDuration } = options2;
    return check(res, {
        [`status is ${expectedStatus}`]: (r) => r.status === expectedStatus,
        [`response time is less than ${maxDuration}ms`]: (r) => r.timings.duration < maxDuration
    });
}
function generateKwsSignature(payload, secret) {
    try {
        const timestamp = Date.now();
        const payloadStr = JSON.stringify(payload);
        const dataToSign = `${timestamp}.${payloadStr}`;
        const hmac = crypto.hmac("sha256", secret, dataToSign, "hex");
        return `t=${timestamp},v1=${hmac}`;
    } catch (error) {
        console.error("Failed to generate KWS signature:", error);
        return "invalid-signature-generation-failed";
    }
}

// endpoints.ts
import http from "k6/http";
var BASE_URL = __ENV.BASE_URL || "http://localhost:3000";
var DEFAULT_HEADERS = {
    "x-forwarded-host": "test.com",
    "Content-Type": "application/json"
};
function webhookChildAccountGraduated(body) {
    const signature = generateKwsSignature(body, "secretsecret");
    const headers = __spreadProps(__spreadValues({}, DEFAULT_HEADERS), {
        "x-kws-signature": signature
    });
    const response = http.post(
        `${BASE_URL}/v1/webhooks/child-account-graduated`,
        JSON.stringify(body),
        { headers }
    );
    validateResponse(response, { expectedStatus: 204, maxDuration: 500 });
    return response;
}
function webhookSettingsEffectiveValuesChanged(body) {
    const signature = generateKwsSignature(body, "secretsecret6");
    const headers = __spreadProps(__spreadValues({}, DEFAULT_HEADERS), {
        "x-kws-signature": signature
    });
    const response = http.post(
        `${BASE_URL}/v1/webhooks/settings-effective-values-changed`,
        JSON.stringify(body),
        { headers }
    );
    validateResponse(response, { expectedStatus: 204, maxDuration: 500 });
    return response;
}
function webhookUserRemovedFromFamily(body) {
    const signature = generateKwsSignature(body, "secretsecret4");
    const headers = __spreadProps(__spreadValues({}, DEFAULT_HEADERS), {
        "x-kws-signature": signature
    });
    const response = http.post(
        `${BASE_URL}/v1/webhooks/user-removed-from-family`,
        JSON.stringify(body),
        { headers }
    );
    validateResponse(response, { expectedStatus: 204, maxDuration: 500 });
    return response;
}
function webhookUserAddedToFamily(body) {
    const signature = generateKwsSignature(body, "secretsecret3");
    const headers = __spreadProps(__spreadValues({}, DEFAULT_HEADERS), {
        "x-kws-signature": signature
    });
    const response = http.post(
        `${BASE_URL}/v1/webhooks/user-added-to-family`,
        JSON.stringify(body),
        { headers }
    );
    validateResponse(response, { expectedStatus: 204, maxDuration: 500 });
    return response;
}
function webhookGuardianRequestExpired(body) {
    const signature = generateKwsSignature(body, "secretsecret7");
    const headers = __spreadProps(__spreadValues({}, DEFAULT_HEADERS), {
        "x-kws-signature": signature
    });
    const response = http.post(
        `${BASE_URL}/v1/webhooks/guardian-request-expired`,
        JSON.stringify(body),
        { headers }
    );
    validateResponse(response, { expectedStatus: 204, maxDuration: 500 });
    return response;
}
function webhookFamiliesGroupDeleted(body) {
    const signature = generateKwsSignature(body, "secretsecret");
    const headers = __spreadProps(__spreadValues({}, DEFAULT_HEADERS), {
        "x-kws-signature": signature
    });
    const response = http.post(
        `${BASE_URL}/v1/webhooks/families-group-deleted`,
        JSON.stringify(body),
        { headers }
    );
    validateResponse(response, { expectedStatus: 204, maxDuration: 500 });
    return response;
}

// webhook-payloads.ts
var optOutDefinition = {
    ageBracket: {
        consentType: "opt-out"
    }
};
var optInDefinition = {
    ageBracket: {
        consentType: "opt-in-verified"
    }
};
var commonWebhookData = {
    time: Date.now(),
    orgId: "9d14b0c5-fa1b-4590-8def-3f881ee021b7",
    productId: "48de3e54-33a8-499c-955a-4fd5070e79ab"
};
var childAccountGraduatedPayload = __spreadProps(__spreadValues({}, commonWebhookData), {
    name: "settings:user-graduated",
    payload: {
        userId: "1234567"
    }
});
var settingsEffectiveValuesChangedPayload = __spreadProps(__spreadValues({}, commonWebhookData), {
    name: "settings:effective-values-changed",
    payload: {
        userId: "2345678",
        settings: [
            {
                namespace: "default",
                settingName: "display-name",
                effectiveValue: true,
                definition: optOutDefinition
            },
            {
                namespace: "Stranger",
                settingName: "things",
                effectiveValue: true,
                parentLimitedUpdatedAt: (/* @__PURE__ */ new Date()).toISOString(),
                definition: optOutDefinition
            },
            {
                namespace: "default",
                settingName: "complicated",
                effectiveValue: false,
                definition: optInDefinition
            }
        ]
    }
});
var userRemovedFromFamilyPayload = __spreadProps(__spreadValues({}, commonWebhookData), {
    name: "families:user-removed-from-family",
    payload: {
        userId: "3456789"
    }
});
var userAddedToFamilyPayload = __spreadProps(__spreadValues({}, commonWebhookData), {
    name: "families:user-added-to-family",
    payload: {
        userId: "4567890"
    }
});
var guardianRequestExpiredPayload = __spreadProps(__spreadValues({}, commonWebhookData), {
    name: "families:guardian-request-expired",
    payload: {
        userId: "5678901"
    }
});
var familiesGroupDeletedPayload = __spreadProps(__spreadValues({}, commonWebhookData), {
    name: "families:family-group-deleted",
    payload: {
        members: [
            {
                userId: "6789012",
                role: "MANAGER"
            },
            {
                userId: "7890123",
                role: "SUPERVISED"
            },
            {
                userId: "8901234",
                role: "SUPERVISED"
            }
        ]
    }
});
var webhookPayloads = {
    childAccountGraduated: childAccountGraduatedPayload,
    settingsEffectiveValuesChanged: settingsEffectiveValuesChangedPayload,
    userRemovedFromFamily: userRemovedFromFamilyPayload,
    userAddedToFamily: userAddedToFamilyPayload,
    guardianRequestExpired: guardianRequestExpiredPayload,
    familiesGroupDeleted: familiesGroupDeletedPayload
};

// webhook-load-tests.ts
var webhookType = __ENV.WEBHOOK_TYPE || "all";
var options = {
    scenarios: {
        webhook_rps_test: {
            executor: "ramping-arrival-rate",
            startRate: 0,
            timeUnit: "1s",
            preAllocatedVUs: 500,
            maxVUs: 800,
            stages: [
                { target: 50, duration: "1m" },
                { target: 150, duration: "1m" },
                { target: 250, duration: "1m" },
                { target: 350, duration: "1m" },
                { target: 450, duration: "1m" },
                { target: 450, duration: "2m" },
                // steady
                { target: 300, duration: "1m" },
                { target: 150, duration: "1m" },
                { target: 50, duration: "1m" }
            ]
            // stages: [
            //     { duration: '1m', target: 10 },
            //     { duration: '1m', target: 30 },
            //     { duration: '1m', target: 50 },
            //     { duration: '1m', target: 75 },
            //     { duration: '1m', target: 100 },
            //     { duration: '2m', target: 100 },
            //     { duration: '1m', target: 60 },
            //     { duration: '1m', target: 30 },
            //     { duration: '1m', target: 10 },
            // ],
        }
    },
    thresholds: {
        http_req_duration: ["p(95)<500"],
        // 95% of requests should complete in < 500ms
        http_req_failed: ["rate<0.01"]
        // Failures should be under 1%
    }
};
function updateRequestPayload(payload) {
    const innerPayload = __spreadProps(__spreadValues({}, payload), {
        payload: __spreadProps(__spreadValues({}, payload.payload), {
            userId: `550385866`
        })
    });
    return __spreadProps(__spreadValues({}, innerPayload), {
        time: Date.now()
    });
}
function webhook_load_tests_default() {
    let response;
    let effectiveWebhookType = webhookType;
    if (webhookType === "all") {
        const allWebhookTypes = [
            "child-graduated",
            "settings-changed",
            "user-removed",
            "user-added",
            "guardian-expired",
            "family-deleted"
        ];
        effectiveWebhookType = allWebhookTypes[__ITER % allWebhookTypes.length];
    }
    console.log(`Testing webhook type ${effectiveWebhookType} Iteration ${__ITER}`);
    switch (effectiveWebhookType) {
        case "child-graduated":
            response = webhookChildAccountGraduated(
                updateRequestPayload(webhookPayloads.childAccountGraduated)
            );
            check2(response, {
                "Child Account Graduated webhook returns 204": (r) => r.status === 204
            });
            break;
        case "settings-changed":
            response = webhookSettingsEffectiveValuesChanged(
                updateRequestPayload(webhookPayloads.settingsEffectiveValuesChanged)
            );
            check2(response, {
                "Settings Effective Values Changed webhook returns 204": (r) => r.status === 204
            });
            break;
        case "user-removed":
            response = webhookUserRemovedFromFamily(
                updateRequestPayload(webhookPayloads.userRemovedFromFamily)
            );
            check2(response, {
                "User Removed From Family webhook returns 204": (r) => r.status === 204
            });
            break;
        case "user-added":
            response = webhookUserAddedToFamily(
                updateRequestPayload(webhookPayloads.userAddedToFamily)
            );
            check2(response, {
                "User Added To Family webhook returns 204": (r) => r.status === 204
            });
            break;
        case "guardian-expired":
            response = webhookGuardianRequestExpired(
                updateRequestPayload(webhookPayloads.guardianRequestExpired)
            );
            check2(response, {
                "Guardian Request Expired webhook returns 204": (r) => r.status === 204
            });
            break;
        case "family-deleted":
            response = webhookFamiliesGroupDeleted(
                updateRequestPayload(webhookPayloads.familiesGroupDeleted)
            );
            check2(response, {
                "Families Group Deleted webhook returns 204": (r) => r.status === 204
            });
            break;
        default:
            console.error(`Unknown webhook type: ${effectiveWebhookType}`);
    }
    console.log(response);
    sleep(Math.random() * 0.4 + 0.1);
}
export {
    webhook_load_tests_default as default,
    options
};
