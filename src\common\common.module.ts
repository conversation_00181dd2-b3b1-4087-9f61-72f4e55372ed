import { Module } from '@nestjs/common';
import { EncryptionService } from '@superawesome/freekws-auth-library';
import { CacheModule, ECacheServiceProviders } from '@superawesome/freekws-cache-decorator';
import { SALogger } from '@superawesome/freekws-common-logger';
import { MetricsService, MetricsServiceModule } from '@superawesome/freekws-metrics-nestjs-service';

import { CallbackModule } from './services/callback/callback.module';
import { ConfigModule } from './services/config/config.module';
import { ConfigService } from './services/config/config.service';
import { DevPortalModule } from './services/dev-portal/dev-portal.module';
import { KeycloakModule } from './services/keycloak/keycloak.module';
import { KwsSignatureService } from './services/kws-signature/kws-signature.service';

const logger = new SALogger();

@Module({
  imports: [
    ConfigModule,
    MetricsServiceModule,
    KeycloakModule,
    {
      module: CacheModule,
      imports: [ConfigModule, MetricsServiceModule],
      providers: [
        {
          provide: ECacheServiceProviders.Config,
          useClass: ConfigService,
        },
        {
          provide: ECacheServiceProviders.Metric,
          useClass: MetricsService,
        },
      ],
      exports: [ECacheServiceProviders.Config, ECacheServiceProviders.Metric],
    },
    DevPortalModule,
    CallbackModule,
  ],
  providers: [
    {
      provide: SALogger,
      useValue: logger,
    },
    {
      provide: EncryptionService,
      useFactory: (config: ConfigService, logger: SALogger) => {
        const { secrets, secretVersion } = config.getEncryptionConfig();
        return new EncryptionService(secrets, secretVersion, logger);
      },
      inject: [ConfigService, SALogger],
    },
    KwsSignatureService,
  ],
  exports: [ConfigModule, MetricsServiceModule, SALogger, EncryptionService],
})
export class CommonModule {}
