#!/bin/bash

# Define the base file and output file
BASE_FILE="000_base.json"
OUTPUT_FILE="transformation-rules.json"
TEMP_FILE=$(mktemp)

# Define the placeholder value to search for (as a variable)
ORGENVID="'org-env-id-value'"
TODAYDATE="'today-date'"

# Check if a replacement value is provided
if [ $# -eq 0 ]; then
    echo "Please provide a value to replace $ORGENVID as an argument."
    exit 1
fi

# Define the replacement value from the script argument
REPLACEMENT_VALUE="$1"

# Start with the base file
echo "Processing $BASE_FILE"
jq '.' "$BASE_FILE" > "$TEMP_FILE"

# Loop through all numbered JSON files and combine their contents
for file in [1-9]*.json; do
    if [ "$file" != "$BASE_FILE" ] && [ "$file" != "$OUTPUT_FILE" ]; then
        echo "Processing $file"
        if ! jq -s '.[0].rules += .[1] | .[0]' "$TEMP_FILE" "$file" > "${TEMP_FILE}.new"; then
            echo "Error processing $file"
            exit 1
        fi
        mv "${TEMP_FILE}.new" "$TEMP_FILE"
    fi
done

# Replace the placeholder value with the provided replacement value
echo "Applying final transformations"
TODAY_DATE=$(date +%Y-%m-%d)

if ! jq --arg new_value "$REPLACEMENT_VALUE" \
       --arg placeholder "$ORGENVID" \
       --arg date_placeholder "$TODAYDATE" \
       --arg today_date "$TODAY_DATE" '
    (.rules[] | 
      select(.expression == $placeholder) | .expression) = "\"" + $new_value + "\"" |
    (.. | select(type == "string")) |= sub($date_placeholder; $today_date)
' "$TEMP_FILE" > "$OUTPUT_FILE"; then
    echo "Error in final transformation step"
    exit 1
fi


# Clean up the temporary file
rm "$TEMP_FILE"

echo "Combined rules have been saved to $OUTPUT_FILE with $ORGENVID replaced by '$REPLACEMENT_VALUE'"

