import { forwardRef, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { AppTranslation } from './app-translation.entity';
import { AppController } from './app.controller';
import { App } from './app.entity';
import { AppService } from './app.service';
import { CommonModule } from '../common/common.module';
import { AgeGateModule } from '../common/services/age-gate/age-gate.module';
import { AnalyticModule } from '../common/services/analytic/analytic.module';
import { SettingsModule } from '../common/services/settings/settings.module';
import { JwkModule } from '../oauth/jwk.module';
import { OrgEnvModule } from '../org-env/org-env.module';
import { UserModule } from '../user/user.module';

@Module({
  imports: [
    JwkModule,
    AgeGateModule,
    forwardRef(() => UserModule),
    TypeOrmModule.forFeature([App, AppTranslation]),
    AnalyticModule,
    SettingsModule,
    CommonModule,
    OrgEnvModule,
  ],
  providers: [AppService],
  exports: [AppService],
  controllers: [AppController],
})
export class AppModule {}
