import { FastifyRequest } from 'fastify';

import { Or, And, Not } from './boolean-ops.policy';

const mockRequest = {} as FastifyRequest;

describe('boolean ops policies', () => {
  const brokenError = new Error('Its Broken');
  const passPolicy1 = {
    handle: async () => true,
  };
  const passPolicy2 = {
    handle: async () => true,
  };
  const rejectPolicy1 = {
    handle: async () => false,
  };
  const errorPolicy1 = {
    handle: async () => {
      throw brokenError;
    },
  };

  describe('Or', () => {
    describe('handle()', () => {
      it('should return true if all policies pass', async () => {
        expect(await new Or(passPolicy1, passPolicy2).handle(mockRequest)).toBe(true);
      });
      it('should return true if a single policy pass', async () => {
        expect(await new Or(passPolicy1, rejectPolicy1).handle(mockRequest)).toBe(true);
      });
      it('should return true if a single policy passes but others error', async () => {
        expect(await new Or(passPolicy1, errorPolicy1).handle(mockRequest)).toBe(true);
      });
      it('should return false if all policies fail', async () => {
        await expect(new Or(rejectPolicy1).handle(mockRequest)).rejects.toThrow('Sub-policy failure');
      });
      it('should return false if all policies fail or error', async () => {
        await expect(new Or(rejectPolicy1, errorPolicy1).handle(mockRequest)).rejects.toThrow('Its Broken');
      });
      it('should fail all policies error', async () => {
        await expect(new Or(errorPolicy1).handle(mockRequest)).rejects.toThrow('Its Broken');
      });
    });
  });

  describe('And', () => {
    describe('handle()', () => {
      it('should return true if all policies pass', async () => {
        expect(await new And(passPolicy1, passPolicy2).handle(mockRequest)).toBe(true);
      });
      it('should return false if a single policy fails', async () => {
        expect(await new And(passPolicy1, rejectPolicy1).handle(mockRequest)).toBe(false);
      });
      it('should propagate if a single policy errors', async () => {
        await expect(new And(passPolicy1, errorPolicy1).handle(mockRequest)).rejects.toThrow(brokenError);
      });
    });
  });

  describe('Not', () => {
    describe('handle()', () => {
      it('should return false if original policy passes', async () => {
        expect(await new Not(passPolicy1).handle(mockRequest)).toBe(false);
      });
      it('should return true if original policy fails', async () => {
        expect(await new Not(rejectPolicy1).handle(mockRequest)).toBe(true);
      });
      it('should propagate if original policy throws error', async () => {
        await expect(new Not(errorPolicy1).handle(mockRequest)).rejects.toThrow(brokenError);
      });
    });
  });

  describe('combined', () => {
    it('should return true', async () => {
      // (Not reject policy) AND (passPolicy1 OR rejectPolicy1) AND passPolicy2
      expect(
        await new And(new Not(rejectPolicy1), new Or(passPolicy1, rejectPolicy1), passPolicy2).handle(mockRequest),
      ).toBe(true);
    });

    it('should return false', async () => {
      // Not((Not reject policy) AND (passPolicy1 OR rejectPolicy1) AND passPolicy2)
      expect(
        await new Not(new And(new Not(rejectPolicy1), new Or(passPolicy1, rejectPolicy1), passPolicy2)).handle(
          mockRequest,
        ),
      ).toBe(false);
    });
  });
});
