import { NestApplication } from '@nestjs/core';
import { getRepositoryToken } from '@nestjs/typeorm';
import { In } from 'typeorm';

import { DeleteOldRefreshTokens } from './delete-old-token-refresh-entries';
import { RefreshToken } from '../../oauth/refresh-token.entity';

const mockRefreshTokenRepo = {
  find: jest.fn(),
  delete: jest.fn(),
};

const mockApp = {
  get: jest.fn(),
} as unknown as NestApplication;

describe('DeleteOldRefreshTokens', () => {
  let job: DeleteOldRefreshTokens;

  beforeEach(() => {
    jest.clearAllMocks();
    (mockApp.get as jest.Mock).mockImplementation((token) => {
      if (token === getRepositoryToken(RefreshToken)) {
        return mockRefreshTokenRepo;
      }
      return;
    });

    process.env.REFRESH_TOKEN_RETENTION_DAYS = '30';
    process.env.REFRESH_TOKEN_DELETE_BATCH_SIZE = '1000';
  });

  afterEach(() => {
    delete process.env.REFRESH_TOKEN_RETENTION_DAYS;
    delete process.env.REFRESH_TOKEN_DELETE_BATCH_SIZE;
  });

  describe('in dry mode', () => {
    beforeEach(() => {
      job = new DeleteOldRefreshTokens(mockApp, true);
    });

    it('should find and count tokens without deleting them', async () => {
      const mockTokens = [
        { id: 1, orgEnvId: 'org1', createdAt: new Date('2023-01-01') },
        { id: 2, orgEnvId: 'org1', createdAt: new Date('2023-01-02') },
      ];

      mockRefreshTokenRepo.find.mockResolvedValueOnce(mockTokens).mockResolvedValueOnce([]);

      await job.run();

      expect(mockRefreshTokenRepo.find).toHaveBeenCalledWith(
        expect.objectContaining({
          where: {
            createdAt: expect.objectContaining({
              _type: 'lessThan',
            }),
          },
          take: 1000,
          select: ['id', 'orgEnvId', 'createdAt'],
        }),
      );

      expect(mockRefreshTokenRepo.delete).not.toHaveBeenCalled();
    });
  });

  describe('in production mode', () => {
    beforeEach(() => {
      job = new DeleteOldRefreshTokens(mockApp, false);
      mockRefreshTokenRepo.find.mockReset();
      mockRefreshTokenRepo.delete.mockReset();
    });

    it('should delete tokens in batches', async () => {
      const mockTokens = [
        { id: 1, orgEnvId: 'org1', createdAt: new Date('2023-01-01') },
        { id: 2, orgEnvId: 'org1', createdAt: new Date('2023-01-02') },
      ];

      mockRefreshTokenRepo.find.mockResolvedValueOnce(mockTokens).mockResolvedValueOnce([]);

      mockRefreshTokenRepo.delete.mockResolvedValue({ affected: 2 });

      await job.run();

      expect(mockRefreshTokenRepo.delete).toHaveBeenCalledWith({
        id: In([1, 2]),
        orgEnvId: 'org1',
      });
    });
  });

  describe('configuration', () => {
    it('should use default retention days when env var is not set', async () => {
      delete process.env.REFRESH_TOKEN_RETENTION_DAYS;
      job = new DeleteOldRefreshTokens(mockApp, true);

      mockRefreshTokenRepo.find.mockResolvedValue([]);

      await job.run();

      expect(mockApp.get).toHaveBeenCalledWith(getRepositoryToken(RefreshToken));
    });

    it('should use custom retention days from env var', async () => {
      process.env.REFRESH_TOKEN_RETENTION_DAYS = '60';
      job = new DeleteOldRefreshTokens(mockApp, true);

      mockRefreshTokenRepo.find.mockResolvedValue([]);

      await job.run();

      expect(mockRefreshTokenRepo.find).toHaveBeenCalledWith(
        expect.objectContaining({
          where: {
            createdAt: expect.objectContaining({
              _type: 'lessThan',
            }),
          },
        }),
      );
    });
  });
});
