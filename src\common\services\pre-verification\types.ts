export const enum PreverificationErrorMessages {
  RESOURCE_NOT_FOUND = 'The requested endpoint/resource was not found',
}

class PreverificationServiceError extends Error {}

export class GetParentVerificationsError extends PreverificationServiceError {
  constructor(cause?: Error) {
    super('Can not get parent verification status', { cause });
  }
}

export type PreverificationErrorResponse = {
  meta: {
    requestId: string;
    timestamp: string;
  };
  error: {
    message: string;
    code: string;
    errorCode: string;
    statusCode: number;
  };
};
