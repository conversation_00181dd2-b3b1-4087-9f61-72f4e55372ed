{"name": "@superawesome/freekws-classic-wrapper-common", "version": "1.0.0", "description": "Shared types and utility code shared between service and clients", "main": "dist/index.js", "typings": "dist/index.d.ts", "scripts": {"build": "tsc -p tsconfig.build.json", "build:swagger": "tsc -p tsconfig.build.json", "lint": "eslint \"src/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "semantic-release": "freekws-semantic-release --path ./dist --copy-package --transform-path --tag-format {PACKAGE_NAME}-v{VERSION} --publish-on-artifactory", "semantic-release:dryrun": "freekws-semantic-release --dry-run --path ./dist --copy-package --transform-path --tag-format {PACKAGE_NAME}-v{VERSION} --publish-on-artifactory"}, "author": "", "license": "UNLICENSED", "peerDependencies": {"@nestjs/swagger": ">= 7", "@superawesome/freekws-regional-config": ">= 7.0.0 < 8", "class-validator": ">= 0.13.2 < 11", "class-transformer": ">= 0.5.1 < 1"}}