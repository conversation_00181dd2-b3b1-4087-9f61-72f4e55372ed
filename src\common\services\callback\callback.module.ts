import { Module } from '@nestjs/common';
import {
  CALLBACK_SERVICE_API_CLIENT_INJECT_KEY,
  callbackServicePlugin,
} from '@superawesome/freekws-callback-service-common';
import { NestJsClient } from '@superawesome/freekws-clients-nestjs';
import { DEVPORTAL_BACKEND_API_CLIENT_UPSTREAM } from '@superawesome/freekws-devportal-common';
import { MetricsService, MetricsServiceModule } from '@superawesome/freekws-metrics-nestjs-service';
import type { AxiosError } from 'axios';

import { CallbackService } from './callback.service';
import { ConfigModule } from '../config/config.module';
import { ConfigService } from '../config/config.service';
import { KeycloakModule } from '../keycloak/keycloak.module';

@Module({
  imports: [ConfigModule, MetricsServiceModule, KeycloakModule],
  providers: [
    MetricsService,
    CallbackService,
    {
      provide: CALLBACK_SERVICE_API_CLIENT_INJECT_KEY,
      useFactory: (config: ConfigService, metricsService: MetricsService) => {
        const apiClientConfig = config.getCallbackApiClient();
        const circuitBreakerConfig = config.getCallbackApiCircuitBreakerConfig();
        return new NestJsClient(callbackServicePlugin, apiClientConfig.baseURL, metricsService.metrics, {
          timeout: apiClientConfig.timeoutMs,
          upstream: DEVPORTAL_BACKEND_API_CLIENT_UPSTREAM,
          retry: {
            retries: apiClientConfig.retries,
            initialRetryDelay: apiClientConfig.initialRetryDelay,
            bailOnStatus: apiClientConfig.bailOnStatus,
          },
          circuitBreaker: {
            timeout: circuitBreakerConfig.timeoutMs,
            errorThresholdPercentage: circuitBreakerConfig.errorThresholdPercentage,
            resetTimeout: circuitBreakerConfig.resetTimeoutMs,
            errorFilter: (error: AxiosError) => {
              return !!(error.response?.status && error.response.status >= 400 && error.response.status < 500);
            },
          },
        });
      },
      inject: [ConfigService, MetricsService],
    },
  ],
  exports: [CallbackService, CALLBACK_SERVICE_API_CLIENT_INJECT_KEY],
})
export class CallbackModule {}
