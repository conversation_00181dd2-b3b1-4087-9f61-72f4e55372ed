import { NestFactory } from '@nestjs/core';
import { FastifyAdapter, NestFastifyApplication } from '@nestjs/platform-fastify';

import { bootstrap } from './bootstrap';
import { RootAppModule } from './root-app.module';

async function main() {
  const app = await NestFactory.create<NestFastifyApplication>(RootAppModule, new FastifyAdapter({ trustProxy: true }));
  await bootstrap(app);
}

// eslint-disable-next-line unicorn/prefer-top-level-await
main();
