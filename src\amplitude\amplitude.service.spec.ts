import { Status } from '@amplitude/node';
import { Test, TestingModule } from '@nestjs/testing';
import { EStorageRegionShortID } from '@superawesome/freekws-auth-library';
import { SALogger } from '@superawesome/freekws-common-logger';
import { TKWSParentAccessToken } from '@superawesome/freekws-nestjs-guards/types';

import { AmplitudeService } from './amplitude.service';
import { ConfigService } from '../common/services/config/config.service';

const mockLogEvent = jest.fn();
const mockFlush = jest.fn();

jest.mock('@amplitude/node', () => {
  return {
    ...jest.requireActual('@amplitude/node'),
    init: () => {
      return {
        logEvent: mockLogEvent,
        flush: mockFlush,
      };
    },
  };
});

describe('AmplitudeService', () => {
  let service: AmplitudeService;

  const mockConfigService = {
    config: {
      amplitude: {
        apiKey: 'test-api-key',
        serverUrl: 'https://test-server-url.com',
      },
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AmplitudeService,
        SALogger,
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<AmplitudeService>(AmplitudeService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should call logEvent with the right data', async () => {
    mockLogEvent.mockResolvedValue(true);
    mockFlush.mockResolvedValue(true);
    const accessToken: Partial<TKWSParentAccessToken> = {
      content: {
        aud: 'test',
        azp: 'test',
        exp: 123,
        iat: 123,
        kws: {
          orgId: 'test',
          orgEnvId: 'test',
          products: [{ productId: 'test', productEnvId: 'test' }],
          mode: 'test',
          orgLevel: false,
          s: [],
          f: [],
          sr: EStorageRegionShortID.EU_WEST_1,
        },
        scope: 'test',
        sub: 'test',
        typ: 'test',
        jti: 'test_user_id',
        child_location: 'whatever',
        external_payload: 'what',
        parent_email: 'what',
      },
    };
    const eventProperties = { whatever: true };
    await service.logEvent('test', accessToken as TKWSParentAccessToken, eventProperties);
    expect(mockLogEvent).toHaveBeenCalledTimes(1);
    expect(mockLogEvent).toHaveBeenCalledWith({
      event_properties: eventProperties,
      event_type: 'test',
      user_id: accessToken.content?.jti,
      user_properties: {
        ...accessToken.content?.kws,
        repository: 'freekws-parent-portal-backend',
      },
    });
    expect(mockFlush).toHaveBeenCalledTimes(1);
  });

  it('should log event without token', async () => {
    mockLogEvent.mockResolvedValue(true);
    mockFlush.mockResolvedValue(true);

    await service.logWithoutToken('test', { whatever: true });
    expect(mockLogEvent).toHaveBeenCalledTimes(1);
    expect(mockLogEvent).toHaveBeenCalledWith({
      user_id: 'missing_token',
      event_properties: { whatever: true },
      event_type: 'test',
      user_properties: {
        repository: 'freekws-parent-portal-backend',
      },
    });
    expect(mockFlush).toHaveBeenCalledTimes(1);
  });

  it('should handle errors from amplitude', async () => {
    mockLogEvent.mockResolvedValue(true);
    mockFlush.mockResolvedValue({ status: Status.Invalid, body: 'body' });

    await service.logWithoutToken('test', { whatever: true });
    expect(mockLogEvent).toHaveBeenCalledTimes(1);
    expect(mockLogEvent).toHaveBeenCalledWith({
      user_id: 'missing_token',
      event_properties: { whatever: true },
      event_type: 'test',
      user_properties: {
        repository: 'freekws-parent-portal-backend',
      },
    });
    expect(mockFlush).toHaveBeenCalledTimes(1);
  });
});
