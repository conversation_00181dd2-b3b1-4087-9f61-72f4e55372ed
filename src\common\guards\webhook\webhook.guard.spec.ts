import { BadRequestException, ExecutionContext, UnauthorizedException } from '@nestjs/common';

import { WebhookGuard } from './webhook.guard';
import { OrgEnvService } from '../../../org-env/org-env.service';
import { KwsSignatureService } from '../../services/kws-signature/kws-signature.service';

describe('WebhookGuard', () => {
  const mockGetRequest = jest.fn();
  const context = {
    getHandler: jest.fn(),
    switchToHttp: () => ({
      getRequest: mockGetRequest,
    }),
  } as unknown as ExecutionContext;

  const mockSignatureService = {
    verify: jest.fn(),
  };

  let guard: WebhookGuard;

  beforeAll(() => {
    const orgEnvServiceMock = {
      getOrgEnvFromRequest: () => {
        return {
          id: '123',
        };
      },
    };

    guard = new WebhookGuard(
      mockSignatureService as unknown as KwsSignatureService,
      orgEnvServiceMock as unknown as OrgEnvService,
    );
  });

  beforeEach(() => {
    mockSignatureService.verify = jest.fn();
  });

  it('should throw a bad request exception when there is no signature in header', async () => {
    const request = {
      headers: {},
    };
    mockGetRequest.mockReturnValueOnce(request);

    await expect(guard.canActivate(context)).rejects.toBeInstanceOf(BadRequestException);
  });

  it('should throw exception when signature is invalid', async () => {
    mockSignatureService.verify.mockResolvedValueOnce(false);

    const request = {
      headers: {
        'x-kws-signature': 'Invalid-KWS-Signature',
      },
      body: {},
      params: {
        orgEnvId: 'envId',
      },
    };

    mockGetRequest.mockReturnValueOnce(request);

    await expect(guard.canActivate(context)).rejects.toBeInstanceOf(UnauthorizedException);
  });

  it('should return true for valid request', async () => {
    mockSignatureService.verify.mockResolvedValueOnce(true);

    const request = {
      headers: {
        'x-kws-signature': 'KWS-Signature',
      },
      body: {},
      params: {
        orgEnvId: 'envId',
      },
    };

    mockGetRequest.mockReturnValueOnce(request);

    await expect(guard.canActivate(context)).resolves.toBeTruthy();
  });
});
