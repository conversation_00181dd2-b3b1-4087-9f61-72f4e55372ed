import { MigrationInterface, QueryRunner } from 'typeorm';

export class OrgEnvAddHost1729592937338 implements MigrationInterface {
  name = 'OrgEnvAddHost1729592937338';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "org_env"
            ADD "host" character varying NOT NULL
        `);
    await queryRunner.query(`
            CREATE INDEX "IDX_42918623bdd16ec23eb803a61e" ON "org_env" ("host")
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            DROP INDEX "public"."IDX_42918623bdd16ec23eb803a61e"
        `);
    await queryRunner.query(`
            ALTER TABLE "org_env" DROP COLUMN "host"
        `);
  }
}
