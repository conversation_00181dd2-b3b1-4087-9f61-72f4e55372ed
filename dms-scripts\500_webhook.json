[{"rule-type": "selection", "rule-id": "500", "rule-name": "Include webhooks table", "object-locator": {"schema-name": "public", "table-name": "webhooks"}, "rule-action": "include", "filters": [{"filter-type": "source", "column-name": "deletedAt", "filter-conditions": [{"filter-operator": "null"}]}]}, {"rule-type": "transformation", "rule-id": "501", "rule-name": "Rename webhooks to webhook", "rule-action": "rename", "rule-target": "table", "object-locator": {"schema-name": "public", "table-name": "webhooks"}, "value": "webhook"}, {"rule-type": "transformation", "rule-id": "502", "rule-name": "Add orgEnvId column to webhooks", "rule-target": "column", "object-locator": {"schema-name": "public", "table-name": "webhooks"}, "rule-action": "add-column", "value": "orgEnvId", "expression": "'org-env-id-value'", "data-type": {"type": "string", "length": 255}}, {"rule-type": "transformation", "rule-id": "510", "rule-name": "Remove deletedAt column from webhooks", "rule-action": "remove-column", "rule-target": "column", "object-locator": {"schema-name": "public", "table-name": "webhooks", "column-name": "deletedAt"}}]