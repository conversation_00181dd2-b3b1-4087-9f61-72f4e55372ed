services:
  project:
    image: artifacts.ol.epicgames.net/supawe-kws-docker/superawesomeltd/freekws-classic-wrapper-backend:${TAG}
    user: "node:node"
    read_only: true
    tmpfs: /tmp
    security_opt:
      - no-new-privileges:true
    build:
      context: .
      target: builder
      args:
        ARTIFACTORY_TOKEN_PLATFORM: ${ARTIFACTORY_TOKEN_PLATFORM}
    environment:
      NODE_ENV: develop
      KAFKA_HOST: testkafka:9092
      LOG_LEVEL: error
      JEST_JUNIT_OUTPUT_DIR: ./reports/junit/
      DATABASE_URL: ***************************************/postgres
      DATABASE_READ_URL: ***************************************/postgres
      KEYCLOAK_BASE_URL: http://keycloak:8080/auth
    volumes:
      - './test-acceptance:/srv/test-acceptance'
      - './reports:/srv/reports'
    links:
      - classic-wrapper-backend
      - postgres
      - keycloak
      - stub
    depends_on:
      - classic-wrapper-backend
      - keycloak
      - stub

  classic-wrapper-backend:
    image: artifacts.ol.epicgames.net/supawe-kws-docker/superawesomeltd/freekws-classic-wrapper-backend:${TAG}
    command:
      - npm
      - run
      - start:dev
    build:
      context: .
      target: builder
      args:
        ARTIFACTORY_TOKEN_PLATFORM: ${ARTIFACTORY_TOKEN_PLATFORM}
    environment:
      NODE_ENV: develop
      KAFKA_HOST: testkafka:9092
      LOG_LEVEL: error
      JEST_JUNIT_OUTPUT_DIR: ./reports/junit/
      DATABASE_URL: ***************************************/postgres
      DATABASE_READ_URL: ***************************************/postgres
      KEYCLOAK_BASE_URL: http://keycloak:8080/auth
    ports:
      - '3000:80'
    volumes:
      - './src:/srv/src'
      - './test:/srv/test'
      - './config:/srv/config'
      - './swagger:/srv/swagger'
      - './coverage:/srv/coverage'
      - './reports:/srv/reports'
      - './migration:/srv/migration'
    links:
      - postgres
      - keycloak
      - stub
      - testkafka
    depends_on:
      - keycloak
      - postgres
      - stub
      - testkafka

  postgres:
    image: postgres:15.5
    environment:
      POSTGRES_PASSWORD: dev
    ports:
      - '15432:5432'

  keycloak:
    # Ensure this keycloak image remains in sync with keycloak in other docker-compose files
    image: artifacts.ol.epicgames.net/supawe-kws-docker/superawesomeltd/freekws-test-keycloak:f830a69b33a9d9117f3f3935cc716216f7a44938
    ports:
      - '18080:8080'

  testkafka:
    image: artifacts.ol.epicgames.net/supawe-kws-docker/superawesomeltd/freekws-test-kafka:cfda18110eac85768acbe943d11ed11aa75ce3bc
    hostname: testkafka
  
  stub:
    command:
      - mb
      - --configfile
      - imposters.ejs
      - --allowInjection
      - --stringify
    build:
      context: test-acceptance/stubs/.
      args:
        ARTIFACTORY_TOKEN_PLATFORM: ${ARTIFACTORY_TOKEN_PLATFORM}
    ports:
      - '2525:2525' # Useful to check mountebank logs
      - '4747:4747'
      - '4748:4748'
      - '4749:4749'
      - '4750:4750'
      - '4751:4751'
      - '4752:4752'