helm/charts
helm/requirements.lock
helm/custom-*
helm/Chart.lock

dms-scripts/transformation-rules.json

# Created by https://www.gitignore.io/api/node
# Edit at https://www.gitignore.io/?templates=node

### Node ###
# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage and reports directory
coverage
reports/*
!reports/junit/
reports/junit/*
!reports/junit/.gitkeep

# nyc test coverage
.nyc_output

# Grunt intermediate storage (https://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# Bower dependency directory (https://bower.io/)
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons (https://nodejs.org/api/addons.html)
build/Release

# Dependency directories
node_modules/
jspm_packages/

# TypeScript v1 declaration files
typings/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env

# parcel-bundler cache (https://parceljs.org/)
.cache

# next.js build output
.next

# nuxt.js build output
.nuxt

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

#DynamoDB Local files
.dynamodb/
shared-local-instance.db

# End of https://www.gitignore.io/api/node

/DRAFT/
.ipynb_checkpoints
*__pycache__*

src/data/*

dist/

swagger/

.DS_Store

.idea/
*.iml
# .vscode/
tmp.yaml

.vscode
config/local.*
/config/myhost.json
