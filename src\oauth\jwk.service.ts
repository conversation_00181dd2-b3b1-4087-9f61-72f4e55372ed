import { Injectable, NotFoundException, UnprocessableEntityException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { randomBytes } from 'crypto';
import { PrivateKey, Secret, sign, SignOptions, verify, decode, Algorithm } from 'jsonwebtoken';
import { Span } from 'nestjs-ddtrace';
import { IsNull, Not, Repository } from 'typeorm';

import { JWK } from './jwk.entity';
import { TJWT, TJwtPayload } from './types';

type VerifyParams = Parameters<typeof verify>;

@Injectable()
@Span()
export class JWKService {
  private readonly TOKEN_EXPIRY_TIME_SECONDS = 60 * 60 * 24;
  static readonly TOKEN_ISSUER = 'superawesome';

  constructor(@InjectRepository(JWK) private readonly jwkRepository: Repository<JWK>) {}

  static async signAsync(...args: [string | Buffer | object, Secret | PrivateKey, SignOptions]): Promise<string> {
    return new Promise((resolve, reject) => {
      sign(...args, (err: Error | null, data: string) => {
        if (err) {
          reject(err);
          return;
        }
        resolve(data);
      });
    });
  }

  static async verifyAsync(...args: [VerifyParams[0], VerifyParams[1], VerifyParams[2]]): Promise<TJWT> {
    return new Promise((resolve, reject) => {
      verify(...args, (err: Error | null, data: TJWT) => {
        if (err) {
          reject(err);
          return;
        }
        resolve(data);
      });
    });
  }

  static isAssymmetric(algorithm: Algorithm) {
    switch (algorithm) {
      case 'HS256':
      case 'HS384':
      case 'HS512': {
        return false;
      }
      default: {
        return true;
      }
    }
  }

  async sign(payload: TJwtPayload, orgEnvId: string, expireTimeSeconds?: number): Promise<string> {
    const { keyId, privatePem, algorithm } = await this.getPrivateKey(orgEnvId);

    const payloadWithJti = {
      ...payload,
      jti: randomBytes(16).toString('hex'),
    };

    return JWKService.signAsync(payloadWithJti, privatePem, {
      issuer: JWKService.TOKEN_ISSUER,
      algorithm: algorithm,
      expiresIn: expireTimeSeconds ?? this.TOKEN_EXPIRY_TIME_SECONDS,
      keyid: keyId,
    });
  }

  async verify(token: string, orgEnvId: string): Promise<TJWT> {
    const decoded = decode(token, { complete: true });
    if (!decoded?.header.kid) {
      throw new NotFoundException('Matching key not found');
    }

    const { key, algorithm } = await this.getValidationKey(decoded.header.kid, orgEnvId);
    return JWKService.verifyAsync(token, key, {
      algorithms: [algorithm],
      issuer: JWKService.TOKEN_ISSUER,
    });
  }

  async getPrivateKey(orgEnvId: string): Promise<Pick<JWK, 'privatePem' | 'keyId' | 'algorithm'>> {
    const { privatePem, keyId, algorithm } = await this.jwkRepository.findOneOrFail({
      where: {
        orgEnv: { id: orgEnvId },
        privatePem: Not(IsNull()),
      },
    });

    return {
      privatePem: Buffer.from(privatePem, 'base64').toString(),
      keyId: keyId,
      algorithm: algorithm,
    };
  }

  async getValidationKey(
    keyId: string,
    orgEnvId: string,
  ): Promise<{
    algorithm: Algorithm;
    key: string;
  }> {
    const { privatePem, publicPem, algorithm } = await this.jwkRepository.findOneByOrFail({
      keyId,
      orgEnvId,
    });

    if (JWKService.isAssymmetric(algorithm)) {
      if (!publicPem) {
        throw new UnprocessableEntityException('Assymmetric algorithm should have public key for validation');
      }
      return { algorithm: algorithm, key: Buffer.from(publicPem, 'base64').toString() };
    }

    return { algorithm: algorithm, key: Buffer.from(privatePem, 'base64').toString() };
  }
}
