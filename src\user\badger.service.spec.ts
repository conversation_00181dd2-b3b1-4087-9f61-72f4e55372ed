import { Test, TestingModule } from '@nestjs/testing';
import { HttpService } from '@superawesome/freekws-http-nestjs-service';
import { MetricsService } from '@superawesome/freekws-metrics-nestjs-service';

import { BadgerService, EbadgerDecision } from './badger.service';
import { ConfigService } from '../common/services/config/config.service';

jest.mock('@superawesome/freekws-common-logger', () => ({
  SALogger: jest.fn().mockImplementation(() => ({
    error: jest.fn(),
  })),
}));

describe('BadgerService', () => {
  let service: BadgerService;
  let metricsService: MetricsService;
  let httpService: HttpService;

  const mockBadgerConfig = {
    apiKey: 'test-api-key',
    baseURL: 'https://badger-api.test',
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        BadgerService,
        {
          provide: ConfigService,
          useValue: {
            getBadgerConfig: jest.fn().mockReturnValue(mockBadgerConfig),
          },
        },
        {
          provide: MetricsService,
          useValue: {
            increment: jest.fn(),
          },
        },
        {
          provide: HttpService,
          useValue: {
            init: jest.fn(),
            request: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<BadgerService>(BadgerService);
    metricsService = module.get<MetricsService>(MetricsService);
    httpService = module.get<HttpService>(HttpService);
  });

  describe('moderateUsername', () => {
    const mockUsername = 'testUser';
    const mockLanguage = 'en-US';

    const mockBadgerResponse = {
      screen_id: 'test-screen-id',
      workload: 'test-workload',
      customer_facing: true,
      action: EbadgerDecision.ALLOW,
      request: {
        text: mockUsername,
      },
      modules: ['test-module'],
      tags: [
        { id: 'test-tag', confidence: 0.9 },
        { id: 'eml-display-name-tags', confidence: 0.8 },
      ],
      text_reponse: {
        action: EbadgerDecision.ALLOW,
        sha256_hash: 'test-hash',
        hash_decision: true,
        classifier_response: {},
      },
    };

    it('should return the action from badger response when successful', async () => {
      jest.spyOn(httpService, 'request').mockResolvedValue({
        data: mockBadgerResponse,
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {},
      });

      const result = await service.moderateUsername(mockUsername, mockLanguage);

      expect(httpService.request).toHaveBeenCalledWith({
        method: 'POST',
        url: `${mockBadgerConfig.baseURL}/v4/moderate/screen/kws_usernames`,
        data: {
          text: mockUsername,
        },
        headers: {
          'Content-Type': 'application/json',
          'X-Epic-Auth-Key': mockBadgerConfig.apiKey,
        },
      });
      expect(result).toBe(EbadgerDecision.ALLOW);
    });

    it('should return BADGER_ERROR_RESPONSE when the request fails', async () => {
      const mockError = new Error('Request failed');
      jest.spyOn(httpService, 'request').mockRejectedValue(mockError);

      const result = await service.moderateUsername(mockUsername, mockLanguage);

      expect(result).toBe('BADGER_ERROR_RESPONSE');
      expect(service['logger'].error).toHaveBeenCalledWith('Badger error response', mockError);
    });

    it('should use default language code when language is not provided', async () => {
      jest.spyOn(httpService, 'request').mockResolvedValue({
        data: mockBadgerResponse,
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {},
      });

      await service.moderateUsername(mockUsername);

      expect(metricsService.increment).toHaveBeenCalledWith(
        'sa.classicwrapper.kws_badger_result',
        1,
        expect.arrayContaining(['language:en']),
      );
    });

    it('should handle different language codes correctly', async () => {
      jest.spyOn(httpService, 'request').mockResolvedValue({
        data: mockBadgerResponse,
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {},
      });

      await service.moderateUsername(mockUsername, 'fr   ');

      expect(metricsService.increment).toHaveBeenCalledWith(
        'sa.classicwrapper.kws_badger_result',
        1,
        expect.arrayContaining(['language:fr']),
      );
    });

    it('should send metrics with filtered tags', async () => {
      jest.spyOn(httpService, 'request').mockResolvedValue({
        data: mockBadgerResponse,
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {},
      });

      await service.moderateUsername(mockUsername, mockLanguage);

      expect(metricsService.increment).toHaveBeenCalledWith(
        'sa.classicwrapper.kws_badger_result',
        1,
        expect.arrayContaining([`decision:${EbadgerDecision.ALLOW}`, 'language:en', 'badger_test-tag:true']),
      );
      // Verify that the eml-display-name-tags was filtered out
      expect(metricsService.increment).toHaveBeenCalledWith(
        'sa.classicwrapper.kws_badger_result',
        1,
        expect.not.arrayContaining(['badger_eml-display-name-tags:true']),
      );
    });
  });
});
