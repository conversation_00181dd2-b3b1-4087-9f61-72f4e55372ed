import { BadRequestException, CanActivate, ExecutionContext, Injectable, UnauthorizedException } from '@nestjs/common';

import { WebhookFastifyRequest } from './types';
import { OrgEnvService } from '../../../org-env/org-env.service';
import { KwsSignatureService } from '../../services/kws-signature/kws-signature.service';

@Injectable()
export class WebhookGuard implements CanActivate {
  constructor(private readonly signatureService: KwsSignatureService, private readonly orgEnvService: OrgEnvService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest<WebhookFastifyRequest>();

    const signature = request.headers['x-kws-signature'] as string | undefined;
    if (!signature) {
      throw new BadRequestException('The x-kws-signature was not found');
    }

    const verified = await this.signatureService.verify(signature, request.body, request.params.orgEnvId);

    if (!verified) {
      throw new UnauthorizedException('Signature verification failed. Please check your request and try again.');
    }

    return verified;
  }
}
