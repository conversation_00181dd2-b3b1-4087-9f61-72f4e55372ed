import {
  BadRequestException,
  Body,
  ConflictException,
  Controller,
  ForbiddenException,
  Get,
  Headers,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Query,
  Res,
  UseGuards,
} from '@nestjs/common';
import { ApiBody, ApiOperation, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { EOAuthScope } from '@superawesome/freekws-classic-wrapper-common';
import { Public } from '@superawesome/freekws-nestjs-guards';
import { FastifyReply as Response } from 'fastify';

import { BadgerService, EbadgerDecision } from './badger.service';
import { TalonService } from './talon.service';
import { UserAppActivationDTO, UsernameCheckResponseDTO, UserRegisterDTO } from './user.dto';
import { UserService } from './user.service';
import { AppService } from '../app/app.service';
import { ExtractOrgEnv } from '../common/guards/inject-org-env/inject-org-env.decorator';
import { InjectOrgEnvGuard } from '../common/guards/inject-org-env/inject-org-env.guard';
import { JWT } from '../common/guards/oauth/jwt.decorator';
import { OauthGuard } from '../common/guards/oauth/oauth.guard';
import { And, HasScope, Policies } from '../common/guards/policies';
import { EAPITags } from '../common/types';
import { isUserToken } from '../oauth/type-check';
import { TJWT } from '../oauth/types';
import { OrgEnv } from '../org-env/org-env.entity';

@Controller()
export class UserController {
  constructor(
    private readonly userService: UserService,
    private readonly appService: AppService,
    private readonly badgerService: BadgerService,
    private readonly talonService: TalonService,
  ) {}

  @ApiOperation({
    summary: 'Activates a user for a given app',
  })
  @ApiTags(EAPITags.Apps)
  @ApiBody({
    type: UserAppActivationDTO,
  })
  @Post('/v1/users/:userId/apps')
  @HttpCode(HttpStatus.CREATED)
  @UseGuards(OauthGuard, InjectOrgEnvGuard)
  @Policies({
    useClass: And,
    inject: [new HasScope(EOAuthScope.USER)],
  })
  async activateUser(
    @Body() body: UserAppActivationDTO,
    @Param('userId') userId: number,
    @ExtractOrgEnv() orgEnv: OrgEnv,
    @JWT() jwt: TJWT,
    @Res() res: Response,
  ) {
    if (!isUserToken(jwt)) {
      throw new ForbiddenException('Token must be of scope "user"');
    }

    if (jwt.userId !== userId) {
      throw new ForbiddenException('Token does not belong to requested user');
    }

    const { app } = await this.appService.getAppInfoBy(jwt.appId, orgEnv.id);
    if (jwt.clientId !== app.oauthClientId) {
      throw new ForbiddenException('Token does not belong to requested app');
    }

    const appId = app.id;
    const existingActivation = await this.appService.userHasActivation(orgEnv.id, userId, appId);
    if (existingActivation) {
      throw new ConflictException('App activation already exists');
    }

    await this.userService.activateUserToApp(userId, appId, body, orgEnv.id);

    // To align with classic headers and response
    res.header('Content-Type', 'application/json; charset=utf-8');
    res.send('');
    return res;
  }

  @ApiOperation({
    summary: 'Checks if a username is available and appropriate',
  })
  @ApiTags(EAPITags.Apps)
  @ApiQuery({
    name: 'username',
    type: String,
    required: true,
    description: 'Username to check for availability and moderation',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Username availability status',
    type: UsernameCheckResponseDTO,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Missing or empty username parameter',
  })
  @Get('/v1/users/check-username')
  @HttpCode(HttpStatus.OK)
  @Public()
  @UseGuards(InjectOrgEnvGuard)
  async checkUsername(
    @Query('username') username: string,
    @ExtractOrgEnv() orgEnv: OrgEnv,
    @Headers('accept-language') language?: string,
  ) {
    if (!username || username.trim() === '') {
      throw new BadRequestException('Username query parameter is required.');
    }
    const usernameAvailable = await this.userService.isUsernameAvailable(username, orgEnv.id);
    const badgerResult = await this.badgerService.moderateUsername(username, language);
    const passesModeration = badgerResult !== EbadgerDecision.REJECT;

    const reasons = [
      !usernameAvailable && 'Username is already taken.',
      !passesModeration && `Username moderation judgement is '${badgerResult}'.`,
    ].filter(Boolean) as string[]; // filters out falsy values like `false`

    return {
      username,
      available: usernameAvailable && passesModeration,
      details: {
        isValid: passesModeration,
        isAvailable: usernameAvailable,
        reasons: reasons.length > 0 ? reasons : undefined,
      },
    };
  }

  @ApiOperation({
    summary: 'Registers a user',
  })
  @ApiTags(EAPITags.Apps)
  @Post('/v2/users')
  @Public()
  @HttpCode(HttpStatus.CREATED)
  @UseGuards(InjectOrgEnvGuard)
  async registerUser(
    @Body() body: UserRegisterDTO,
    @ExtractOrgEnv() orgEnv: OrgEnv,
    @Headers() headers: Record<string, string>,
    @Param('country') country?: string,
    @Headers('CloudFront-Viewer-Country') countryHeader?: string,
  ) {
    const verified = await this.talonService.verify(body.token, headers, body.parentEmail);
    if (!verified) {
      throw new ForbiddenException('Suspected Bot from Talon');
    }

    const signupCountry = body.country ?? country ?? countryHeader ?? 'ZZ';

    if (body.username) {
      const usernameAvailable = await this.userService.isUsernameAvailable(body.username, orgEnv.id);
      if (!usernameAvailable) {
        throw new ConflictException('Username is already taken');
      }
      const badgerResult = await this.badgerService.moderateUsername(body.username, body.language);
      const passesModeration = badgerResult !== EbadgerDecision.REJECT;
      if (!passesModeration) {
        throw new BadRequestException('Username rejected by moderation');
      }
    }

    const userId = await this.userService.createUserV2(body, signupCountry, orgEnv.id);
    return {
      id: userId,
    };
  }
}
