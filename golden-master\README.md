# Golden Master Tests

In this file are golden master tests which intend to ensure that the API's exposed by the wrapper
are consistent with how the ones exposed by classic.

## How it works

Tests make API requests to the wrapper and to classic to the same endpoint. The endpoints they test
are in the `config.ts` file. A .env file is used to configure the base URLs of endpoints of classic
and the wrapper.

The golden master tests can be run with or without an input file. The input file can be generated by
running

```bash
npm run golden-master:generate-input
```

This will create a `golden-master-input.json` file, which captures the response from classic. We
can use this to ensure correctness of the wrapper even when classic has been decommissioned. Running

```bash
npm run golden-master:run-with-input
```

<PERSON> then use the input file as a source of truth when running the tests, instead of hitting
classic. This means the `golden-master-input.json` file should be committed to the repo when we want
to decommission classic so that the tests still function.

Alternatively, running

```bash
npm run golden-master:run-no-input
```

Will run the tests hitting point endpoints at the same time and comparing responses.

## Configuration

Tested endpoints can be found in `config.ts`. To add a new endpoint, add an entry in the endpoints
array of the config object. Endpoints have the following properties:

- **path**: The path of the endpoint
- **method**: The HTTP method of the endpoint
- **params**: An object with the parameter values of the endpoint
- **payload**: The payload to be sent to the endpoint
- **bodyFieldConditions**: An object with the comparison conditions when comparing endpoint
  responses. I.e., whether to skip a field, check if its present, etc. By default, all fields must
  contain the same value for the test to pass.
- **skipHeaders**: An array of headers to skip comparing

There exists some global configuration:

- **defaultParams**: This property will fill any path parameters by default in a URL with the given
  value. I.e., A value of "appId" will fill any URL path with "/something/:appId"

## Workflows

In addition to testing single endpoints, you can define workflows that test a sequence of API calls.
Workflows are useful for testing complex user journeys that involve multiple API interactions.

### Defining a Workflow

To add a workflow, include an object with the following properties in the `endpoints` array:

- **name**: A descriptive name for the workflow
- **workflow**: An array of functions, each representing a step in the workflow

Each step function:

- Receives the response data from the previous step
- Returns an endpoint configuration object (same format as single endpoint tests)
- Can use data from previous responses to construct the next request

### Example

```typescript
{
  name: 'Delete user flow',
  workflow: [
    () => {
      // First step - create a user
      return {
        path: '/v2/apps/:appId/users',
        method: 'POST',
        payload: {
          // payload details
        }
      };
    },
    (previousResponse) => {
      // Second step - use data from the first response
      return {
        path: `/v2/apps/:appId/users/${previousResponse.resBody.id}`,
        method: 'DELETE'
      };
    }
  ]
}