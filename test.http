### Health Check
GET https://classic-wrapper-api.staging.kidswebservices.com/healthcheck

### OAuth
POST https://classic-wrapper-api.staging.kidswebservices.com/oauth/token
Content-Type: application/x-www-form-urlencoded
x-forwarded-host: test.com

grant_type=client_credentials&scope=app&client_id=classic-wrapper-api&client_secret=get-the-staging-secret-for-local-development

### Something Else
GET https://classic-wrapper-api.staging.kidswebservices.com/v1/countries/child-age?country=GB&dob=2015-12-12
Host: classic-wrapper-api.staging.kidswebservices.com

###

### Test Amplitude API Connection
POST https://analytics.staging.kidswebservices.com/v1/analytics/amplitude/http-api
Content-Type: application/json

{
  "api_key": "f2dfc6537d5bfddcce14344c1c956cdb",
  "events": [
    {
      "user_id": "test_user_123",
      "event_type": "connection_test",
      "time": 1703123456789,
      "event_properties": {
        "test": true,
        "source": "jetbrains_http_client",
        "timestamp": "2024-01-01T12:00:00Z"
      },
      "user_properties": {
        "repository": "freekws-classic-wrapper-backend",
        "test_environment": true
      }
    }
  ]
}

###

### Test Amplitude API Connection
POST https://analytics.staging.kidswebservices.com/v1/analytics/amplitude/http-api
Content-Type: application/json

{
  "api_key": "f2dfc6537d5bfddcce14344c1c956cdb",
  "events": [
    {
      "user_id": "777777",
      "event_type": "hasfun",
      "event_properties": { "page": "/dashboard", "referrer": "/login" },
      "user_properties": {
        "id": "AAAAAAAAA-FFFF-FFFF-FFFF-AAAAAAAAAAAAA",
        "userId": 777777,
        "user": { "email": "<EMAIL>", "status": "parent" },
        "repository": "freekws-classic-wrapper-backend"
      },
      "time": 1686678901234,
      "country": "US"
    }

  ]
}

###

### Alternative format - API key as query parameter
POST https://analytics.staging.kidswebservices.com/v1/analytics/amplitude/http-api?api_key=f2dfc6537d5bfddcce14344c1c956cdb
Content-Type: application/json

{
  "events": [
    {
      "user_id": "test_user_456",
      "event_type": "connection_test_query_param",
      "time": 1703123456789,
      "event_properties": {
        "test": true,
        "source": "jetbrains_http_client_query_param"
      }
    }
  ]
}

###

### Test with minimal payload
POST https://analytics.staging.kidswebservices.com/v1/analytics/amplitude/http-api
Content-Type: application/json

{
  "api_key": "f2dfc6537d5bfddcce14344c1c956cdb",
  "events": [
    {
      "user_id": "minimal_test",
      "event_type": "minimal_test"
    }
  ]
}

###

### Test endpoint health/connectivity (if supported)
GET https://analytics.staging.kidswebservices.com/v1/analytics/amplitude/http-api

###

### Test with Authorization header (alternative auth method)
POST https://analytics.staging.kidswebservices.com/v1/analytics/amplitude/http-api
Content-Type: application/json
Authorization: Bearer f2dfc6537d5bfddcce14344c1c956cdb

{
  "events": [
    {
      "user_id": "auth_header_test",
      "event_type": "auth_header_test",
      "time": 1703123456789
    }
  ]
}

