import { Inject, Injectable } from '@nestjs/common';
import { KeycloakService } from '@superawesome/freekws-auth-library';
import { NestJsClient } from '@superawesome/freekws-clients-nestjs';
import {
  EMemberRole,
  FAMILY_SERVICE_CLIENT_INJECT_KEY,
  FamilyGroupDTO,
  familyServicePlugin,
} from '@superawesome/freekws-family-service-common';

import { IAppOauthClientCredentials } from '../../../app/types';
import { ClientKeycloakService } from '../keycloak/client-keycloak.service';
import { KEYCLOAK_PROVIDER } from '../keycloak/keycloak.module';

@Injectable()
export class FamilyGroupService {
  constructor(
    private readonly clientKeycloakService: ClientKeycloakService,
    @Inject(FAMILY_SERVICE_CLIENT_INJECT_KEY)
    private readonly client: NestJsClient<typeof familyServicePlugin>,
    @Inject(KEYCLOAK_PROVIDER)
    private readonly keycloakClient: KeycloakService,
  ) {}

  async isUserManagedByFamily(userId: number, credentials: IAppOauthClientCredentials): Promise<boolean> {
    const familyGroup = await this.getUserFamily(userId, credentials);

    if (!familyGroup) {
      return false;
    }

    return familyGroup.members.some((member) => member.role === EMemberRole.Manager);
  }

  async getParentInfo(userId: number, credentials: IAppOauthClientCredentials) {
    const familyGroup = await this.getUserFamily(userId, credentials);
    if (!familyGroup) {
      return;
    }

    const parent = familyGroup.members.find((member) => member.role === EMemberRole.Manager);
    if (!parent) {
      return;
    }

    return {
      id: parent.id,
      email: parent.email,
      familyGroupId: parent.familyGroupId,
    };
  }

  async getUserFamily(userId: number, credentials: IAppOauthClientCredentials): Promise<FamilyGroupDTO | void> {
    const token = await this.clientKeycloakService.getClientToken(credentials);

    const {
      data: {
        response: { familyGroups },
      },
    } = await this.client.getModule('familyGroup').listFamilyGroups(
      {
        query: { userId: `${userId}` },
      },
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      },
    );

    if (familyGroups.length > 0) {
      return familyGroups[0];
    }
  }

  async getGuardianLinks(userId: number) {
    const axoisResponse = await this.client.getModule('internalAdminFamilyGroup').findGuardianLinks(
      {
        query: { userId: `${userId}` },
      },
      {
        headers: {
          Authorization: `Bearer ${await this.keycloakClient.getUpToDateServiceAccessToken()}`,
        },
      },
    );

    return axoisResponse.data.response;
  }
}
