import { BadRequestException } from '@nestjs/common';
import { TestingModule } from '@nestjs/testing';
import { EOAuthGrantType, EOAuthScope, OAuthTokenDTO } from '@superawesome/freekws-classic-wrapper-common';

import { OAuthTokenHandlers } from './oauth-token-handlers';
import { OauthService, secondsInADay } from './oauth.service';
import { RefreshToken } from './refresh-token.entity';
import { TJWT } from './types';
import { IAppOauthClient } from '../app/types';
import { Testing } from '../common/utils';
import { OrgEnv } from '../org-env/org-env.entity';
import { UserService } from '../user/user.service';

describe('OAuthTokenHandlers', () => {
  let handlers: OAuthTokenHandlers;
  let userService: UserService;
  let oAuthService: OauthService;

  beforeEach(async () => {
    const module: TestingModule = await Testing.createModule({
      providers: [OAuthTokenHandlers],
    });

    handlers = module.get<OAuthTokenHandlers>(OAuthTokenHandlers);
    oAuthService = module.get<OauthService>(OauthService);
    userService = module.get<UserService>(UserService);
  });

  const accessToken = 'access-token';
  const refreshToken = 'refresh-token';
  const newAccessTokenFromRefreshToken = 'new-access-token-from-refresh-token';
  const userScopedToken = 'user-scoped-token';

  beforeEach(() => {
    jest.spyOn(oAuthService, 'getAccessToken').mockResolvedValue(accessToken);
    jest.spyOn(oAuthService, 'getRefreshToken').mockResolvedValue({ token: refreshToken } as RefreshToken);
    jest.spyOn(oAuthService, 'getAccessTokenFromRefreshToken').mockResolvedValue(newAccessTokenFromRefreshToken);
    jest.spyOn(oAuthService, 'createUserTokenFromUsername').mockResolvedValue(userScopedToken);
    jest.spyOn(userService, 'verifyPassword').mockResolvedValue(false);
  });

  describe('handleRefreshToken', () => {
    it('should generate new access and refresh tokens when valid refresh_token is provided', async () => {
      const getAccessTokenFromRefreshTokenSpy = jest.spyOn(oAuthService, 'getAccessTokenFromRefreshToken');
      const getRefreshTokenSpy = jest.spyOn(oAuthService, 'getRefreshToken');

      const data: OAuthTokenDTO = {
        grant_type: EOAuthGrantType.REFRESH_TOKEN,
        refresh_token: 'valid-refresh-token',
        client_id: 'test-client-id',
        client_secret: 'test-client-secret',
      };

      const client: IAppOauthClient = {
        appId: 1,
        orgEnvId: 'test-org-env-id',
        clientId: 'test-client-id',
      };

      const result = await handlers.handleRefreshToken(data, client);

      expect(getAccessTokenFromRefreshTokenSpy).toHaveBeenCalledWith('valid-refresh-token', data, client);
      expect(getRefreshTokenSpy).toHaveBeenCalledWith(data.scope, client);
      expect(result).toEqual({
        token_type: 'bearer',
        access_token: newAccessTokenFromRefreshToken,
        refresh_token: refreshToken,
        expires_in: secondsInADay,
      });
    });

    it('should throw BadRequestException when refresh_token is missing', async () => {
      const getAccessTokenFromRefreshTokenSpy = jest.spyOn(oAuthService, 'getAccessTokenFromRefreshToken');
      const getRefreshTokenSpy = jest.spyOn(oAuthService, 'getRefreshToken');

      const data: OAuthTokenDTO = {
        grant_type: EOAuthGrantType.REFRESH_TOKEN,
        client_id: 'test-client-id',
        client_secret: 'test-client-secret',
      };

      const client: IAppOauthClient = {
        appId: 1,
        orgEnvId: 'test-org-env-id',
        clientId: 'test-client-id',
      };

      await expect(handlers.handleRefreshToken(data, client)).rejects.toThrow(
        new BadRequestException('refresh_token is required for refresh_token grant_type'),
      );

      expect(getAccessTokenFromRefreshTokenSpy).not.toHaveBeenCalled();
      expect(getRefreshTokenSpy).not.toHaveBeenCalled();
    });
  });

  describe('handleClientCredentials', () => {
    it('should generate tokens for client_credentials grant type', async () => {
      const getAccessTokenSpy = jest.spyOn(oAuthService, 'getAccessToken');
      const getRefreshTokenSpy = jest.spyOn(oAuthService, 'getRefreshToken');

      const data: OAuthTokenDTO = {
        grant_type: EOAuthGrantType.CLIENT_CREDENTIALS,
        client_id: 'test-client-id',
        client_secret: 'test-client-secret',
        scope: EOAuthScope.APP,
      };

      const client: IAppOauthClient = {
        appId: 1,
        orgEnvId: 'test-org-env-id',
        clientId: 'test-client-id',
      };

      const result = await handlers.handleClientCredentials(data, client);

      expect(getAccessTokenSpy).toHaveBeenCalledWith(data, client);
      expect(getRefreshTokenSpy).toHaveBeenCalledWith(data.scope, client);
      expect(result).toEqual({
        token_type: 'bearer',
        access_token: accessToken,
        refresh_token: refreshToken,
        expires_in: secondsInADay,
      });
    });
  });

  describe('handlePassword', () => {
    it('should generate tokens for password grant type with valid credentials', async () => {
      jest.spyOn(userService, 'verifyPassword').mockResolvedValueOnce(true);
      const createUserTokenSpy = jest.spyOn(oAuthService, 'createUserTokenFromUsername');
      const getRefreshTokenSpy = jest.spyOn(oAuthService, 'getRefreshToken');

      const data: OAuthTokenDTO = {
        grant_type: EOAuthGrantType.PASSWORD,
        client_id: 'test-client-id',
        client_secret: 'test-client-secret',
        username: 'testuser',
        password: 'password123',
      };

      const client: IAppOauthClient = {
        appId: 1,
        orgEnvId: 'test-org-env-id',
        clientId: 'test-client-id',
      };

      const result = await handlers.handlePassword(data, client);

      expect(userService.verifyPassword).toHaveBeenCalledWith('testuser', 'password123', 'test-org-env-id');
      expect(createUserTokenSpy).toHaveBeenCalledWith(client, 'testuser');
      expect(getRefreshTokenSpy).toHaveBeenCalledWith(EOAuthScope.USER, client, 'testuser');
      expect(result).toEqual({
        token_type: 'bearer',
        access_token: userScopedToken,
        refresh_token: refreshToken,
        expires_in: secondsInADay,
      });
    });

    it('should throw BadRequestException for password grant type with invalid credentials', async () => {
      jest.spyOn(userService, 'verifyPassword').mockResolvedValueOnce(false);
      const createUserTokenSpy = jest.spyOn(oAuthService, 'createUserTokenFromUsername');

      const data: OAuthTokenDTO = {
        grant_type: EOAuthGrantType.PASSWORD,
        client_id: 'test-client-id',
        client_secret: 'test-client-secret',
        username: 'testuser',
        password: 'wrong-password',
      };

      const client: IAppOauthClient = {
        appId: 1,
        orgEnvId: 'test-org-env-id',
        clientId: 'test-client-id',
      };

      await expect(handlers.handlePassword(data, client)).rejects.toThrow(
        new BadRequestException('Invalid username or password'),
      );

      expect(userService.verifyPassword).toHaveBeenCalledWith('testuser', 'wrong-password', 'test-org-env-id');
      expect(createUserTokenSpy).not.toHaveBeenCalled();
    });

    it('should throw BadRequestException for password grant type with missing credentials', async () => {
      const data: OAuthTokenDTO = {
        grant_type: EOAuthGrantType.PASSWORD,
        client_id: 'test-client-id',
        client_secret: 'test-client-secret',
        // Missing username and password
      };

      const client: IAppOauthClient = {
        appId: 1,
        orgEnvId: 'test-org-env-id',
        clientId: 'test-client-id',
      };

      await expect(handlers.handlePassword(data, client)).rejects.toThrow(
        new BadRequestException('Username and password are both required'),
      );

      expect(userService.verifyPassword).not.toHaveBeenCalled();
    });
  });

  describe('handleAuthorizationCode', () => {
    it('should throw BadRequestException when code is missing', async () => {
      const data: OAuthTokenDTO = {
        grant_type: EOAuthGrantType.AUTHORIZATION_CODE,
        client_id: 'test-client-id',
        client_secret: 'test-client-secret',
        redirect_uri: 'https://example.com/callback',
      };

      const client: IAppOauthClient = {
        appId: 1,
        orgEnvId: 'test-org-env-id',
        clientId: 'test-client-id',
      };

      const orgEnv = { id: 'test-org-env-id' } as OrgEnv;

      await expect(handlers.handleAuthorizationCode(data, client, orgEnv)).rejects.toThrow(
        new BadRequestException('Missing code parameter'),
      );
    });

    it('should throw BadRequestException when redirect_uri is missing', async () => {
      const data: OAuthTokenDTO = {
        grant_type: EOAuthGrantType.AUTHORIZATION_CODE,
        client_id: 'test-client-id',
        client_secret: 'test-client-secret',
        code: 'valid-code',
      };

      const client: IAppOauthClient = {
        appId: 1,
        orgEnvId: 'test-org-env-id',
        clientId: 'test-client-id',
      };

      const orgEnv = { id: 'test-org-env-id' } as OrgEnv;

      await expect(handlers.handleAuthorizationCode(data, client, orgEnv)).rejects.toThrow(
        new BadRequestException('Missing redirect_uri parameter'),
      );
    });

    it('should generate tokens when code verification succeeds', async () => {
      const mockCodeJwt = { userId: 123 } as TJWT;
      jest.spyOn(oAuthService, 'getJwt').mockResolvedValue(mockCodeJwt);
      jest.spyOn(oAuthService, 'verifyCode').mockResolvedValue(true);
      const createUserTokenSpy = jest
        .spyOn(oAuthService, 'createUserTokenFromUserId')
        .mockResolvedValue(userScopedToken);
      const getRefreshTokenSpy = jest
        .spyOn(oAuthService, 'getRefreshTokenWithUserId')
        .mockResolvedValue({ token: refreshToken } as RefreshToken);

      const data: OAuthTokenDTO = {
        grant_type: EOAuthGrantType.AUTHORIZATION_CODE,
        client_id: 'test-client-id',
        client_secret: 'test-client-secret',
        code: 'valid-code',
        redirect_uri: 'https://example.com/callback',
      };

      const client: IAppOauthClient = {
        appId: 1,
        orgEnvId: 'test-org-env-id',
        clientId: 'test-client-id',
      };

      const orgEnv = { id: 'test-org-env-id' } as OrgEnv;

      const result = await handlers.handleAuthorizationCode(data, client, orgEnv);

      expect(oAuthService.getJwt).toHaveBeenCalledWith('valid-code', 'test-org-env-id');
      expect(oAuthService.verifyCode).toHaveBeenCalledWith(
        {
          codeJwt: mockCodeJwt,
          redirect_uri: 'https://example.com/callback',
          clientId: 'test-client-id',
          appId: 1,
          codeVerifier: undefined,
        },
        'test-org-env-id',
      );
      expect(createUserTokenSpy).toHaveBeenCalledWith(123, client);
      expect(getRefreshTokenSpy).toHaveBeenCalledWith(123, client);
      expect(result).toEqual({
        token_type: 'bearer',
        access_token: userScopedToken,
        refresh_token: refreshToken,
        expires_in: secondsInADay,
      });
    });

    it('should throw BadRequestException when code verification fails', async () => {
      const mockCodeJwt = { userId: 123 } as TJWT;
      jest.spyOn(oAuthService, 'getJwt').mockResolvedValue(mockCodeJwt);
      jest.spyOn(oAuthService, 'verifyCode').mockResolvedValue(false);
      const createUserTokenSpy = jest.spyOn(oAuthService, 'createUserTokenFromUserId');

      const data: OAuthTokenDTO = {
        grant_type: EOAuthGrantType.AUTHORIZATION_CODE,
        client_id: 'test-client-id',
        client_secret: 'test-client-secret',
        code: 'invalid-code',
        redirect_uri: 'https://example.com/callback',
      };

      const client: IAppOauthClient = {
        appId: 1,
        orgEnvId: 'test-org-env-id',
        clientId: 'test-client-id',
      };

      const orgEnv = { id: 'test-org-env-id' } as OrgEnv;

      await expect(handlers.handleAuthorizationCode(data, client, orgEnv)).rejects.toThrow(
        new BadRequestException('Code verification failed'),
      );

      expect(createUserTokenSpy).not.toHaveBeenCalled();
    });
  });
});
