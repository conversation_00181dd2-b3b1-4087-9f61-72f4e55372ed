import { Consumer, EachMessagePayload, Kafka, logLevel } from 'kafkajs';

export class KafkaTestHelper {
  private readonly kafka: Kafka;
  private readonly consumer: Consumer;
  private readonly topic: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  private messages: Record<string, any>[] = [];
  private readonly logger: Console;

  constructor(host: string, topic: string) {
    this.topic = topic;
    this.logger = console;
    this.kafka = new Kafka({
      clientId: 'e2e-test-client',
      brokers: host.split(','),
      logCreator: () => {
        return ({ level, log }) => {
          // Skip logging for group coordinator errors
          if (
            log.message?.includes('Response GroupCoordinator') ||
            log.message?.includes('Restarting the consumer in') ||
            log.message?.includes('Crash: KafkaJSNumberOfRetriesExceeded')
          ) {
            return;
          }

          if (level === logLevel.ERROR) {
            console.error(log.message);
          }
        };
      },
    });
    this.consumer = this.kafka.consumer({
      groupId: 'e2e-test-group',
    });
  }

  async connect(): Promise<void> {
    await this.createTopicIfNotExists();
    await this.consumer.connect();
    await this.consumer.subscribe({ topic: this.topic, fromBeginning: true });
    await this.sleep(500);
    await this.consumer.run({
      eachMessage: async ({ message }: EachMessagePayload) => {
        const messageString = message.value?.toString();
        if (!messageString) {
          this.logger.log('Message value was undefined.', message);
          return;
        }
        this.messages.push(JSON.parse(messageString));
      },
    });
  }

  private sleep(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  private async createTopicIfNotExists(): Promise<void> {
    const admin = this.kafka.admin();
    await admin.connect();

    const topics = await admin.listTopics();
    if (!topics.includes(this.topic)) {
      this.logger.log(`Topic "${this.topic}" does not exist. Creating it...`);
      await admin.createTopics({
        topics: [
          {
            topic: this.topic,
          },
        ],
      });
    }

    await admin.disconnect();
  }

  async disconnect(): Promise<void> {
    await this.consumer.disconnect();
  }

  async getNextMessage(timeoutMs = 5000) {
    const startTime = Date.now();

    while (Date.now() - startTime < timeoutMs) {
      if (this.messages.length > 0) {
        const result = this.messages.shift();
        if (result) {
          return result;
        } else {
          throw new Error('Unexpected undefined message.');
        }
      }
      await new Promise((resolve) => setTimeout(resolve, 100));
    }

    throw new Error(`No message received after ${timeoutMs}ms`);
  }

  clearMessages(): void {
    this.messages = [];
  }
}
