import { Test, TestingModule } from '@nestjs/testing';
import { FastifyRequest } from 'fastify';

import { CountriesController } from './countries.controller';
import { CountriesService } from './countries.service';
import { OrgEnvService } from '../org-env/org-env.service';

const mockCountriesService = {
  childAge: jest.fn(),
};

const mockOrgEnvRepo = {
  getOrgEnvByHost: jest.fn(),
};

describe('CountriesController', () => {
  let controller: CountriesController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [CountriesController],
      providers: [
        { provide: CountriesService, useValue: mockCountriesService },
        { provide: OrgEnvService, useValue: mockOrgEnvRepo },
      ],
    }).compile();

    controller = module.get<CountriesController>(CountriesController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getChildAge', () => {
    it('should return child age config', async () => {
      mockCountriesService.childAge.mockResolvedValueOnce({
        age: 14,
        consentAgeForCountry: 13,
        country: 'US',
        isMinor: false,
      });
      await expect(
        controller.getChildAge({ dob: '2010-10-10' }, { ip: '127.0.0.1' } as FastifyRequest, {
          clientId: '',
          clientSecret: '',
          id: '',
          orgId: '',
          host: '',
          apps: [],
          users: [],
          jwks: [],
          webhooks: [],
        }),
      ).resolves.toEqual({
        age: 14,
        consentAgeForCountry: 13,
        country: 'US',
        isMinor: false,
      });
    });
  });
});
