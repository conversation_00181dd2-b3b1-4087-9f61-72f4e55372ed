import { OAuthTokenDTO } from '@superawesome/freekws-classic-wrapper-common';
import { FastifyRequest, RawRequestDefaultExpression, RawServerDefault, RouteGenericInterface } from 'fastify';

import { IAppOauthClient } from '../../../app/types';

export type OauthFastifyRequest = FastifyRequest<
  RouteGenericInterface & { Body: OAuthTokenDTO },
  RawServerDefault,
  RawRequestDefaultExpression & { oauthClient: IAppOauthClient }
>;
