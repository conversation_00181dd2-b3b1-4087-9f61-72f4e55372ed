import { forwardRef, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { Activation } from './activation.entity';
import { BadgerModule } from './badger.module';
import { TalonModule } from './talon.module';
import { UserController } from './user.controller';
import { User } from './user.entity';
import { UserService } from './user.service';
import { App } from '../app/app.entity';
import { AppModule } from '../app/app.module';
import { AgeGateModule } from '../common/services/age-gate/age-gate.module';
import { AnalyticModule } from '../common/services/analytic/analytic.module';
import { FamilyGroupModule } from '../common/services/family-group/family-group.module';
import { PreVerificationModule } from '../common/services/pre-verification/pre-verification.module';
import { SettingsModule } from '../common/services/settings/settings.module';
import { JwkModule } from '../oauth/jwk.module';
import { OrgEnvModule } from '../org-env/org-env.module';
import { WebhookModule } from '../webhook/webhook.module';

@Module({
  imports: [
    WebhookModule,
    TypeOrmModule.forFeature([User, Activation, App]),
    FamilyGroupModule,
    PreVerificationModule,
    SettingsModule,
    AnalyticModule,
    forwardRef(() => AppModule),
    JwkModule,
    forwardRef(() => OrgEnvModule),
    BadgerModule,
    TalonModule,
    AgeGateModule,
  ],
  providers: [UserService],
  exports: [UserService],
  controllers: [UserController],
})
export class UserModule {}
