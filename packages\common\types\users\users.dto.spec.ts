import { plainToInstance } from 'class-transformer';
import { validate } from 'class-validator';

import { UserCreateDTO } from './users.dto';

describe('Users DTOs', () => {
  describe('test UserCreateDTO', () => {
    it('should validate correct data', async () => {
      const result = plainToInstance(UserCreateDTO, {
        country: 'US',
        dateOfBirth: '2010-10-10',
        parentEmail: '<EMAIL>',
        permissions: ['chat.voice'],
        language: 'en',
      });

      await expect(validate(result)).resolves.toEqual([]);
    });

    it('should define language by country', async () => {
      const result = plainToInstance(UserCreateDTO, {
        country: 'US',
        dateOfBirth: '2010-10-10',
        parentEmail: '<EMAIL>',
        permissions: ['chat.voice'],
      });

      await expect(validate(result)).resolves.toEqual([]);
      expect(result.language).toEqual('en');
    });

    it('should define language by country (NE)', async () => {
      const result = plainToInstance(UserCreateDTO, {
        country: 'NE',
        dateOfBirth: '2010-10-10',
        parentEmail: '<EMAIL>',
        permissions: ['chat.voice'],
      });

      await expect(validate(result)).resolves.toEqual([]);
      expect(result.language).toEqual('fr');
    });
  });
});
