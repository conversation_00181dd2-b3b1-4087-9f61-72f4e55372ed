import { AxiosError } from 'axios';

export enum EAPITags {
  Internal = 'Internal',
  Countries = 'Countries',
  Oauth = 'Oauth',
  Apps = 'Apps',
  Webhooks = 'Webhooks',
  InternalAdmin = 'Internal Only Admin API',
  Events = 'Events',
}

export const isAxiosError = <T>(err: Error): err is AxiosError<T> => {
  return 'isAxiosError' in err && !!err.isAxiosError;
};

export const CACHEABLE_BRANDING_TTL_SECS = 2 * 3600 + Math.ceil(Math.random() * 3600);
export const CACHEABLE_CUSTOMCOPY_TTL_SECS = 2 * 3600 + Math.ceil(Math.random() * 3600);
