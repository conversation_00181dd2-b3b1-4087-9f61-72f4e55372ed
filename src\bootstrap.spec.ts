import { ValidationPipe } from '@nestjs/common';
import { NestFastifyApplication } from '@nestjs/platform-fastify';
import * as interceptors from '@superawesome/freekws-nestjs-http-interceptors';

import { bootstrap } from './bootstrap';

jest.mock('@nestjs/common', () => ({
  ...jest.requireActual('@nestjs/common'),
  ValidationPipe: jest.fn(),
}));
const ValidationPipeMock = ValidationPipe as unknown as jest.Mock;

describe('bootstrap()', () => {
  const app = {
    useGlobalPipes: jest.fn(),
    listen: jest.fn(),
    enableCors: jest.fn(),
    get: jest.fn(),
    select: jest.fn(),
    useGlobalInterceptors: jest.fn(),
  };
  const mockHttpConfig = {
    cors: 'cors-config',
    port: 12345,
  };
  let startHttpServerSpy: jest.SpyInstance;

  const mockConfigService = {
    getHttpConfig: () => mockHttpConfig,
  };
  const mockMetricsService = {
    increment: jest.fn(),
  };

  beforeEach(() => {
    startHttpServerSpy = jest.spyOn(interceptors, 'startHttpServer').mockResolvedValue();
    app.get.mockImplementation((className) => {
      switch (className.name) {
        case 'ConfigService': {
          return mockConfigService;
        }
        case 'MetricsService': {
          return mockMetricsService;
        }
      }
    });
  });
  afterEach(() => {
    jest.resetAllMocks();
  });

  it('should call startHttpService', async () => {
    await bootstrap(app as unknown as NestFastifyApplication);
    expect(startHttpServerSpy).toHaveBeenCalledWith(app, {
      port: mockHttpConfig.port,
      globalPipes: [{}],
      corsOptions: mockHttpConfig.cors,
      trustCloudfrontProxy: false,
      requestIDPassthrough: true,
      requestIdHeader: 'x-epic-correlation-id',
      transformResponse: false,
      filterExceptions: false,
    });
  });

  it('should not listen when required', async () => {
    await bootstrap(app as unknown as NestFastifyApplication, false);
    expect(startHttpServerSpy).not.toHaveBeenCalled();
  });

  it('should start Validation pipe with whitelist and transform options', async () => {
    await bootstrap(app as unknown as NestFastifyApplication);
    expect(ValidationPipeMock).toHaveBeenCalledWith({ whitelist: true, transform: true });
  });
});
