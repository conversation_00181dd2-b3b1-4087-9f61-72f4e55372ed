import { INestApplication } from '@nestjs/common';
import { getDataSourceToken } from '@nestjs/typeorm';
import request from 'supertest';
import { DataSource, Repository } from 'typeorm';

import { App } from '../../src/app/app.entity';
import { JWK } from '../../src/oauth/jwk.entity';
import { JwkRepository } from '../../src/oauth/jwk.repository';
import { Utils } from '../utils';

function randomString() {
  return Math.random().toString().replace('.', '');
}

describe('JwkController (e2e)', () => {
  let app: INestApplication;
  let dataSource: DataSource;
  let jwkRepository: JwkRepository;
  let jwkTypeormRepository: Repository<JWK>;
  let testApp: App;

  beforeAll(async () => {
    app = await Utils.createTestServer();
    dataSource = app.get<DataSource>(getDataSourceToken());
    jwkRepository = app.get<JwkRepository>(JwkRepository);
    jwkTypeormRepository = dataSource.getRepository(JWK);

    await Utils.cleanDb();
    const fixtures = await Utils.loadFixtures();
    testApp = fixtures.App.find((app) => app.name === 'test-app') as App;

    // Set up any necessary mocks for other services
    Utils.mockAgeGateAPI();
    Utils.mockSettingsBackendAPI();
  });

  afterAll(async () => {
    await Utils.stopTestServer(app);
    jest.clearAllMocks();
  });

  describe('GET /v1/jwks', () => {
    it('should return a valid JWK set', async () => {
      const testJwkBase = {
        algorithm: 'ES256',
        keyType: 'RSA',
        use: 'sig',
        modulus: 'test-modulus',
        exponent: 'AQAB',
        keyId: 'test-kid-' + Date.now(),
        orgEnvId: testApp.orgEnvId,
        certThumbprint: 'test-thumbprint',
        publicPem: 'pem',
        privatePem: 'private-pem',
      } as JWK;

      for (let i = 0; i < 50; i++) {
        const testJwk = {
          ...testJwkBase,
          keyId: `test-kid-${i}`,
          modulus: randomString(),
          exponent: randomString(),
          createdAt: new Date(),
        };
        await jwkTypeormRepository.save(testJwk);
      }

      const { body } = await request(app.getHttpServer())
        .get('/v1/jwks')
        .set('x-forwarded-host', testApp.orgEnv.host)
        .expect(200);

      expect(body).toHaveProperty('keys');
      expect(body.keys).toHaveLength(20);

      const jwks = await jwkRepository.getRecentJwks();

      // Match each JWK in the response to its corresponding repository entity
      for (const jwk of jwks) {
        const matchingKey = body.keys.find((key: { kid: string }) => key.kid === jwk.keyId);
        expect(matchingKey).toBeDefined();
        expect(matchingKey).toEqual({
          alg: jwk.algorithm,
          kty: jwk.keyType,
          use: jwk.use,
          n: jwk.modulus,
          e: jwk.exponent,
          kid: jwk.keyId,
        });
      }
    });

    it('should return an empty keys array when no JWKs exist', async () => {
      // Mock the repository to return an empty array
      const originalMethod = jwkRepository.getRecentJwks;
      jwkRepository.getRecentJwks = jest.fn().mockResolvedValue([]);

      try {
        const { body } = await request(app.getHttpServer())
          .get('/v1/jwks')
          .set('x-forwarded-host', testApp.orgEnv.host)
          .expect(200);

        expect(body).toEqual({ keys: [] });
      } finally {
        jwkRepository.getRecentJwks = originalMethod;
      }
    });
  });
});
