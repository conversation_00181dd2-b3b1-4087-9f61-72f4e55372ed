import { Test, TestingModule } from '@nestjs/testing';
import { MetricsService } from '@superawesome/freekws-metrics-nestjs-service';

import { CountriesService } from './countries.service';
import { AgeGateService } from '../common/services/age-gate/age-gate.service';

const mockAgeGateService = {
  getConsentAgeForCountry: jest.fn(),
};

const mockMetricsService = {
  increment: jest.fn(),
};

describe('CountriesService', () => {
  let service: CountriesService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CountriesService,
        { provide: AgeGateService, useValue: mockAgeGateService },
        { provide: MetricsService, useValue: mockMetricsService },
      ],
    }).compile();

    service = module.get<CountriesService>(CountriesService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('check childAge method', () => {
    it('should return mapped agegate response', async () => {
      expect(true).toBeTruthy();
    });
  });

  describe('childAge', () => {
    it('should return mapped agegate servce response', async () => {
      mockAgeGateService.getConsentAgeForCountry.mockResolvedValueOnce({
        consentAge: 13,
        country: 'US',
        underAgeOfDigitalConsent: false,
        userAge: 14,
      });
      await expect(
        service.childAge({ dob: '2010-10-10', country: 'US' }, '127.0.0.1', { clientId: '', secret: '' }),
      ).resolves.toEqual({
        age: 14,
        consentAgeForCountry: 13,
        country: 'US',
        isMinor: false,
      });
    });

    it('should return mapped agegate servce response without consent', async () => {
      mockAgeGateService.getConsentAgeForCountry.mockResolvedValueOnce({
        underAgeOfDigitalConsent: false,
        userAge: 14,
      });
      await expect(
        service.childAge({ dob: '2010-10-10', country: 'US' }, '127.0.0.1', { clientId: '', secret: '' }),
      ).resolves.toEqual({
        age: 14,
        isMinor: false,
      });
    });
  });
});
