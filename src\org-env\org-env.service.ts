import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Cacheable, paramsExtractor } from '@superawesome/freekws-cache-decorator';
import { FastifyRequest } from 'fastify';
import { Span } from 'nestjs-ddtrace';
import { Repository } from 'typeorm';

import { OrgEnv } from './org-env.entity';

@Injectable()
@Span()
export class OrgEnvService {
  constructor(@InjectRepository(OrgEnv) private readonly orgEnv: Repository<OrgEnv>) {}

  @Cacheable(600, 'getOrgEnvByHost', paramsExtractor(0))
  async getOrgEnvByHost(host: string): Promise<OrgEnv | null> {
    return this.orgEnv.findOneBy({ host });
  }

  async getById(id: string): Promise<OrgEnv> {
    return this.orgEnv.findOneByOrFail({ id });
  }

  async getOrgEnvFromRequest(request: FastifyRequest) {
    const host = request.headers['x-forwarded-host'] as string | undefined;
    if (!host) {
      throw new BadRequestException('"x-forwarded-host" header is missing or empty.');
    }

    const orgEnv = await this.getOrgEnvByHost(host);
    if (!orgEnv) {
      throw new BadRequestException('Could not handle request from unknown domain.');
    }
    return orgEnv;
  }
}
