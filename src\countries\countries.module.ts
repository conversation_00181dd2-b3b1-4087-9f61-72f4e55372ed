import { Module } from '@nestjs/common';

import { CountriesController } from './countries.controller';
import { CountriesService } from './countries.service';
import { AgeGateModule } from '../common/services/age-gate/age-gate.module';
import { OrgEnvModule } from '../org-env/org-env.module';

@Module({
  imports: [AgeGateModule, OrgEnvModule],
  controllers: [CountriesController],
  providers: [CountriesService],
})
export class CountriesModule {}
