defaultEnvironment: &defaultEnvironment
  AUDIT_KAFKA_TOPIC: freekws.eu-west-1.audit
  CURRENT_REGION: eu-west-1
  DD_ENV: staging
  DD_PROFILING_ENABLED: false
  DD_RUNTIME_METRICS_ENABLED: false
  DD_TRACE_AGENT_URL: http://**********:8126
  DD_TRACE_ENABLED: false
  DEV_PORTAL_URL: https://portal-api-internal.staging.kidswebservices.com
  DATABASE_READ_URL: vault:superawesome/kws/staging/freekws-classic-wrapper-backend/DATABASE_READ_URL?version=1
  DATABASE_URL: vault:superawesome/kws/staging/freekws-classic-wrapper-backend/DATABASE_URL?version=1
  ENVIRONMENT: staging
  HTTP_CORS_ORIGIN: "*"
  NODE_ENV: production
  KAFKA_HOST: b-1.kafka-kws-v2-staging-f.dmph1q.c5.kafka.eu-west-1.amazonaws.com:9092,b-3.kafka-kws-v2-staging-f.dmph1q.c5.kafka.eu-west-1.amazonaws.com:9092,b-2.kafka-kws-v2-staging-f.dmph1q.c5.kafka.eu-west-1.amazonaws.com:9092
  KAFKA_OUT_OF_ORDER_TOPIC: freekws.eu-west-1.out-of-order
  KEYCLOAK_ADDITIONAL_TRUSTED_ISSUERS: "https://auth.staging.kidswebservices.com/auth/realms/kws,https://auth2.staging.kidswebservices.com/auth/realms/kws"
  KEYCLOAK_BASE_URL: https://keycloak-internal.staging.kidswebservices.com/auth
  KEYCLOAK_CLIENT_SECRET: vault:superawesome/kws/staging/freekws-classic-wrapper-backend/KEYCLOAK_CLIENT_SECRET?version=1
  KEYCLOAK_REALM_URL: https://keycloak-internal.staging.kidswebservices.com/auth/realms/kws
  KEYCLOAK_TIMEOUT_MS: 30000
  AGE_GATE_SERVICE_URL: https://agegate-api-internal.staging.kidswebservices.com
  SETTINGS_BACKEND_SERVICE_URL: https://settings-api-internal.staging.kidswebservices.com
  FAMILY_SERVICE_SERVICE_URL: https://family-service-api.staging.kidswebservices.com
  PREVERIFICATION_SERVICE_SERVICE_URL: https://preverification-service-internal.staging.kidswebservices.com
  ENCRYPTION_SECRET_KEYS: vault:superawesome/kws/staging/freekws-shared/ENCRYPTION_SECRET_KEYS?version=3
  ENCRYPTION_SECRET_VERSION: vault:superawesome/kws/staging/freekws-shared/ENCRYPTION_SECRET_VERSION?version=1
  ANALYTICS_SERVICE_URL: https://events2.v1staging.kidswebservices.com
  ANALYTICS_SERVICE_AUTH_STRING: vault:superawesome/kws/staging/freekws-classic-wrapper-backend/ANALYTICS_SERVICE_AUTH_STRING
  APP_WEBHOOKS_KAFKA_TOPIC: freekws.eu-west-1.webhook
  CALLBACK_API_URL: https://callback-service-internal.staging.kidswebservices.com
  BADGER_API_KEY: vault:superawesome/kws/staging/freekws-classic-wrapper-backend/BADGER_API_KEY
  TALON_FLOW_ID: "kws_registration_dev"
  TALON_API_KEY: vault:superawesome/kws/staging/freekws-classic-wrapper-backend/TALON_API_KEY
  APP_WEBHOOKS_SEND_WEBHOOKS: vault:superawesome/kws/staging/freekws-classic-wrapper-backend/APP_WEBHOOKS_SEND_WEBHOOKS
  EMAIL_KAFKA_TOPIC: freekws.eu-west-1.email
  EMAIL_SA_DEFAULT_HEADER_SRC: https://branding.staging.kidswebservices.com/defaultBranding/very-excellent-games-purple.png
  EMAIL_SA_LOGO_SRC: https://branding.staging.kidswebservices.com/defaultBranding/footer-logo-default-light-hq.png
  BRANDING_API_URL: https://branding-service-internal.staging.kidswebservices.com
  AMPLITUDE_API_KEY: vault:superawesome/kws/staging/freekws-shared/AMPLITUDE_API_KEY?version=1
  AMPLITUDE_SERVER_URL: https://analytics.staging.kidswebservices.com/v1/analytics/amplitude/http-api
freekws-classic-wrapper-backend-sa-deployment:
  autoscaling:
    replicas:
      fallback: 2
      max: 100
      min: 2
  containers:
    freekws-classic-wrapper-backend-api:
      env:
        <<: *defaultEnvironment
        DD_SERVICE: freekws-classic-wrapper-backend
      ports:
        - class: alb
          containerPort: 80
          customWebACL: 1
          groupName: freekws-classic-wrapper-backend-internal
          healthcheck: /healthcheck
          hosts:
            - classic-wrapper-api-internal.staging.kidswebservices.com
            - classic-wrapper-api-eu-west-1-internal.staging.kidswebservices.com
          name: internal
          path: /*
          scheme: internal
          servicePort: 80
          type: Ingress
          annotations:
            alb.ingress.kubernetes.io/target-group-attributes: slow_start.duration_seconds=30
        - class: alb
          containerPort: 80
          customWebACL: 1
          groupName: freekws-classic-wrapper-backend
          healthcheck: /healthcheck
          hosts:
            - classic-wrapper-api.staging.kidswebservices.com
            - classic-wrapper-api-eu-west-1.staging.kidswebservices.com
            - classic-wrapper-eu.staging.kidswebservices.com
          name: http
          path: /*
          scheme: internet-facing
          servicePort: 80
          type: Ingress
          annotations:
            alb.ingress.kubernetes.io/target-group-attributes: load_balancing.algorithm.type=least_outstanding_requests
sa-overprovisioner:
  containers:
    - name: freekws-classic-wrapper-backend
      image:
        name: artifacts.ol.epicgames.net/supawe-kws-docker/superawesomeltd/freekws-classic-wrapper-backend
      requests:
        cpu: 1000m
