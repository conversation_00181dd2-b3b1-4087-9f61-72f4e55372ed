WITH selected_apps AS (SELECT id,
                              "productId",
                              "productEnvId",
                              name,
                              mode,
                              "oauthClientId",
                              "apiKey",
                              "termsAndConditionsRequired",
                              "mobileApiKey"
                       FROM apps
                       WHERE "deletedAt" IS NULL
                         AND "mode" IS NOT NULL
                         AND "productId" IS NOT NULL
                         AND "productEnvId" IS NOT NULL),
     selected_users AS (SELECT DISTINCT u.id,
                                        u.username,
                                        u.password,
                                        u."externalId",
                                        u."passwordResetToken",
                                        u."dateOfBirth",
                                        u.language,
                                        u."signUpCountry",
                                        u.uuid
                        FROM users u
                                 INNER JOIN activations a ON u.id = a."userId"
                                 INNER JOIN selected_apps sa ON a."appId" = sa.id
                        WHERE u."deletedAt" IS NULL
                          AND a."deletedAt" IS NULL),
     selected_activations AS (SELECT id, "appId", "userId"
                              FROM activations
                              WHERE "appId" IN (SELECT id FROM selected_apps)
                                AND "userId" IN (SELECT id FROM selected_users)
                                AND "deletedAt" IS NULL),
     selected_app_translations AS (SELECT id, language, description, "privacyPolicyUrl", "logoUrl", "iconUrl", "appId"
                                   FROM "appsTranslations"
                                   WHERE "appId" IN (SELECT id FROM selected_apps)
                                     AND "deletedAt" IS NULL),
     selected_jwks AS (SELECT id,
                              algorithm,
                              "keyType",
                              use,
                              "certThumbprint",
                              "keyId",
                              "publicPem",
                              "privatePem",
                              modulus
                       FROM "jwks"
                       WHERE "deletedAt" IS NULL),
     selected_webhooks AS (SELECT id, name, description, url, "secretKey", "appId"
                           FROM "webhooks"
                           WHERE ("appId" IN (SELECT id FROM selected_apps) OR ("appId" IS NULL))
                             AND "deletedAt" IS NULL
                             AND "secretKey" IS NOT NULL),
     selected_refreshTokens AS (SELECT id, expires, token, "clientId", scope, "userId", "appId"
                                FROM "refreshTokens"
                                WHERE "expires" IN '2025-02-28'
                                  AND "deletedAt" IS NULL),
     record_hashes AS (SELECT 'apps'                                                       AS table_name,
                              id,
                              MD5(CONCAT_WS('|', id, "productId", "productEnvId", name, mode, "oauthClientId", "apiKey",
                                            "termsAndConditionsRequired", "mobileApiKey")) AS record_hash
                       FROM selected_apps

                       UNION ALL

                       SELECT 'users'                                                        AS table_name,
                              id,
                              MD5(CONCAT_WS('|', id, username, password, "externalId", "passwordResetToken",
                                            "dateOfBirth", language, "signUpCountry", uuid)) AS record_hash
                       FROM selected_users

                       UNION ALL

                       SELECT 'activations'                              AS table_name,
                              id,
                              MD5(CONCAT_WS('|', id, "appId", "userId")) AS record_hash
                       FROM selected_activations

                       UNION ALL

                       SELECT 'appTranslations'       AS table_name,
                              id,
                              MD5(CONCAT_WS('|', id, language, description, "privacyPolicyUrl", "logoUrl", "iconUrl",
                                            "appId")) AS record_hash
                       FROM selected_app_translations

                       UNION ALL

                       SELECT 'jwks'                                AS table_name,
                              id,
                              MD5(CONCAT_WS('|', id, algorithm, "keyType", use, "certThumbprint", "keyId", "publicPem",
                                            "privatePem", modulus)) AS record_hash
                       FROM selected_jwks

                       UNION ALL

                       SELECT 'refreshTokens'                   AS table_name,
                              id,
                              MD5(CONCAT_WS('|', id, (expires AT TIME ZONE 'UTC')::timestamp, token, "clientId", scope,
                                            "userId", "appId")) AS record_hash
                       FROM selected_refreshTokens

                       UNION ALL

                       SELECT 'webhooks'                                                            AS table_name,
                              id,
                              MD5(CONCAT_WS('|', id, name, description, url, "secretKey", "appId")) AS record_hash
                       FROM selected_webhooks)
SELECT table_name, id, record_hash
FROM record_hashes
ORDER BY table_name, id;

