import { MigrationInterface, QueryRunner } from 'typeorm';

export class ChangeToPseudorandomSeq1728458113639 implements MigrationInterface {
  name = 'ChangeToPseudorandomSeq1728458113639';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER SEQUENCE user_id_seq RESTART WITH 1000000000;
            ALTER SEQUENCE app_id_seq RESTART WITH 1000000000;
        `);
    await queryRunner.query(`
            CREATE OR REPLACE FUNCTION pseudo_encrypt_30(VALUE int) returns int AS $$
            DECLARE
                l1 int;
                l2 int;
                r1 int;
                r2 int;
                i int:=0;
            BEGIN
                l1:= (VALUE >> 15) & 32767;
                r1:= VALUE & 32767;
                WHILE i < 3 LOOP
                l2 := r1;
                r2 := l1 # ((((945 * r1 + 342158) % 580943) / 580943.0) * 16383)::int;
                l1 := l2;
                r1 := r2;
                i := i + 1;
                END LOOP; 
                RETURN ((l1 << 15) + r1);
            END
            $$ LANGUAGE plpgsql strict immutable;
        `);
    await queryRunner.query(`
            CREATE OR REPLACE FUNCTION bounded_pseudo_encrypt_30(VALUE int, MAX bigint, OFFST int) returns int AS $$
            BEGIN
            LOOP
                VALUE := pseudo_encrypt_30(VALUE);
                EXIT WHEN VALUE <= MAX;
            END LOOP;
            VALUE := VALUE + OFFST;
            RETURN VALUE;
            END
            $$ LANGUAGE plpgsql strict immutable
        `);

    await queryRunner.query(`
            ALTER TABLE "user"
            ALTER COLUMN id
            SET DEFAULT bounded_pseudo_encrypt_30(nextval('user_id_seq')::int, 2000000000000, 1000000000)
        `);
    await queryRunner.query(`
            ALTER TABLE app
            ALTER COLUMN id
            SET DEFAULT bounded_pseudo_encrypt_30(nextval('app_id_seq')::int, 2000000000000, 1000000000)
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('DROP FUNCTION bounded_pseudo_encrypt_30');
    await queryRunner.query('DROP FUNCTION pseudo_encrypt_30');
  }
}
