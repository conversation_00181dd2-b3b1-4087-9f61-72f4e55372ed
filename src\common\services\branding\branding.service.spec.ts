import { TestingModule, Test } from '@nestjs/testing';
import { KeycloakService } from '@superawesome/freekws-auth-library';
import {
  BRANDING_SERVICE_API_CLIENT_INJECT_KEY,
  BrandingDTO,
  brandingServicePlugin,
  CustomCopyDTO,
} from '@superawesome/freekws-branding-service-common';
import { createCacheKey, ECacheServiceProviders } from '@superawesome/freekws-cache-decorator';
import { createClientMock } from '@superawesome/freekws-clients-base/src/test-utils';
import { MetricsService } from '@superawesome/freekws-metrics-nestjs-service';
import { v4 as uuidv4 } from 'uuid';

import { BrandingService } from './branding.service';
import { Utils } from '../../../../test/utils';
import { ConfigService } from '../config/config.service';

const mockBrandingApiClient = createClientMock(brandingServicePlugin, jest.fn);

const mockGetCacheable = jest.fn();
const configServiceMock: Partial<ConfigService> = {
  getCacheable: mockGetCacheable,
};

const mockGet = jest.fn();
const mockSet = jest.fn();
const mockClear = jest.fn();
const localLruServiceMock = {
  get: mockGet,
  set: mockSet,
  clear: mockClear,
  size: () => 10,
};

const metricsServiceMock = {
  metrics: {
    increment: jest.fn(),
    gauge: jest.fn(),
  },
};

let keycloakServiceMock: { getUpToDateServiceAccessToken: jest.Mock };
const mockTokenString = 'someBase64Token';
const brandingObject: BrandingDTO = {
  environmentId: uuidv4(),
  language: 'en',
  name: 'some-branding',
  theme: 'light',
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
};
const brandingResponse = {
  brandings: [brandingObject],
};

const customCopyResponse: CustomCopyDTO = {
  environmentId: uuidv4(),
  language: 'en',
  terms: {
    key: 'value_' + uuidv4(),
  },
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
};

describe('BrandingService', () => {
  let serviceUnderTest: BrandingService;

  beforeEach(async () => {
    jest.clearAllMocks();
    jest.resetAllMocks();
    mockGetCacheable.mockReturnValue({
      enabled: true,
      defaultTtlSecs: 30,
      maxCacheSizeEntries: 1000,
      metricsNamespace: 'consent-backend',
    });

    keycloakServiceMock = {
      getUpToDateServiceAccessToken: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        BrandingService,
        MetricsService,
        { provide: ECacheServiceProviders.Config, useValue: configServiceMock },
        { provide: ECacheServiceProviders.Metric, useValue: metricsServiceMock },
        { provide: ECacheServiceProviders.Cache, useValue: localLruServiceMock },
        {
          provide: BRANDING_SERVICE_API_CLIENT_INJECT_KEY,
          useValue: mockBrandingApiClient,
        },
        {
          provide: KeycloakService,
          useValue: keycloakServiceMock,
        },
      ],
    }).compile();
    serviceUnderTest = module.get<BrandingService>(BrandingService);
    keycloakServiceMock.getUpToDateServiceAccessToken?.mockResolvedValue(mockTokenString);
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  it('should be defined', () => {
    expect(serviceUnderTest).toBeDefined();
  });

  describe('getBrandings', () => {
    it('should retrieve brandings using the provided params when no query is used', async () => {
      mockBrandingApiClient
        .getModule('branding')
        .getBrandings.mockResolvedValueOnce(Utils.buildFullResponse(brandingResponse));
      const result = await serviceUnderTest.getBrandings('some-environment', null);

      expect(result).toEqual([brandingObject]);
      expect(mockBrandingApiClient.getModule('branding').getBrandings).toHaveBeenCalledWith(
        {
          params: {
            environmentId: 'some-environment',
          },
          query: {},
        },
        { headers: { Authorization: `Bearer ${mockTokenString}` } },
      );
    });

    it('should retrieve brandings from cache the provided params when no query is used', async () => {
      mockGet.mockResolvedValueOnce([brandingObject]);
      const result = await serviceUnderTest.getBrandings('some-environment', null);

      expect(result).toEqual([brandingObject]);
      expect(mockBrandingApiClient.getModule('branding').getBrandings).not.toHaveBeenCalledWith();
      expect(mockGet).toHaveBeenCalledWith(createCacheKey('getBrandings', ['some-environment', 'null', 'undefined']));
    });

    it('should retrieve brandings using the provided params when a query is used', async () => {
      mockBrandingApiClient
        .getModule('branding')
        .getBrandings.mockResolvedValueOnce(Utils.buildFullResponse(brandingResponse));
      const result = await serviceUnderTest.getBrandings('some-environment', 'en', 'another-environment');

      expect(result).toEqual([brandingObject]);
      expect(mockBrandingApiClient.getModule('branding').getBrandings).toHaveBeenCalledWith(
        {
          params: {
            environmentId: 'some-environment',
          },
          query: {
            language: 'en',
            fallbackEnvironmentId: 'another-environment',
          },
        },
        { headers: { Authorization: `Bearer ${mockTokenString}` } },
      );
    });

    it('should retrieve brandings from cache the provided params when a query is used', async () => {
      mockGet.mockResolvedValueOnce([brandingObject]);
      const result = await serviceUnderTest.getBrandings('some-environment', 'en', 'another-environment');

      expect(result).toEqual([brandingObject]);
      expect(mockBrandingApiClient.getModule('branding').getBrandings).not.toHaveBeenCalledWith();
      expect(mockGet).toHaveBeenCalledWith(
        createCacheKey('getBrandings', ['some-environment', 'en', 'another-environment']),
      );
    });
  });

  describe('getCustomCopy', () => {
    it('should retrieve custom copy using the provided params', async () => {
      mockBrandingApiClient
        .getModule('customCopy')
        .getCustomCopy.mockResolvedValueOnce(Utils.buildFullResponse(customCopyResponse));
      const result = await serviceUnderTest.getCustomCopy('some-environment', 'en');

      expect(result).toEqual(customCopyResponse);
      expect(mockBrandingApiClient.getModule('customCopy').getCustomCopy).toHaveBeenCalledWith(
        {
          params: {
            environmentId: 'some-environment',
          },
          query: { language: 'en' },
        },
        { headers: { Authorization: `Bearer ${mockTokenString}` } },
      );
    });

    it('should retrieve custom copy from cache', async () => {
      mockGet.mockResolvedValueOnce(customCopyResponse);
      const result = await serviceUnderTest.getCustomCopy('some-environment', 'en');

      expect(result).toEqual(customCopyResponse);
      expect(mockBrandingApiClient.getModule('customCopy').getCustomCopy).not.toHaveBeenCalledWith();
      expect(mockGet).toHaveBeenCalledWith(createCacheKey('getCustomCopy', ['some-environment', 'en']));
    });
  });
});
