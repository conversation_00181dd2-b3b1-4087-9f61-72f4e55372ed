# Sequential Load Testing with K6 Scenarios

A clean implementation using K6's built-in `scenarios` and `startTime` features for sequential endpoint testing.

## How It Works

**K6 automatically manages everything:**

1. **Sequential execution** - Each scenario has a `startTime` that starts after the previous one finishes
2. **Built-in ramp up/down** - Uses `ramping-vus` executor with stages
3. **Automatic cooldowns** - Scenarios naturally have gaps between them
4. **No manual timing logic** - K6 handles all the scheduling

## Files

- **`requests.ts`** - Reusable endpoint request functions
- **`sequential-scenarios-test.ts`** - K6 scenarios-based sequential test

## Configuration

| Environment Variable | Description | Default |
|---------------------|-------------|---------|
| `TARGET_QPS` | Target queries per second | `100` |
| `TEST_DURATION` | Seconds to test each endpoint | `60` |
| `COOLDOWN_DURATION` | Seconds between endpoints | `60` |
| `CLIENT_ID` | OAuth client ID | Required |
| `CLIENT_SECRET` | OAuth client secret | Required |
| `BASE_URL` | API base URL | `http://localhost:7001` |

## Usage

### Setup
```bash
# Copy environment template
cp .env.example .env

# Edit .env with your credentials
CLIENT_ID=your-actual-client-id
CLIENT_SECRET=your-actual-client-secret
BASE_URL=http://localhost:7001
```

### Run Tests

```bash
# Standard test (100 QPS)
npm run test:sequential-scenarios

# Stress test (300 QPS)
npm run test:sequential-scenarios-stress

# Custom configuration
npm run build:sequential-scenarios && dotenv -- k6 run dist/sequential-scenarios-test.js \
  --env TARGET_QPS=200 \
  --env TEST_DURATION=90 \
  --env COOLDOWN_DURATION=30
```

## How K6 Scenarios Work

Instead of manual timing, K6 automatically manages:

```javascript
scenarios: {
  healthcheck: {
    executor: 'ramping-vus',
    startTime: '0s',        // Starts immediately
    stages: [/* ramp up/down */]
  },
  getJwks: {
    executor: 'ramping-vus',
    startTime: '120s',      // Starts after healthcheck + cooldown
    stages: [/* ramp up/down */]
  },
  // ... more scenarios
}
```

## Endpoint Sequence

The test runs these 18 endpoints sequentially:

### **OAuth Endpoints (Authentication Flows)**
1. **oauthTokenClientCredentials** (0s) - `POST /oauth/token` (javi-test app)
2. **oauthTokenClientCredentialsDomCraft** (120s) - `POST /oauth/token` (dom-craft app)
3. **oauthTokenInvalidClient** (240s) - `POST /oauth/token` (error case)

### **Basic Endpoints (No Auth Required)**
4. **healthcheck** (360s) - `GET /healthcheck`
5. **getJwks** (480s) - `GET /v1/jwks`
6. **childAge** (600s) - `GET /v1/countries/child-age`
7. **checkUsername** (720s) - `GET /v1/users/check-username`

### **User Management Endpoints (Auth Required)**
8. **createUser** (840s) - `POST /v2/apps/:appId/users`
9. **createUserV2** (960s) - `POST /v2/users`
10. **getUser** (1080s) - `GET /v2/apps/:appId/users/:userId`
11. **activateUser** (1200s) - `POST /v1/users/:userId/apps`
12. **activateUserV2** (1320s) - `POST /v2/apps/:appId/users/:userId/activate`

### **Permission Endpoints (Auth Required)**
13. **requestPermissions** (1440s) - `POST /v2/apps/:appId/users/:userId/request-permissions`
14. **getPermissions** (1560s) - `GET /v2/apps/:appId/users/:userId/permissions`
15. **getPermissionsExtended** (1680s) - `GET /v2/apps/:appId/users/:userId/permissions?extended=true`
16. **reviewPermissions** (1800s) - `POST /v2/apps/:appId/users/:userId/review-permissions`
17. **updateParentEmail** (1920s) - `POST /v2/apps/:appId/users/:userId/update-parent-email`
18. **deleteUser** (2040s) - `DELETE /v2/apps/:appId/users/:userId`

*Times shown for 60s test + 60s cooldown*

## Load Pattern Per Endpoint

Each endpoint follows this VU pattern:

```
VUs
 ↑
 │     ┌─────────────┐
 │    ╱               ╲
 │   ╱                 ╲
 │  ╱                   ╲
 │ ╱                     ╲
 └╱───────────────────────╲─→ Time
  20%      60%       20%    Cooldown
Ramp Up  Steady    Ramp Down  (Next scenario)
```

## Advantages Over Manual Timing

✅ **K6 native** - Uses built-in features as intended
✅ **No manual calculations** - K6 handles all timing
✅ **Cleaner code** - No complex state management
✅ **More reliable** - K6's scenario engine is battle-tested
✅ **Better observability** - Each endpoint gets its own scenario metrics
✅ **Easier debugging** - Clear separation between endpoints

## Example Output

```
Sequential scenarios test starting:
- Target QPS: 100
- Target VUs: 10
- Test duration per endpoint: 60s
- Cooldown between endpoints: 60s
- Total endpoints: 14
- Estimated total time: 28.0 minutes

running (00m01s), 00/10 VUs, 0 complete and 0 interrupted iterations
healthcheck ✓ [======================================] 10 VUs  60s

running (02m01s), 00/10 VUs, 0 complete and 0 interrupted iterations
getJwks     ✓ [======================================] 10 VUs  60s
...
```

## Key Benefits

- **Automatic endpoint management** - K6 handles which endpoint runs when
- **Built-in load patterns** - Native ramp up/down with `ramping-vus`
- **Natural cooldowns** - Time gaps between scenarios provide cooldown
- **No complex logic** - Simple, clean implementation
- **Better metrics** - Each endpoint tagged separately by K6

This approach leverages K6's strengths instead of working around them!
