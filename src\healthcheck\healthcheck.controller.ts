import { Controller, Get, ServiceUnavailableException } from '@nestjs/common';
import { ApiExtraModels, ApiOperation, ApiTags } from '@nestjs/swagger';
import { Public } from '@superawesome/freekws-nestjs-guards';
import { SaApiOkResponse, SaApiResponseDto } from '@superawesome/freekws-nestjs-http-interceptors';

import { HealthcheckOutputDTO } from './healthcheck.dto';
import { HealthcheckService } from './healthcheck.service';
import { EAPITags } from '../common/types';

@Controller('healthcheck')
@ApiExtraModels(SaApiResponseDto, HealthcheckOutputDTO)
export class HealthcheckController {
  constructor(private readonly service: HealthcheckService) {}

  @ApiOperation({
    summary: 'Check if API is responsive',
    description: 'Will return success if the API is capable of responding (used for kubernetes pod healthcheck).',
  })
  @ApiTags(EAPITags.Internal)
  @SaApiOkResponse({
    type: HealthcheckOutputDTO,
  })
  @Get('')
  @Public()
  async get(): Promise<HealthcheckOutputDTO> {
    if (!this.service.isHealthy()) {
      throw new ServiceUnavailableException('server not healthy');
    }
    return {
      name: 'Success',
      message: 'What a day to be alive!',
    };
  }
}
