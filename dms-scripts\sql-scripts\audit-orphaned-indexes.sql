-- Create indexes for the cleanup operation
BEGIN;

-- Indexes for "user" table
CREATE INDEX IF NOT EXISTS idx_user_orgenvid ON "user" ("orgEnvId");
CREATE INDEX IF NOT EXISTS idx_user_id_orgenvid ON "user" (id, "orgEnvId");

-- Indexes for org_env table
CREATE INDEX IF NOT EXISTS idx_org_env_id ON org_env (id);

-- Indexes for activation table
CREATE INDEX IF NOT EXISTS idx_activation_orgenvid ON activation ("orgEnvId");
CREATE INDEX IF NOT EXISTS idx_activation_userid_orgenvid ON activation ("userId", "orgEnvId");
CREATE INDEX IF NOT EXISTS idx_activation_appid_orgenvid ON activation ("appId", "orgEnvId");

-- Indexes for app table
CREATE INDEX IF NOT EXISTS idx_app_id_orgenvid ON app (id, "orgEnvId");

-- Indexes for app_translation table
CREATE INDEX IF NOT EXISTS idx_app_translation_orgenvid ON app_translation ("orgEnvId");
CREATE INDEX IF NOT EXISTS idx_app_translation_appid_orgenvid ON app_translation ("appId", "orgEnvId");

-- Indexes for webhook table
CREATE INDEX IF NOT EXISTS idx_webhook_orgenvid ON webhook ("orgEnvId");
CREATE INDEX IF NOT EXISTS idx_webhook_appid_orgenvid ON webhook ("appId", "orgEnvId");

COMMIT;




-- Remove indexes created for the cleanup operation
BEGIN;

-- Remove indexes from "user" table
DROP INDEX IF EXISTS idx_user_orgenvid;
DROP INDEX IF EXISTS idx_user_id_orgenvid;

-- Remove index from org_env table
DROP INDEX IF EXISTS idx_org_env_id;

-- Remove indexes from activation table
DROP INDEX IF EXISTS idx_activation_orgenvid;
DROP INDEX IF EXISTS idx_activation_userid_orgenvid;
DROP INDEX IF EXISTS idx_activation_appid_orgenvid;

-- Remove index from app table
DROP INDEX IF EXISTS idx_app_id_orgenvid;

-- Remove indexes from app_translation table
DROP INDEX IF EXISTS idx_app_translation_orgenvid;
DROP INDEX IF EXISTS idx_app_translation_appid_orgenvid;

-- Remove indexes from webhook table
DROP INDEX IF EXISTS idx_webhook_orgenvid;
DROP INDEX IF EXISTS idx_webhook_appid_orgenvid;

COMMIT;


