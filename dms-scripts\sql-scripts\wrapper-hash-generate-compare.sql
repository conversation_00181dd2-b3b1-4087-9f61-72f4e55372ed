WITH selected_apps AS (
    SELECT id, "productId", "productEnvId", name, mode, "oauthClientId", "apiKey", "termsAndConditionsRequired", "mobileApiKey"
    FROM app
    WHERE "orgEnvId" = 'cf52457d-1a3b-4305-ac43-ff7269329703'
),
selected_users AS (
    SELECT DISTINCT u.id, u.username, u.password, u."externalId", u."passwordResetToken", u."dateOfBirth", u.language, u."signUpCountry", u.uuid
    FROM "user" u
    INNER JOIN activation a ON u.id = a."userId" AND u."orgEnvId" = a."orgEnvId"
    INNER JOIN selected_apps sa ON a."appId" = sa.id
    WHERE u."orgEnvId" = 'cf52457d-1a3b-4305-ac43-ff7269329703'
),
selected_activations AS (
    SELECT id, "appId", "userId"
    FROM activation
    WHERE "orgEnvId" = 'cf52457d-1a3b-4305-ac43-ff7269329703'
      AND "appId" IN (SELECT id FROM selected_apps)
      AND "userId" IN (SELECT id FROM selected_users)
),
selected_app_translations AS (
    SELECT id, language, description, "privacyPolicyUrl", "logoUrl", "iconUrl", "appId"
    FROM app_translation
    WHERE "orgEnvId" = 'cf52457d-1a3b-4305-ac43-ff7269329703'
      AND "appId" IN (SELECT id FROM selected_apps)
),
selected_jwks AS (
    SELECT id, algorithm, "keyType", use, "certThumbprint", "keyId", "publicPem", "privatePem", modulus
    FROM jwk
    WHERE "orgEnvId" = 'cf52457d-1a3b-4305-ac43-ff7269329703'
),
selected_webhooks AS (
    SELECT id, name, description, url, "secretKey", "appId"
    FROM webhook
    WHERE "orgEnvId" = 'cf52457d-1a3b-4305-ac43-ff7269329703'
      AND ("appId" IN (SELECT id FROM selected_apps) OR ("appId" IS NULL))
      AND "secretKey" IS NOT NULL
),
selected_refresh_tokens AS (
    SELECT id, expires, token, "clientId", scope, "userId", "appId"
    FROM refresh_token
    WHERE "orgEnvId" = 'cf52457d-1a3b-4305-ac43-ff7269329703'
      AND "expires" >= '2025-02-28' 
),
record_hashes AS (
    SELECT 'apps' AS table_name,
           id,
           MD5(CONCAT_WS('|', id, "productId", "productEnvId", name, mode, "oauthClientId", "apiKey", "termsAndConditionsRequired", "mobileApiKey")) AS record_hash
    FROM selected_apps

    UNION ALL

    SELECT 'users' AS table_name,
           id,
           MD5(CONCAT_WS('|', id, username, password, "externalId", "passwordResetToken", "dateOfBirth", language, "signUpCountry", uuid)) AS record_hash
    FROM selected_users

    UNION ALL

    SELECT 'activations' AS table_name,
           id,
           MD5(CONCAT_WS('|', id, "appId", "userId")) AS record_hash
    FROM selected_activations

    UNION ALL

    SELECT 'appTranslations' AS table_name,
           id,
           MD5(CONCAT_WS('|', id, language, description, "privacyPolicyUrl", "logoUrl", "iconUrl", "appId")) AS record_hash
    FROM selected_app_translations

    UNION ALL

    SELECT 'jwks' AS table_name,
           id,
           MD5(CONCAT_WS('|', id, algorithm, "keyType", use, "certThumbprint", "keyId", "publicPem", "privatePem", modulus)) AS record_hash
    FROM selected_jwks

    UNION ALL

    SELECT 'refreshTokens' AS table_name,
           id,
           MD5(CONCAT_WS('|', id, expires, token, "clientId", scope, "userId", "appId")) AS record_hash
    FROM selected_refresh_tokens

    UNION ALL

    SELECT 'webhooks' AS table_name,
           id,
           MD5(CONCAT_WS('|', id, name, description, url, "secretKey", "appId")) AS record_hash
    FROM selected_webhooks
)
SELECT table_name, id, record_hash
FROM record_hashes
ORDER BY table_name, id;

