import { ClassicWebhookName } from '@superawesome/freekws-queue-messages/webhook/classic-webhook.dto';

import { App } from '../../src/app/app.entity';
import { OrgEnv } from '../../src/org-env/org-env.entity';
import { Webhook } from '../../src/webhook/webhook.entity';

const orgEnvId = 'e67fed5f-da21-4a5c-b1bd-e35bfffe7101';

const WEBHOOKS: Webhook[] = [
  buildWebhook(1, ClassicWebhookName.CHILD_ACCOUNT_GRADUATED),
  buildWebhook(2, ClassicWebhookName.USER_PERMISSION_CHANGED),
  buildWebhook(3, ClassicWebhookName.PARENT_UNLINKED_FROM_CHILD),
  buildWebhook(4, ClassicWebhookName.CHILD_LINKED_TO_PARENT),
  buildWebhook(5, ClassicWebhookName.UNRESPONSIVE_PARENT_ACCOUNT_DELETED),
];

function buildWebhook(id: number, name: string) {
  return {
    id: id,
    name: name,
    description: 'Description',
    url: 'https://example.com/webhook',
    secretKey: 'secret',
    app: { id: **********, productId: '7794e949-9fed-43eb-80be-4b504dc43b1b' } as App,
    appId: **********,
    orgEnv: { id: orgEnvId } as OrgEnv,
    orgEnvId,
    createdAt: new Date(Date.now()),
    updatedAt: new Date(Date.now()),
  };
}

export default WEBHOOKS;
