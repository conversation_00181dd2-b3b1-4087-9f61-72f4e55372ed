function devPortalResponseBuilder() {
  const webhooks = [
    {
      id: '123',
      name: 'settings:user-graduated',
      url: 'https://example.com/graduateUser',
      secretKey: 'top-secret',
    },
    {
      id: '1234',
      name: 'settings:effective-values-changed',
      url: 'https://example.com/effective-values-changed',
      secretKey: 'top-secret',
    },
    {
      id: '1234',
      name: 'families:user-removed-from-family',
      url: 'https://example.com/user-removed-from-family',
      secretKey: 'top-secret',
    },
    {
      id: '1234',
      name: 'families:user-added-to-family',
      url: 'https://example.com/user-added-to-family',
      secretKey: 'top-secret',
    },
    {
      id: '1234',
      name: 'families:guardian-request-expired',
      url: 'https://example.com/user-added-to-family',
      secretKey: 'top-secret',
    },
    {
      id: '12345',
      name: 'families:family-group-deleted',
      url: 'https://example.com/family-group-deleted',
      secretKey: 'top-secret',
    },
  ];

  return {
    statusCode: 200,
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      response: { webhooks },
    }),
  };
}
