import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { CountryRegionExists, Tiso31662 } from '@superawesome/freekws-regional-config';
import { Expose } from 'class-transformer';
import { IsBoolean, IsNumber, IsOptional, IsString, Matches, Validate } from 'class-validator';

import { DATE_FORMAT_REGEX } from '../common';

export class ChildAgeQueryDTO {
  @ApiPropertyOptional({
    description: `Date of bith of the user in YYYY-MM-DD format.
      If supplied, will return the age & whether or not this age is considered a child.`,
    example: '2010-10-10',
  })
  @Matches(DATE_FORMAT_REGEX)
  @IsOptional()
  dob?: string;

  @ApiPropertyOptional({
    description:
      'Country of the user in ISO31662 format. If provided, will override the detected country and use this one instead.',
    example: 'US',
  })
  @Validate(CountryRegionExists, [{ optional: true, case: false }])
  @IsOptional()
  country?: Tiso31662;
}

export class ChildAgeResponseDTO {
  @ApiProperty({
    description:
      'The detected country from the IP address, or the country overridden via the query params if this was set.',
    example: 'US',
  })
  @Expose()
  @IsString()
  country: string;

  @ApiProperty({
    description: 'The age threshold for the country.',
    example: 13,
  })
  @Expose()
  @IsNumber()
  consentAgeForCountry: number;

  @ApiPropertyOptional({
    description: `The current age of the user, if 'dob' was provided in the request.`,
    example: 8,
  })
  @Expose()
  @IsNumber()
  @IsOptional()
  age?: number;

  @ApiPropertyOptional({
    description: `Flag indicating whether the user is under age, based on the age and age threshold for the country.
      Only sent if 'dob' was provided in the request.`,
  })
  @Expose()
  @IsBoolean()
  @IsOptional()
  isMinor?: boolean;
}
