-- get all records for org-env-id
with org_env_id AS (
    SELECT 'cf52457d-1a3b-4305-ac43-ff7269329703'::varchar AS id
),
    record_counts as (
                       select 'activation' as table_name, count(*) as count
                       from activation, org_env_id
                       where "orgEnvId" = org_env_id.id
                       union all
                       select 'app' as table_name, count(*) as count
                       from app, org_env_id
                       where "orgEnvId" = org_env_id.id
                       union all
                       select 'app_translation' as table_name, count(*) as count
                       from app_translation, org_env_id
                       where "orgEnvId" = org_env_id.id
                       union all
                       select 'jwk' as table_name, count(*) as count
                       from jwk, org_env_id
                       where "orgEnvId" = org_env_id.id
                       union all
                       select 'refresh_token' as table_name, count(*) as count
                       from refresh_token, org_env_id
                       where "orgEnvId" = org_env_id.id
                       union all
                       select 'user' as table_name, count(*) as count
                       from "user", org_env_id
                       where "orgEnvId" = org_env_id.id
                       union all
                       select 'webhook' as table_name, count(*) as count
                       from webhook, org_env_id
                       where "orgEnvId" = org_env_id.id)
select table_name || ': ' || count AS summary
from record_counts;


-- get valid records that should stay
WITH org_env_id AS (
    SELECT 'cf52457d-1a3b-4305-ac43-ff7269329703'::varchar AS orgId
),
selected_apps AS (
    SELECT id
    FROM app, org_env_id
    WHERE app."orgEnvId" = org_env_id.orgId
),
selected_users AS (
    SELECT u.id
    FROM "user" u
    JOIN org_env_id ON u."orgEnvId" = org_env_id.orgId
    JOIN activation a ON u.id = a."userId" AND u."orgEnvId" = a."orgEnvId"
    JOIN selected_apps sa ON a."appId" = sa.id
),
selected_activations AS (
    SELECT id
    FROM activation, org_env_id
    WHERE activation."orgEnvId" = org_env_id.orgId
      AND "appId" IN (SELECT id FROM selected_apps)
      AND "userId" IN (SELECT id FROM selected_users)
),
selected_app_translations AS (
    SELECT id
    FROM app_translation, org_env_id
    WHERE app_translation."orgEnvId" = org_env_id.orgId
      AND "appId" IN (SELECT id FROM selected_apps)
),
selected_jwks AS (
    SELECT id
    FROM jwk, org_env_id
    WHERE jwk."orgEnvId" = org_env_id.orgId
),
selected_webhooks AS (
    SELECT id
    FROM webhook, org_env_id
    WHERE webhook."orgEnvId" = org_env_id.orgId
      AND ("appId" IN (SELECT id FROM selected_apps) OR ("appId" IS NULL))
      AND "secretKey" IS NOT NULL
),
selected_refresh_tokens AS (
    SELECT id
    FROM refresh_token, org_env_id
    WHERE refresh_token."orgEnvId" = org_env_id.orgId
      AND "expires" >= '2025-02-28'
),
records_counts AS (
    SELECT 'apps' AS table_name, COUNT(*) AS count FROM selected_apps UNION ALL
    SELECT 'users' AS table_name, COUNT(*) AS count FROM selected_users UNION ALL
    SELECT 'activations' AS table_name, COUNT(*) AS count FROM selected_activations UNION ALL
    SELECT 'appTranslations' AS table_name, COUNT(*) AS count FROM selected_app_translations UNION ALL
    SELECT 'jwks' AS table_name, COUNT(*) AS count FROM selected_jwks UNION ALL
    SELECT 'refreshTokens' AS table_name, COUNT(*) AS count FROM selected_refresh_tokens UNION ALL
    SELECT 'webhooks' AS table_name, COUNT(*) AS count FROM selected_webhooks
)
SELECT table_name || ': ' || count AS summaryWrapper
FROM records_counts;

