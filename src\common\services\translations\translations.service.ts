﻿import { ConflictException, Injectable } from '@nestjs/common';
import { BrandingDTO } from '@superawesome/freekws-branding-service-common';
import { getBestLanguageMatch } from '@superawesome/freekws-regional-config/languages';
import { isNil } from 'lodash';
import { Span } from 'nestjs-ddtrace';

import { BrandingService } from '../branding/branding.service';

@Span()
@Injectable()
export class TranslationsService {
  constructor(private readonly brandingService: BrandingService) {}

  public async getTranslationWithBestMatch(orgEnvId: string, language?: string): Promise<BrandingDTO | undefined> {
    const brandings = await this.brandingService.getBrandings(orgEnvId, null);
    if (brandings.length === 0) {
      throw new ConflictException('No translations found, add translations at organisation level');
    }
    return this.getBestTranslationMatch(brandings, language);
  }

  private getBestTranslationMatch(translations: BrandingDTO[], preferredLanguage?: string): BrandingDTO | undefined {
    const availableLanguages = translations.filter((t) => t.language).map((t) => t.language);
    if (availableLanguages.length === 0) {
      return translations[0];
    }
    const preferredLanguages = isNil(preferredLanguage) ? [] : [preferredLanguage];
    const language = getBestLanguageMatch(availableLanguages, preferredLanguages);
    return translations.find((t) => t.language === language);
  }
}
