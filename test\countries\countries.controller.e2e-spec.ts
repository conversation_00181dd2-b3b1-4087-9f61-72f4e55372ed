import { INestApplication } from '@nestjs/common';
import request from 'supertest';

import { Utils } from '../utils';
import { TEST_APP_HOST } from '../utils/constants';

describe('CountriesController (e2e)', () => {
  let app: INestApplication;

  beforeAll(async () => {
    app = await Utils.createTestServer();
    Utils.mockAgeGateAPI();
  });

  beforeEach(async () => {
    await Utils.cleanDb();
    await Utils.loadFixtures();
  });

  afterAll(async () => {
    await Utils.stopTestServer(app);
    jest.clearAllMocks();
  });

  describe('GET /v1/countries/child-age', () => {
    it('should return the expected response', async () => {
      const result = await request(app.getHttpServer())
        .get('/v1/countries/child-age')
        .set('x-forwarded-host', TEST_APP_HOST)
        .query({ dob: '2011-10-10', country: 'CA' })
        .expect(200);
      Utils.assertApiBody(result.body, {
        consentAgeForCountry: 13,
        country: 'US',
        age: 12,
        isMinor: true,
      });
    });
  });
});
