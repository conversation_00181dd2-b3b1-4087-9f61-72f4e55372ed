import { Test, TestingModule } from '@nestjs/testing';
import { AGE_GATE_API_CLIENT_INJECT_KEY, ageGateApiPlugin } from '@superawesome/freekws-agegate-api-common';
import { createClientMock } from '@superawesome/freekws-clients-base/src/test-utils/mock';

import { AgeGateService } from './age-gate.service';
import { ClientKeycloakService } from '../keycloak/client-keycloak.service';

const MOCK_TOKEN = 'mockToken';

const mockAgeGateApi = createClientMock(ageGateApiPlugin, jest.fn);

const mockKeycloakService = {
  getClientToken: jest.fn(),
};

describe('AgeGateService', () => {
  let service: AgeGateService;

  beforeEach(async () => {
    jest.resetAllMocks();

    mockKeycloakService.getClientToken.mockResolvedValue(MOCK_TOKEN);

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AgeGateService,
        { provide: AGE_GATE_API_CLIENT_INJECT_KEY, useValue: mockAgeGateApi },
        { provide: ClientKeycloakService, useValue: mockKeycloakService },
      ],
    }).compile();
    await module.init();

    service = module.get<AgeGateService>(AgeGateService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getConsentAgeForCountry', () => {
    it('should generate a token and make a call to get the consent age for country', async () => {
      mockAgeGateApi.getModule('age').getAgeOfDigitalConsent.mockResolvedValueOnce({
        data: {
          response: { country: 'US', consentAge: 13, userAge: 10, underAgeOfDigitalConsent: true },
          meta: { requestId: 'foo', timestamp: new Date().toISOString() },
        },
        status: 200,
      });
      await expect(service.getConsentAgeForCountry({}, { clientId: '', secret: '' })).resolves.toEqual({
        consentAge: 13,
        country: 'US',
        underAgeOfDigitalConsent: true,
        userAge: 10,
      });
    });

    it('should get the consent age for country with location and age', async () => {
      mockAgeGateApi.getModule('age').getAgeOfDigitalConsent.mockResolvedValueOnce({
        data: {
          response: { country: 'US', consentAge: 13, userAge: 10, underAgeOfDigitalConsent: true },
          meta: { requestId: 'foo', timestamp: new Date().toISOString() },
        },
        status: 200,
      });
      await expect(
        service.getConsentAgeForCountry({ location: 'AD', age: 12 }, { clientId: '', secret: '' }),
      ).resolves.toEqual({
        consentAge: 13,
        country: 'US',
        underAgeOfDigitalConsent: true,
        userAge: 10,
      });
    });

    it('should throw an error if agegate-service request failed', async () => {
      mockAgeGateApi.getModule('age').getAgeOfDigitalConsent.mockRejectedValueOnce(new Error('ECONREFUSED'));
      await expect(
        service.getConsentAgeForCountry(
          {},
          {
            clientId: '',
            secret: '',
          },
        ),
      ).rejects.toThrow();
    });
  });
});
