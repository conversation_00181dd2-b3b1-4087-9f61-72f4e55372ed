import { TestingModule } from '@nestjs/testing';
import { Repository } from 'typeorm';

import { JWK } from './jwk.entity';
import { JwkRepository } from './jwk.repository';
import { Testing } from '../common/utils';

describe('JwkRepository', () => {
  let repository: JwkRepository;
  let jwkRepository: Repository<JWK>;

  beforeEach(async () => {
    const module: TestingModule = await Testing.createModule({
      providers: [
        JwkRepository,
        {
          provide: 'JWKRepository',
          useValue: {
            find: jest.fn(),
          },
        },
      ],
    });

    repository = module.get<JwkRepository>(JwkRepository);
    jwkRepository = module.get<Repository<JWK>>('JWKRepository');
  });

  describe('getRecentJwks', () => {
    it('returns most recent JWKs', async () => {
      const mockJwks = [
        {
          id: 1,
          algorithm: 'RS256',
          keyType: 'RSA',
          use: 'sig',
          modulus: 'test-modulus',
          exponent: 'test-exponent',
          keyId: 'test-key-id',
          publicPem: 'test-public-pem',
          privatePem: 'test-private-pem',
          certThumbprint: 'test-thumbprint',
          createdAt: new Date('2023-01-01'),
        },
      ] as JWK[];

      jest.spyOn(jwkRepository, 'find').mockResolvedValue(mockJwks);

      const result = await repository.getRecentJwks();

      expect(jwkRepository.find).toHaveBeenCalledWith({
        order: {
          createdAt: 'DESC',
        },
        take: 20,
      });
      expect(result).toEqual(mockJwks);
    });
  });
});
