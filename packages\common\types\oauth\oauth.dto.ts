import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsEnum, IsIn, IsN<PERSON>ber, IsOptional, IsString, IsUrl, Matches } from 'class-validator';

export enum EOAuthScope {
  APP = 'app',
  MOBILE_APP = 'mobileApp',
  USER = 'user',
}

export enum EOAuthGrantType {
  CLIENT_CREDENTIALS = 'client_credentials',
  REFRESH_TOKEN = 'refresh_token',
  PASSWORD = 'password',
  AUTHORIZATION_CODE = 'authorization_code',
}

export class OAuthTokenDTO {
  @ApiPropertyOptional({
    description: `OAuth client id should be provided in the request body or as basic authorisation header a user value.
    Example (base64 decoded): \`Authorization: Basic client_id:client_secret\``,
  })
  @IsString()
  @IsOptional()
  client_id?: string;

  @ApiPropertyOptional({
    description:
      'OAuth client id should be provided in the request body or as basic authorisation header a password value.',
  })
  @IsString()
  @IsOptional()
  client_secret?: string;

  @ApiPropertyOptional({
    description: `Refresh token for the refresh token grant type`,
  })
  @IsString()
  @IsOptional()
  refresh_token?: string;

  @ApiProperty({
    description: 'Parameter defines the specific access permissions being requested',
  })
  @IsEnum(EOAuthScope)
  @IsOptional()
  scope?: EOAuthScope;

  @ApiProperty({
    description: 'Username for a specific user in the password grant type',
  })
  @IsString()
  @IsOptional()
  username?: string;

  @ApiProperty({
    description: 'Password for a specific user in the password grant type',
  })
  @IsString()
  @IsOptional()
  password?: string;

  @ApiProperty({
    description: 'Parameter specifies the method being used to request an access token',
  })
  @IsEnum(EOAuthGrantType)
  grant_type: EOAuthGrantType;

  @ApiPropertyOptional()
  @Expose()
  @IsString()
  @IsOptional()
  code?: string;

  @ApiPropertyOptional()
  @Expose()
  @IsString()
  @IsOptional()
  redirect_uri?: string;

  @ApiPropertyOptional()
  @Expose()
  @IsString()
  @IsOptional()
  code_verifier?: string;
}

export class OAuthTokenResponseDTO {
  @ApiProperty({ default: 'bearer' })
  @Expose()
  @IsString()
  token_type: string;

  @ApiProperty()
  @Expose()
  @IsString()
  access_token: string;

  @ApiPropertyOptional()
  @Expose()
  @IsString()
  @IsOptional()
  refresh_token?: string;

  @ApiPropertyOptional()
  @Expose()
  @IsNumber()
  @IsOptional()
  expires_in?: number;
}

export class OAuthAuthorisePayloadDTO {
  @ApiProperty({
    description: 'Type of response expected',
    example: 'code',
    enum: ['code']
  })
  @Expose()
  @IsString()
  @IsIn(['code'])
  response_type: "code";

  @ApiProperty({
    description: 'The client identifier',
    example: 'testapp1'
  })
  @Expose()
  @IsString()
  client_id: string;

  @ApiProperty({
    description: 'The redirect URI where the authorization code will be sent',
    example: 'https://client.example.com/callback'
  })
  @Expose()
  @IsString()
  @IsUrl()
  redirect_uri: string;

  @ApiProperty({
    description: 'A CSRF token used to maintain state between request and callback',
    example: 'xyz123'
  })
  @Expose()
  @IsString()
  @IsOptional()
  state?: string;

  @ApiPropertyOptional({
    description: 'PKCE code challenge',
    example: 'E9Melhoa2OwvFrEMTJguCHaoeK1t8URWbuGJSstw-cM'
  })
  @Expose()
  @IsString()
  @IsOptional()
  @Matches(/^[A-Za-z0-9\-_]+$/)
  code_challenge?: string;

  @ApiPropertyOptional({
    description: 'Method used to derive the code challenge',
    example: 'S256',
    enum: ['plain', 'S256']
  })
  @Expose()
  @IsString()
  @IsOptional()
  @IsIn(['plain', 'S256'])
  code_challenge_method?: "plain" | "S256";
}


export class OAuthAuthorizationCodeResponseDTO {
  @ApiProperty({
    description: 'The redirect URI where the user will be sent',
    example: 'https://client.example.com/callback'
  })
  @Expose()
  @IsString()
  redirectUri: string;

  @ApiProperty({
    description: 'The authorization code to be exchanged for an access token',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
  })
  @Expose()
  @IsString()
  code: string;

  @ApiProperty({
    description: 'The state value echoed from the request for CSRF protection',
    example: 'xyz123'
  })
  @Expose()
  @IsString()
  state: string;
}