import { Type } from '@nestjs/common';
import { FastifyRequest } from 'fastify';

export interface IPolicyHandler {
  handle(request: FastifyRequest): Promise<boolean> | boolean;
}

export type TPolicyHandlerCallback = (request: FastifyRequest) => Promise<boolean>;

export type TPolicyInject = string | symbol | Type | IPolicyHandler | TPolicyHandlerWithInjectables;

export interface TPolicyHandlerWithInjectables {
  // eslint-disable-next-line @typescript-eslint/ban-types
  useClass: Function;
  inject: TPolicyInject[];
}

export type TPolicyHandler = IPolicyHandler | TPolicyHandlerWithInjectables | TPolicyHandlerCallback;
