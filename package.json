{"name": "freekws-classic-wrapper-backend", "version": "0.0.1", "private": true, "description": "", "license": "UNLICENSED", "scripts": {"build": "nest build", "docs:generate": "npm run build && NODE_ENV=test npx ts-node ./node_modules/.bin/freekws-nestjs-apiblueprint src/root-app.module.ts", "docs:start": "npm run build && NODE_ENV=test npx ts-node ./node_modules/.bin/freekws-nestjs-apiblueprint src/root-app.module.ts 8080", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "lint": "eslint \"{src,test,test-acceptance}/**/*.ts\" && npm run lint:package package-lock.json", "lint:fix": "eslint \"{src,test,test-acceptance}/**/*.ts\" --fix && npm run lint:package package-lock.json", "lint:staged": "lint-staged --relative", "lint:package": "./scripts/lint-package-lock.sh", "lint:ignore-formatting": "eslint \"{src,test,test-acceptance}/**/*.ts\" --rule 'prettier/prettier: 0' && npm run lint:package package-lock.json", "migration:create": "ts-node ./node_modules/typeorm/cli.js migration:create ./migration/pleaseRenameThisNewCreatedMigration", "migration:generate": "npm run migration:run && ts-node ./node_modules/typeorm/cli.js migration:generate ./migration/pleaseRenameThisNewCreatedMigration -p -d ormconfig.ts", "migration:generate:local": "NODE_ENV=myhost npm run migration:run && ts-node ./node_modules/typeorm/cli.js migration:generate ./migration/pleaseRenameThisNewCreatedMigration -p -d ormconfig.ts", "migration:revert": "npm run migration:run && ts-node ./node_modules/typeorm/cli.js migration:revert -d ormconfig.ts", "migration:run": "ts-node ./node_modules/typeorm/cli.js migration:run -d ormconfig.ts", "migration:run:prod": "npx typeorm migration:run -d dist/ormconfig.js", "migration:run:uat": "ts-node ./node_modules/typeorm/cli.js migration:run -d test-acceptance/ormconfig.uat.ts", "migration:run:local": "NODE_ENV=myhost npm run migration:run", "migration:show": "ts-node ./node_modules/typeorm/cli.js migration:show -d ormconfig.ts", "migration:show:local": "NODE_ENV=myhost ts-node ./node_modules/typeorm/cli.js migration:show -d ormconfig.ts", "migration:show:uat": "ts-node ./node_modules/typeorm/cli.js migration:show -d test-acceptance/ormconfig.uat.ts", "prepare": "$CI || husky install", "start": "nest start", "start:local": "NODE_ENV=myhost nest start", "start:debug": "nest start --debug --watch", "start:debug:local:watch": "cross-env NODE_ENV=myhost nest start --debug --watch", "start:debug:local": "npm run packages:build && cross-env NODE_ENV=myhost nest start --debug", "start:dev": "NODE_ENV=test npm run migration:run && NODE_ENV=test npm run migration:run:uat && nest start --watch", "start:prod": "node --require dd-trace/init dist/src/main", "test": "NODE_ENV=test jest --forceExit --runInBand", "test:cov": "NODE_ENV=test jest --coverage", "test:debug": "NODE_ENV=test node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand --forceExit", "test:e2e": "./scripts/wait-for-it.sh -t 120 keycloak:8080 && NODE_ENV=test npm run migration:run && NODE_ENV=test jest --config ./test/jest-e2e.json  --detectOpenHandles --forceExit ", "test:e2e:debug": "./scripts/wait-for-it.sh -t 120 keycloak:8080 && NODE_ENV=test npm run migration:run && NODE_ENV=test node --require ./debug.js --inspect=0.0.0.0:9229 ./node_modules/.bin/jest --config ./test/jest-e2e.json --detectOpenHandles --forceExit", "test:e2e:watch": "./scripts/wait-for-it.sh -t 120 keycloak:8080 && NODE_ENV=test npm run migration:run && NODE_ENV=test jest --config ./test/jest-e2e.json --forceExit --runInBand --watchAll", "test:e2e:local": "NODE_ENV=myhost npm run migration:run && NODE_ENV=myhost jest --config ./test/jest-e2e.json --forceExit --runInBand", "test:migration": "(npm run migration:generate > /dev/null 2>&1) && echo 'ERROR: there are pending migrations' && exit 1;echo 'No migrations pending' && exit 0", "test:watch": "NODE_ENV=test jest --runInBand --watchAll", "test:watch:cov": "NODE_ENV=test jest --coverage --runInBand --watchAll", "test:acceptance": "./scripts/wait-for-it.sh -t 120 stub:4747 && ./scripts/wait-for-it.sh -t 120 keycloak:8080 && ./scripts/wait-for-it.sh -t 120 postgres:5432 && ./scripts/wait-for-it.sh -t 120 classic-wrapper-backend:80 && NODE_ENV=test ./node_modules/.bin/cucumber-js --config test-acceptance/cucumber.js -p default --exit", "test:acceptance:watch": "npm-watch test:acceptance", "typeorm": "ts-node -r tsconfig-paths/register ./node_modules/typeorm/cli.js", "golden-master:run-no-input": "NODE_ENV=test ts-node golden-master/run-no-input.ts", "golden-master:run-with-input": "NODE_ENV=test ts-node golden-master/run-with-input.ts", "golden-master:generate-input": "NODE_ENV=test ts-node golden-master/generate-input.ts", "packages:build": "npm run build -ws", "packages:test": "npm run test -ws", "packages:lint": "npm run lint -ws", "packages:test:watch": "npm run test:watch -ws", "packages:publish": "npm run semantic-release -ws", "packages:publish:dryrun": "npm run semantic-release:dryrun -ws", "job": "DRY_MODE=false node --require dd-trace/init dist/src/jobs/run-job.js", "job:dry": "node --require dd-trace/init dist/src/jobs/run-job.js"}, "lint-staged": {"*.ts": "eslint --cache --fix --max-warnings 0", "package-lock.json": "npm run lint:package"}, "release": {"branches": ["main"]}, "workspaces": ["packages/common/"], "jest": {"collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "coveragePathIgnorePatterns": ["/node_modules/", "<rootDir>/.*/*.module.ts", "<rootDir>/.*/*.entity.ts", "<rootDir>/main.ts"], "moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testEnvironment": "node", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}}, "watch": {"test:acceptance": {"patterns": ["src", "test-acceptance"], "extensions": "js,ts,feature,yaml"}}, "dependencies": {"@amplitude/node": "^1.10.2", "@nestjs/common": "^10.0.0", "@nestjs/core": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/platform-fastify": "^10.4.4", "@nestjs/typeorm": "^10.0.2", "@superawesome/freekws-auth-library": "^9.1.0", "@superawesome/freekws-branding-service-common": "^3.1.0", "@superawesome/freekws-cache-decorator": "^2.2.0", "@superawesome/freekws-callback-service-common": "^3.0.0", "@superawesome/freekws-classic-wrapper-common": "*", "@superawesome/freekws-clients-nestjs": "^4.0.0", "@superawesome/freekws-common-logger": "^1.0.11", "@superawesome/freekws-email-builder": "^21.9.0", "@superawesome/freekws-http-nestjs-service": "^2.0.15", "@superawesome/freekws-kafka": "^1.0.4", "@superawesome/freekws-metrics-nestjs-service": "^4.2.0", "@superawesome/freekws-nestjs-audit": "^7.3.1", "@superawesome/freekws-nestjs-guards": "^15.3.2", "@superawesome/freekws-nestjs-http-interceptors": "^12.2.0", "@superawesome/freekws-preverification-service-common": "^1.5.0", "@superawesome/freekws-queue-messages": "^55.9.2", "@superawesome/freekws-redis-nestjs-service": "^6.2.2", "@superawesome/freekws-regional-config": "^7.8.0", "@superawesome/freekws-service-activation-service-common": "^2.1.0", "@superawesome/freekws-settings-common": "^19.0.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "config": "^3.3.12", "date-fns": "^4.1.0", "dd-trace": "^5.22.0", "jsonwebtoken": "^9.0.2", "keycloak-connect": "^18.0.0", "lodash": "^4.17.21", "nestjs-ddtrace": "^5.0.0", "pg": "^8.13.0", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "typeorm": "^0.3.20", "typeorm-transactional": "^0.5.0", "yaml": "^2.6.1"}, "devDependencies": {"@cucumber/cucumber": "^8.11.1", "@cucumber/pretty-formatter": "^1.0.1", "@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/swagger": "^7.4.2", "@nestjs/testing": "^10.4.7", "@stylistic/eslint-plugin": "^2.10.1", "@superawesome/eslint-config-freekws": "^1.1.0", "@superawesome/freekws-common-http-client": "^1.2.2", "@superawesome/freekws-nestjs-apiblueprint": "^4.2.0", "@superawesome/freekws-semantic-release": "^2.1.0", "@superawesome/freekws-test-keycloak": "^14.4.2", "@types/axios": "^0.14.0", "@types/config": "^3.3.5", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/jsonwebtoken": "^9.0.7", "@types/node": "^20.3.1", "@types/opossum": "^8.1.8", "@types/supertest": "^6.0.0", "@typescript-eslint/eslint-plugin": "^8.13.0", "@typescript-eslint/parser": "^8.13.0", "cross-env": "^7.0.3", "cucumber-tsflow": "^4.4.4", "dotenv": "^16.4.7", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-unused-imports": "^4.1.4", "fastify": "^4.28.1", "husky": "^8.0.3", "jest": "^29.5.0", "jest-circus": "^29.7.0", "jest-junit": "^16.0.0", "kafkajs": "^2.2.4", "lint-staged": "^13.1.2", "nock": "^13.5.5", "npm-watch": "^0.13.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typeorm-fixtures-cli": "^4.0.0", "typescript": "^5.1.3"}, "overrides": {"@superawesome/freekws-settings-common": "$@superawesome/freekws-settings-common"}, "engines": {"node": ">= 18", "npm": ">= 9"}}