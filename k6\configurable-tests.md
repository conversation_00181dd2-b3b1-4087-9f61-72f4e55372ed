# Configurable API Load Testing

This directory contains configurable K6 load tests for testing the FreeKWS Classic Wrapper backend
API endpoints.

## Overview

The configurable test solution consists of two main files:

1. `endpoints.ts` - A library of exported functions for testing all API endpoints
2. `configurable-test.ts` - A sample test script demonstrating how to use these functions

## Features

- Each endpoint has its own dedicated function
- All functions support customizable payloads and parameters
- Preconfigured workflows can be selected via environment variables

## Available Test Scripts

For testing locally, you can run some npm script commands for preconfigured tests e.g.,:

```shell
npm run test:users
```

## Environment Variables

Tests can be configured using the following environment variables:

| Variable      | Description                              | Default               |
|---------------|------------------------------------------|-----------------------|
| BASE_URL      | Base URL of the API to test              | http://localhost:3000 |
| CLIENT_ID     | OAuth client ID for authentication       | your-client-id        |
| CLIENT_SECRET | OAuth client secret for authentication   | your-client-secret    |
| APP_ID        | App ID to use in requests                | 207800543             |
| USER_ID       | User ID to use in requests               | 1944229051            |
| WORKFLOWS     | Comma-separated list of workflows to run | basic                 |
| VUS           | Number of virtual users                  | 1                     |
| DURATION      | Test duration                            | 30s                   |

## Creating Custom Test Scripts

You can easily create custom test scripts by importing the necessary functions from
`endpoints.ts`:

```typescript
import { getOAuthToken, getUser, updateParentEmail } from './endpoint-functions';

// Get authentication token
export function setup() {
    const token = getOAuthToken('your-client-id', 'your-client-secret');
    return { token };
}

// Run tests
export default function ({ token }) {
    // Get user
    getUser(token, '207800543', '1944229051');

    // Update parent email
    updateParentEmail(token, '207800543', '1944229051', {
        parentEmail: '<EMAIL>'
    });
}
```

## Adding New Endpoints

To add new endpoint functions, simply modify the `endpoints.ts` file following the same
pattern:

```typescript
export function newEndpoint(token: string, param1: string, body = {}) {
    const headers = addAuthHeader(token);

    const response = http.post(
        `${BASE_URL}/path/to/endpoint/${param1}`,
        JSON.stringify(body),
        { headers }
    );

    validateResponse(response);

    return response;
}
```

## Custom Workflows

You can create custom workflows by adding new entries to the `workflows` object in
`configurable-test.ts`:

```typescript
const workflows = {
    // ... existing workflows

    // Add your custom workflow
    myWorkflow: (token: string) => {
        const response1 = getUser(token, APP_ID, USER_ID);
        sleep(1);

        // Use data from response1 for the next call
        const userData = response1.json();
        requestUserPermissions(token, APP_ID, USER_ID, {
            parentEmail: userData.parentEmail,
            permissions: ['chat.voice']
        });
    }
};
```

Then run it with:

```
k6 run configurable-test.ts --env WORKFLOWS=myWorkflow
```

## Response Validation

By default, all responses are validated for:

- Correct status code (defaults to 200, but can be customized)
- Response time (defaults to < 500ms)

You can customize validation settings when calling the functions:

```typescript
validateResponse(response, { expectedStatus: 201, maxDuration: 1000 });
``` 