version: '3'
services:
  project:
    image: artifacts.ol.epicgames.net/supawe-kws-docker/superawesomeltd/freekws-classic-wrapper-backend:${TAG}
    user: "node:node"
    read_only: true
    tmpfs: /tmp
    security_opt:
      - no-new-privileges:true
    build:
      context: .
      target: builder
      args:
        ARTIFACTORY_TOKEN_PLATFORM: ${ARTIFACTORY_TOKEN_PLATFORM}
    environment:
      JEST_JUNIT_OUTPUT_DIR: ./reports/junit/
      GIT_AUTHOR_NAME: ${GIT_AUTHOR_NAME}
      GIT_AUTHOR_EMAIL: ${GIT_AUTHOR_EMAIL}
      GIT_COMMITTER_NAME: ${GIT_COMMITTER_NAME}
      GIT_COMMITTER_EMAIL: ${GIT_COMMITTER_EMAIL}
      GITHUB_TOKEN: ${GITHUB_TOKEN}
      GITHUB_ACTION: ${GITHUB_ACTION}
      CI: ${CI}
      CIRCLECI: ${CIRCLECI}
      CIRCLE_PR_NUMBER: ${CIRCLE_PR_NUMBER}
      CIRCLE_PULL_REQUEST: ${CIRCLE_PULL_REQUEST}
      CIRCLE_BUILD_NUM: ${CIRCLE_BUILD_NUM}
      CIRCLE_NODE_INDEX: ${CIRCLE_NODE_INDEX}
      CIRCLE_SHA1: ${CIRCLE_SHA1}
      CIRCLE_TAG: ${CIRCLE_TAG}
      CIRCLE_BRANCH: ${CIRCLE_BRANCH}
      CIRCLE_PROJECT_USERNAME: ${CIRCLE_PROJECT_USERNAME}
      CIRCLE_PROJECT_REPONAME: ${CIRCLE_PROJECT_REPONAME}
      ARTIFACTORY_TOKEN_PLATFORM: ${ARTIFACTORY_TOKEN_PLATFORM}
    volumes:
      - './.git:/srv/.git'
      - type: tmpfs
        target: /home/<USER>/.npm
        tmpfs:
          size: "12884901888"
