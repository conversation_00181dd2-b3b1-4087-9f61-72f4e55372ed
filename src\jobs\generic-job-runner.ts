import { NestApplication } from '@nestjs/core';

import { BaseJob } from './base-job';
import * as jobs from './job-list';

export class GenericJobRunner {
  constructor(protected app: NestApplication) {}

  async runJob(jobName: string, dryMode: boolean): Promise<void> {
    const chosenJob = (jobs as unknown as Record<string, typeof BaseJob>)[jobName];
    if (!chosenJob) {
      throw new Error(`No job with that name present: ${jobName}`);
    }
    return await new chosenJob(this.app, dryMode).run();
  }
}
