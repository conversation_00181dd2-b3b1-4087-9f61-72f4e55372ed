import { EWebhookName } from '@superawesome/freekws-queue-messages/webhook/webhook.dto';

export type WebhookNames = (typeof EWebhookName)[keyof typeof EWebhookName];
export const endpointTriggeredWebhookNames = [
  'families:guardian-request-expired',
  'settings:user-graduated',
  'families:user-added-to-family',
  'families:user-removed-from-family',
  'settings:effective-values-changed',
  'families:family-group-deleted',
] as const;

export type EndpointTriggeredWebhookNames = (typeof endpointTriggeredWebhookNames)[number];

export type OrgLevelWebhookPayload<T = unknown> = {
  name: EWebhookName;
  time: number;
  orgId: string;
  payload: T;
};

export type AppLevelWebhookPayload<T = unknown> = OrgLevelWebhookPayload<T> & {
  productId: string;
};
