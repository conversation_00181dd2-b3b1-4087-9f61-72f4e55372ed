import { Modu<PERSON> } from '@nestjs/common';
import { SALogger } from '@superawesome/freekws-common-logger';
import { Producer } from '@superawesome/freekws-kafka';
import { MetricsServiceModule, MetricsService } from '@superawesome/freekws-metrics-nestjs-service';

import { ConfigModule } from '../config/config.module';
import { ConfigService } from '../config/config.service';

export const KAFKA_PRODUCER = 'KAFKA_PRODUCER';

@Module({
  imports: [ConfigModule, MetricsServiceModule],
  providers: [
    MetricsService,
    {
      provide: KAFKA_PRODUCER,
      useFactory: (config: ConfigService, metricsService: MetricsService) => {
        return new Producer({
          brokers: config.getKafka().kafkaHost,
          metricClient: metricsService.metrics,
          logger: new SALogger(),
        });
      },
      inject: [ConfigService, MetricsService],
    },
  ],
  exports: [KAFKA_PRODUCER],
})
export class KafkaProducerModule {}
