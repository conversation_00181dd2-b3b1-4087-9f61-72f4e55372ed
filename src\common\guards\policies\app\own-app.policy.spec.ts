import { ForbiddenException } from '@nestjs/common';

import { IsOwnApp } from './own-app.policy';
import { OAuthFastifyRequest } from '../../oauth/types';

describe('IsOwnApp', () => {
  it('should be defined', () => {
    expect(new IsOwnApp()).toBeDefined();
  });

  it('should return false if JWT not presented in request', async () => {
    const policy = new IsOwnApp();

    await expect(
      policy.handle({
        raw: {},
      } as OAuthFastifyRequest),
    ).resolves.toEqual(false);
  });

  it('should return true for matched app id', async () => {
    const policy = new IsOwnApp();

    await expect(
      policy.handle({
        params: {
          appId: '123',
        },
        raw: {
          jwt: {
            appId: 123,
          },
        },
      } as OAuthFastifyRequest),
    ).resolves.toEqual(true);
  });

  it('should throw an error if app id not matched', async () => {
    const policy = new IsOwnApp();

    await expect(
      policy.handle({
        params: {
          appId: '125',
        },
        raw: {
          jwt: {
            appId: 123,
          },
        },
      } as OAuthFastifyRequest),
    ).rejects.toBeInstanceOf(ForbiddenException);
  });
});
