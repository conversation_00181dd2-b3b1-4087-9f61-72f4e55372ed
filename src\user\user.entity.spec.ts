import { User } from './user.entity';

describe('UserEntity Should', () => {
  it('convert a language unsupported in freeKws to en', async () => {
    const entity = new User();
    entity.language = 'ee';
    entity.checkAndUpdateLanguage();
    expect(entity.language).toEqual('en');
  });

  it('not convert a language supported in freeKws to en', async () => {
    const entity = new User();
    entity.language = 'de';
    entity.checkAndUpdateLanguage();
    expect(entity.language).toEqual('de');
  });
});
