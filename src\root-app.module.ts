import { Module } from '@nestjs/common';
import { HttpAdapterHost } from '@nestjs/core';
import { TypeOrmModule } from '@nestjs/typeorm';
import { FastifyMetricsService, MetricsService } from '@superawesome/freekws-metrics-nestjs-service';

import { CommonModule } from './common/common.module';
import { ExceptionsHandler } from './common/filters/exceptions';
import { ConfigModule } from './common/services/config/config.module';
import { ConfigService } from './common/services/config/config.service';
import { KeycloakModule } from './common/services/keycloak/keycloak.module';
import { CountriesModule } from './countries/countries.module';
import { EventsModule } from './events/events.module';
import { HealthcheckModule } from './healthcheck/healthcheck.module';
import { InternalAdminModule } from './internal-admin/internal-admin.module';
import { OauthModule } from './oauth/oauth.module';
import { OrgEnvModule } from './org-env/org-env.module';
import { UserModule } from './user/user.module';
import { WebhookModule } from './webhook/webhook.module';

@Module({
  imports: [
    HealthcheckModule,
    CommonModule,
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (config: ConfigService) => config.getTypeormConfig(),
    }),
    CountriesModule,
    OrgEnvModule,
    OauthModule,
    UserModule,
    OauthModule,
    WebhookModule,
    InternalAdminModule,
    EventsModule,
    KeycloakModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (config: ConfigService) => config.getKeycloak(),
    }),
  ],
  providers: [
    ExceptionsHandler,
    {
      provide: FastifyMetricsService,
      useFactory: (httpAdapterHost, metricsService) => new FastifyMetricsService(httpAdapterHost, metricsService),
      inject: [HttpAdapterHost, MetricsService],
    },
  ],
})
export class RootAppModule {}

export const AppModule = RootAppModule;
