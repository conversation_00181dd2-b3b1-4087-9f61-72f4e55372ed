import { check } from 'k6';
import http, { Response } from 'k6/http';
import { Options } from 'k6/options';

const BASE_URL = __ENV.BASE_URL || 'http://localhost:3000';
const clientId = __ENV.CLIENT_ID || 'your-client-id';
const clientSecret = __ENV.CLIENT_SECRET || 'your-client-secret';

export const options: Options = {
    vus: 1,
    duration: '1h',
    thresholds: {
        http_req_duration: ['p(95)<500'],
        http_req_failed: ['rate<0.01'],
    },
};

export function setup() {
    const response = http
        .post(`${BASE_URL}/oauth/token`, {
            client_id: clientId,
            client_secret: clientSecret,
            grant_type: "client_credentials",
            scope: "app"
        }, {
            headers: {
                "x-forwarded-host": "test.com",
                "Content-Type": "application/x-www-form-urlencoded"
            }
        });

    check(response, {
        'status is 200': (r) => r.status === 200,
        'response time is less than 500ms': (r) => r.timings.duration < 500,
    });

    const token = response.json('access_token');
    return { token };
}

function getUser(token: string) {
    const appId = "207800543"
    const userId = "1944229051"
    const res = http.get(`${BASE_URL}/v2/apps/${appId}/users/${userId}`, {
        headers: {
            "Authorization": `Bearer ${token}`
        }
    });

    check(res, {
        'status is 200': (r) => r.status === 200,
        'response time is less than 500ms': (r) => r.timings.duration < 500,
    });
}

export default function ({ token }: { token: string }) {
    const response = http
        .post(`${BASE_URL}/oauth/token`, {
            client_id: clientId,
            client_secret: clientSecret,
            grant_type: "client_credentials",
            scope: "app"
        }, {
            headers: {
                "x-forwarded-host": "test.com",
                "Content-Type": "application/x-www-form-urlencoded"
            }
        });

    check(response, {
        'status is 200': (r) => r.status === 200,
        'response time is less than 500ms': (r) => r.timings.duration < 500,
    });

    getUser(token)
}