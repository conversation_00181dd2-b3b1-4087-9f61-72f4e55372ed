import { ClientBuilder, HttpClient } from '@superawesome/freekws-common-http-client';
import { SALogger } from '@superawesome/freekws-common-logger';
import { Agent as HttpAgent } from 'http';
import { Agent as HttpsAgent } from 'https';

export class UATUtils {
  private static logger = new SALogger();
  private static clientBuilder = new ClientBuilder();

  static buildClassicWrapperClient(builder = UATUtils.clientBuilder): HttpClient {
    return builder.withLogger(this.logger).build({
      baseURL: 'http://classic-wrapper-backend:80',
      httpAgent: new HttpAgent({ keepAlive: true }),
      httpsAgent: new HttpsAgent({ keepAlive: true, rejectUnauthorized: true }),
      timeout: 30000,
    });
  }
}
