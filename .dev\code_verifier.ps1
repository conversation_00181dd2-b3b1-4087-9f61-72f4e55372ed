<#
.SYNOPSIS
    Generates PKCE code_verifier and code_challenge for OAuth 2.0 authorization code flow testing.

.DESCRIPTION
    This script creates a cryptographically random code_verifier and derives the corresponding
    code_challenge using either 'plain' or 'S256' method according to RFC 7636.

.PARAMETER Method
    The code challenge method to use. Valid values are 'plain' and 'S256'. Default is 'S256'.

.PARAMETER VerifierLength
    The length of the code_verifier to generate. Must be between 43 and 128 characters. Default is 64.

.PARAMETER CustomVerifier
    Optional parameter to use a custom code_verifier instead of generating a random one.

.EXAMPLE
    .\Generate-PKCE.ps1
    Generates a random code_verifier and S256 code_challenge.

.EXAMPLE
    .\Generate-PKCE.ps1 -Method plain
    Generates a random code_verifier and plain code_challenge.

.EXAMPLE
    .\Generate-PKCE.ps1 -CustomVerifier "my_custom_verifier_string"
    Uses the provided string as code_verifier and generates corresponding S256 code_challenge.
#>

param (
    [Parameter()]
    [ValidateSet('plain', 'S256')]
    [string]$Method = 'S256',

    [Parameter()]
    [ValidateRange(43, 128)]
    [int]$VerifierLength = 64,

    [Parameter()]
    [string]$CustomVerifier = ""
)

function ConvertTo-Base64Url {
    param (
        [Parameter(Mandatory = $true)]
        [byte[]]$Bytes
    )

    $base64 = [Convert]::ToBase64String($Bytes)
    $base64Url = $base64 -replace '\+', '-' -replace '/', '_' -replace '=', ''
    return $base64Url
}

$codeVerifier = ""
if ($CustomVerifier) {
    $codeVerifier = $CustomVerifier
    Write-Host "Using provided code_verifier" -ForegroundColor Yellow
} else {
    $randomBytes = New-Object byte[] $VerifierLength
    $rng = [System.Security.Cryptography.RandomNumberGenerator]::Create()
    $rng.GetBytes($randomBytes)
    $codeVerifier = ConvertTo-Base64Url -Bytes $randomBytes

    $codeVerifier = $codeVerifier.Substring(0, [Math]::Min($codeVerifier.Length, 128))
    if ($codeVerifier.Length -lt 43) {
        Write-Error "Generated code_verifier is too short. Try increasing the VerifierLength parameter."
        exit 1
    }
}

$codeChallenge = ""
if ($Method -eq 'plain') {
    $codeChallenge = $codeVerifier
} elseif ($Method -eq 'S256') {
    $sha256 = [System.Security.Cryptography.SHA256]::Create()
    $verifierBytes = [System.Text.Encoding]::ASCII.GetBytes($codeVerifier)
    $challengeBytes = $sha256.ComputeHash($verifierBytes)
    $codeChallenge = ConvertTo-Base64Url -Bytes $challengeBytes
}

Write-Host "`nPKCE for OAuth 2.0 Authorization Code Flow" -ForegroundColor Cyan
Write-Host "===========================================" -ForegroundColor Cyan
Write-Host "Code Challenge Method: $Method" -ForegroundColor Green
Write-Host "`nFor /oauth/authorise request (STEP 1):" -ForegroundColor Magenta
Write-Host "code_challenge: $codeChallenge" -ForegroundColor White
Write-Host "code_challenge_method: $Method" -ForegroundColor White
Write-Host "`nFor /oauth/token request (STEP 2):" -ForegroundColor Magenta
Write-Host "code_verifier: $codeVerifier" -ForegroundColor White

Write-Host "`n----- Sample Request for Step 1 (Getting Authorization Code) -----" -ForegroundColor Yellow
$step1 = @"
POST /oauth/authorise HTTP/1.1
Host: your-api-host
Authorization: Bearer YOUR_USER_ACCESS_TOKEN
Content-Type: application/x-www-form-urlencoded

response_type=code&
client_id=YOUR_CLIENT_ID&
redirect_uri=YOUR_REDIRECT_URI&
state=YOUR_STATE_VALUE&
code_challenge=$codeChallenge&
code_challenge_method=$Method
"@
Write-Host $step1 -ForegroundColor Gray

Write-Host "`n----- Sample Request for Step 2 (Exchanging Code for Token) -----" -ForegroundColor Yellow
$step2 = @"
POST /oauth/token HTTP/1.1
Host: your-api-host
Authorization: Basic YOUR_BASE64_ENCODED_CLIENT_ID_AND_SECRET
Content-Type: application/x-www-form-urlencoded

grant_type=authorization_code&
code=YOUR_AUTHORIZATION_CODE&
redirect_uri=YOUR_REDIRECT_URI&
code_verifier=$codeVerifier
"@
Write-Host $step2 -ForegroundColor Gray

Write-Host "`nWould you like to copy the code_verifier to clipboard? (Y/N)" -ForegroundColor Cyan
$response = Read-Host
if ($response -eq 'Y' -or $response -eq 'y') {
    $codeVerifier | Set-Clipboard
    Write-Host "code_verifier copied to clipboard!" -ForegroundColor Green
}

Write-Host "`nWould you like to copy the code_challenge to clipboard? (Y/N)" -ForegroundColor Cyan
$response = Read-Host
if ($response -eq 'Y' -or $response -eq 'y') {
    $codeChallenge | Set-Clipboard
    Write-Host "code_challenge copied to clipboard!" -ForegroundColor Green
}