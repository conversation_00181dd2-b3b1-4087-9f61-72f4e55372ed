import { BadRequestException, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { ModuleRef, Reflector } from '@nestjs/core';
import { EOAuthScope } from '@superawesome/freekws-classic-wrapper-common';

import { OauthGuard } from './oauth.guard';
import { JWKService } from '../../../oauth/jwk.service';
import { OrgEnvService } from '../../../org-env/org-env.service';

const mockReflector = {
  get: jest.fn().mockReturnValue([{ handle: () => true }]),
};

const moduleRefMock = {
  get: jest.fn(),
};

const mockJWK = {
  verify: jest.fn(),
};

const mockOrgEnvService = {
  getOrgEnvFromRequest: jest.fn(),
};

const mockGetRequest = jest.fn();

const context = {
  getHandler: jest.fn(),
  switchToHttp: () => ({
    getRequest: mockGetRequest,
  }),
} as unknown as ExecutionContext;

describe('OauthGuard', () => {
  let guard: OauthGuard;

  beforeAll(() => {
    guard = new OauthGuard(
      mockReflector as unknown as Reflector,
      moduleRefMock as unknown as ModuleRef,
      mockJWK as unknown as JWKService,
      mockOrgEnvService as unknown as OrgEnvService,
    );
  });

  it('should be defined', () => {
    expect(guard).toBeDefined();
  });

  it('should add jwt data to the request', async () => {
    const request = {
      params: {
        appId: '10000000',
      },
      headers: {
        authorization: 'Bearer token-data',
      },
      raw: {
        jwt: undefined,
      },
    };
    mockGetRequest.mockReturnValueOnce(request);
    mockJWK.verify.mockResolvedValueOnce({
      scope: EOAuthScope.APP,
      appId: 10000000,
    });

    mockOrgEnvService.getOrgEnvFromRequest.mockResolvedValueOnce({ id: '123' });

    await expect(guard.canActivate(context)).resolves.toBeTruthy();
    expect(request.raw.jwt).toEqual({
      scope: 'app',
      appId: 10000000,
    });
  });

  it('should throw an error if authorization header is missing', async () => {
    const request = {
      headers: {},
    };
    mockGetRequest.mockReturnValueOnce(request);

    await expect(guard.canActivate(context)).rejects.toBeInstanceOf(BadRequestException);
  });

  it('should throw an error for invalid authorization header', async () => {
    const request = {
      headers: {
        authorization: 'Basic creds',
      },
    };
    mockGetRequest.mockReturnValueOnce(request);

    await expect(guard.canActivate(context)).rejects.toBeInstanceOf(BadRequestException);
  });

  it('should throw an error for invalid token in header', async () => {
    const request = {
      headers: {
        authorization: 'Bearer token-data',
      },
    };
    mockGetRequest.mockReturnValueOnce(request);
    mockJWK.verify.mockRejectedValueOnce(new Error('Invalid JWT'));
    mockOrgEnvService.getOrgEnvFromRequest.mockResolvedValueOnce({ id: '123' });

    await expect(guard.canActivate(context)).rejects.toBeInstanceOf(UnauthorizedException);
  });
});
