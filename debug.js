console.log('🔍 Waiting for debugger to attach...');
console.log(
    '👉 Attach your debugger now, then the tests will start automatically');

const inspector = require('inspector');

function waitForDebugger() {
    if (inspector.url()) { // Check if a debugger is attached
        console.log('✅ Debugger attached, continuing execution...');
        return;
    }

    setTimeout(waitFor<PERSON>eb<PERSON><PERSON>, 100);
}

waitForDebugger();
