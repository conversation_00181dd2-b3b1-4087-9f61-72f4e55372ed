import { NotFoundException } from '@nestjs/common';
import { TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { createClientMock } from '@superawesome/freekws-clients-base/src/test-utils/mock';
import { Tiso31662 } from '@superawesome/freekws-regional-config';
import {
  EResponseFormat,
  ESettingBooleanOrder,
  ESettingConsentType,
  ESettingValueType,
  EUserSettingValueEffectiveSource,
  SETTINGS_BACKEND_API_CLIENT_INJECT_KEY,
  settingsBackendPlugin,
  UserSettingValueDTO,
  UserSettingValueShortDTO,
} from '@superawesome/freekws-settings-common';
import { AxiosError } from 'axios';
import { Repository } from 'typeorm';

import { SettingsService } from './settings.service';
import {
  ESettingsServiceErrorCodes,
  SettingsErrorResponse,
  SettingsServiceConsentNotRequestedError,
  SettingsServiceParentNotInFamilyError,
} from './types';
import { Testing } from '../../utils';
import { ClientKeycloakService } from '../keycloak/client-keycloak.service';
import { App } from '../../../app/app.entity';
import { OrgEnv } from '../../../org-env/org-env.entity';

const mockSettingsApi = createClientMock(settingsBackendPlugin, jest.fn);

const optInDefinition = {
  ageBracket: {
    consentType: ESettingConsentType.OPT_IN_UNVERIFIED,
  },
};
describe('SettingsService', () => {
  const TOKEN = 'TOKEN';
  let settingsService: SettingsService;
  let keycloakService: ClientKeycloakService;
  let appRepo: Repository<App>;

  const mockOrgEnv = {
    id: 'test-org-env-id',
    orgId: 'test-org-id',
  } as OrgEnv;

  const mockApp = {
    id: 1,
    productId: 'test-product-id',
    productEnvId: 'test-product-env-id',
    orgEnvId: 'test-org-env-id',
    orgEnv: mockOrgEnv,
  } as App;

  beforeEach(async () => {
    const module: TestingModule = await Testing.createModule({
      providers: [SettingsService, { provide: SETTINGS_BACKEND_API_CLIENT_INJECT_KEY, useValue: mockSettingsApi }],
    });

    settingsService = module.get<SettingsService>(SettingsService);
    keycloakService = module.get<ClientKeycloakService>(ClientKeycloakService);
    appRepo = module.get<Repository<App>>(getRepositoryToken(App));

    keycloakService.getClientToken = jest.fn().mockResolvedValue(TOKEN);

    jest.spyOn(appRepo, 'findOneOrFail').mockResolvedValue(mockApp);

    jest.spyOn(settingsService, 'getProductSettingsDefinition').mockResolvedValue([
      {
        version: 1,
        orgId: 'test-org-id',
        productId: 'test-product-id',
        namespace: 'chat',
        settings: [
          {
            settingName: 'voice',
            valueType: 'boolean',
            userHidden: false,
            required: false,
            autoReviewConsent: false,
            label: { en: 'Voice Chat' },
            parentNotice: { en: 'Allow voice chat' },
            userNotice: { en: 'Voice chat permission' },
            regions: [],
            irrevocable: false,
          },
          {
            settingName: 'text',
            valueType: 'boolean',
            userHidden: false,
            required: false,
            autoReviewConsent: false,
            label: { en: 'Text Chat' },
            parentNotice: { en: 'Allow text chat' },
            userNotice: { en: 'Text chat permission' },
            regions: [],
            irrevocable: true,
          },
        ],
      },
    ]);

    // Mock getUserSettings to return empty array by default
    jest.spyOn(settingsService, 'getUserSettings').mockResolvedValue([]);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('sendConsentEmail', () => {
    it('should return effective values of consent request', async () => {
      mockSettingsApi.getModule('userSettingValue').sendConsentEmailAtProductLevel.mockResolvedValueOnce({
        data: {
          response: {
            consentRequestId: '',
            consents: [],
            settings: [
              {
                namespace: 'chat',
                settingName: 'voice',
                preferredValue: true,
                preferredValueFromOrgLevel: false,
                effectiveValue: true,
                effectiveSource: EUserSettingValueEffectiveSource.DEFAULT,
                isOrgLevel: false,
              } as UserSettingValueShortDTO,
            ],
          },
          meta: {
            timestamp: '',
            requestId: '',
          },
        },
        status: 200,
      });

      const result = await settingsService.sendConsentEmail(
          {
            userId: 1,
            productId: '1',
            dob: '2010-10-10',
            parentEmail: '<EMAIL>',
            language: 'ca',
            location: 'US',
            permissions: ['chat.voice', 'geolocation'],
          },
          {
            clientId: '',
            secret: '',
          },
      );

      expect(result).toEqual([
        {
          namespace: 'chat',
          settingName: 'voice',
          preferredValue: true,
          preferredValueFromOrgLevel: false,
          effectiveValue: true,
          effectiveSource: EUserSettingValueEffectiveSource.DEFAULT,
          isOrgLevel: false,
        },
      ]);

      expect(appRepo.findOneOrFail).toHaveBeenCalledWith({
        where: {
          productId: '1',
          orgEnvId: '',
        },
        relations: {
          orgEnv: true,
        },
      });

      expect(settingsService.getProductSettingsDefinition).toHaveBeenCalledWith({
        orgId: 'test-org-id',
        orgEnvId: 'test-org-env-id',
        productId: 'test-product-id',
        productEnvId: 'test-product-env-id',
      });

      expect(settingsService.getUserSettings).toHaveBeenCalledWith({
        userId: 1,
        productId: '1',
        dob: '2010-10-10',
        parentEmail: '<EMAIL>',
        language: 'ca',
        location: 'US',
        permissions: ['chat.voice', 'geolocation'],
        dateOfBirth: '2010-10-10',
      }, {
        clientId: '',
        secret: '',
      });
    });

    it('should convert unsupported country code to ZZ in consent request', async () => {
      const mockSendConsentEmailAtProductLevel =
        mockSettingsApi.getModule('userSettingValue').sendConsentEmailAtProductLevel;
      mockSendConsentEmailAtProductLevel.mockResolvedValueOnce({
        data: {
          response: {
            consents: [],
            settings: [],
          },
          meta: {
            timestamp: '',
            requestId: '',
          },
        },
        status: 200,
      });

      await settingsService.sendConsentEmail(
        {
          userId: 1,
          productId: '1',
          dob: '2010-10-10',
          parentEmail: '<EMAIL>',
          language: 'ca',
          location: 'unsupported-country-code' as Tiso31662,
          permissions: ['chat.voice', 'geolocation'],
        },
        {
          clientId: '',
          secret: '',
        },
      );

      expect(mockSendConsentEmailAtProductLevel).toHaveBeenCalledWith(
        {
          params: {
            userId: '1',
            productId: '1',
          },
          body: {
            parentEmail: '<EMAIL>',
            language: 'ca',
            location: 'ZZ',
            dob: '2010-10-10',
            settings: [
              { namespace: 'chat', settingName: 'voice' },
              { namespace: 'default', settingName: 'geolocation' },
              { namespace: 'chat', settingName: 'text' },
            ],
          },
        },
        {
          headers: {
            Authorization: `Bearer ${TOKEN}`,
          },
        },
      );
    });

    it('should throw an error if parent email not presented for minor user', async () => {
      mockSettingsApi.getModule('userSettingValue').sendConsentEmailAtProductLevel.mockRejectedValueOnce({
        response: {
          data: {
            freekwsErrorCode: 'consent_request_missing_parent_email',
          },
        },
        status: 400,
      });

      await expect(
        settingsService.sendConsentEmail(
          {
            userId: 1,
            productId: '1',
            dob: '2010-10-10',
            language: 'ca',
            location: 'US',
            permissions: ['chat.voice', 'geolocation'],
          },
          {
            clientId: '',
            secret: '',
          },
        ),
      ).rejects.toThrow(ESettingsServiceErrorCodes.CONSENT_REQUEST_MISSING_PARENT_EMAIL);
    });

    it('should throw an error if request failed', async () => {
      const error = new AxiosError('SERVISE_UNAVAILABLE', '503');
      mockSettingsApi.getModule('userSettingValue').sendConsentEmailAtProductLevel.mockRejectedValueOnce(error);

      await expect(
        settingsService.sendConsentEmail(
          {
            userId: 1,
            productId: '1',
            dob: '2010-10-10',
            language: 'ca',
            location: 'US',
            permissions: ['chat.voice', 'geolocation'],
          },
          {
            clientId: '',
            secret: '',
          },
        ),
      ).rejects.toThrow('SERVISE_UNAVAILABLE');
    });

    it('should include irrevocable settings that are not already granted', async () => {
      // Mock getUserSettings to return no existing settings
      jest.spyOn(settingsService, 'getUserSettings').mockResolvedValueOnce([]);

      mockSettingsApi.getModule('userSettingValue').sendConsentEmailAtProductLevel.mockResolvedValueOnce({
        data: {
          response: {
            consentRequestId: '',
            consents: [],
            settings: [],
          },
          meta: {
            timestamp: '',
            requestId: '',
          },
        },
        status: 200,
      });

      await settingsService.sendConsentEmail(
          {
            userId: 1,
            productId: '1',
            dob: '2010-10-10',
            parentEmail: '<EMAIL>',
            language: 'ca',
            location: 'US',
            permissions: ['chat.voice'],
          },
          {
            clientId: '',
            secret: '',
          },
      );

      expect(mockSettingsApi.getModule('userSettingValue').sendConsentEmailAtProductLevel).toHaveBeenCalledWith(
          expect.objectContaining({
            body: expect.objectContaining({
              settings: expect.arrayContaining([
                { namespace: 'chat', settingName: 'voice' },
                { namespace: 'chat', settingName: 'text' }, // irrevocable setting added
              ]),
            }),
          }),
          expect.any(Object),
      );
    });

    it('should not include irrevocable settings that are already granted', async () => {
      // Mock getUserSettings to return an existing irrevocable setting that is already granted
      jest.spyOn(settingsService, 'getUserSettings').mockResolvedValueOnce([
        {
          namespace: 'chat',
          settingName: 'text',
          effectiveValue: true,
        } as any,
      ]);

      mockSettingsApi.getModule('userSettingValue').sendConsentEmailAtProductLevel.mockResolvedValueOnce({
        data: {
          response: {
            consentRequestId: '',
            consents: [],
            settings: [],
          },
          meta: {
            timestamp: '',
            requestId: '',
          },
        },
        status: 200,
      });

      await settingsService.sendConsentEmail(
          {
            userId: 1,
            productId: '1',
            dob: '2010-10-10',
            parentEmail: '<EMAIL>',
            language: 'ca',
            location: 'US',
            permissions: ['chat.voice'],
          },
          {
            clientId: '',
            secret: '',
          },
      );

      expect(mockSettingsApi.getModule('userSettingValue').sendConsentEmailAtProductLevel).toHaveBeenCalledWith(
          expect.objectContaining({
            body: expect.objectContaining({
              settings: [
                { namespace: 'chat', settingName: 'voice' },
                // irrevocable setting 'text' should not be included since it's already granted
              ],
            }),
          }),
          expect.any(Object),
      );
    });

    it('should include irrevocable settings that exist but are not granted (effectiveValue false)', async () => {
      // Mock getUserSettings to return an existing irrevocable setting that is not granted
      jest.spyOn(settingsService, 'getUserSettings').mockResolvedValueOnce([
        {
          namespace: 'chat',
          settingName: 'text',
          effectiveValue: false,
        } as any,
      ]);

      mockSettingsApi.getModule('userSettingValue').sendConsentEmailAtProductLevel.mockResolvedValueOnce({
        data: {
          response: {
            consentRequestId: '',
            consents: [],
            settings: [],
          },
          meta: {
            timestamp: '',
            requestId: '',
          },
        },
        status: 200,
      });

      await settingsService.sendConsentEmail(
          {
            userId: 1,
            productId: '1',
            dob: '2010-10-10',
            parentEmail: '<EMAIL>',
            language: 'ca',
            location: 'US',
            permissions: ['chat.voice'],
          },
          {
            clientId: '',
            secret: '',
          },
      );

      expect(mockSettingsApi.getModule('userSettingValue').sendConsentEmailAtProductLevel).toHaveBeenCalledWith(
          expect.objectContaining({
            body: expect.objectContaining({
              settings: expect.arrayContaining([
                { namespace: 'chat', settingName: 'voice' },
                { namespace: 'chat', settingName: 'text' }, // irrevocable setting included since not granted
              ]),
            }),
          }),
          expect.any(Object),
      );
    });

    it('should handle when no irrevocable settings exist', async () => {
      // Mock with no irrevocable settings
      jest.spyOn(settingsService, 'getProductSettingsDefinition').mockResolvedValueOnce([
        {
          version: 1,
          orgId: 'test-org-id',
          productId: 'test-product-id',
          namespace: 'chat',
          settings: [
            {
              settingName: 'voice',
              valueType: 'boolean',
              userHidden: false,
              required: false,
              autoReviewConsent: false,
              label: { en: 'Voice Chat' },
              parentNotice: { en: 'Allow voice chat' },
              userNotice: { en: 'Voice chat permission' },
              regions: [],
              irrevocable: false,
            },
          ],
        },
      ]);

      jest.spyOn(settingsService, 'getUserSettings').mockResolvedValueOnce([]);

      mockSettingsApi.getModule('userSettingValue').sendConsentEmailAtProductLevel.mockResolvedValueOnce({
        data: {
          response: {
            consentRequestId: '',
            consents: [],
            settings: [],
          },
          meta: {
            timestamp: '',
            requestId: '',
          },
        },
        status: 200,
      });

      await settingsService.sendConsentEmail(
          {
            userId: 1,
            productId: '1',
            dob: '2010-10-10',
            parentEmail: '<EMAIL>',
            language: 'ca',
            location: 'US',
            permissions: ['chat.voice'],
          },
          {
            clientId: '',
            secret: '',
          },
      );

      expect(mockSettingsApi.getModule('userSettingValue').sendConsentEmailAtProductLevel).toHaveBeenCalledWith(
          expect.objectContaining({
            body: expect.objectContaining({
              settings: [
                { namespace: 'chat', settingName: 'voice' },
                // No irrevocable settings should be added
              ],
            }),
          }),
          expect.any(Object),
      );
    });
  });

  describe('getUserSettings', () => {
    beforeEach(() => {
      jest.clearAllMocks();
      // Reset the getUserSettings spy to use the real implementation for these tests
      jest.spyOn(settingsService, 'getUserSettings').mockRestore();
    });

    it('should return user settings', async () => {
      mockSettingsApi.getModule('userSettingValue').getSettingsForUserAtProductLevel.mockResolvedValueOnce({
        data: {
          response: {
            settings: [],
          },
          meta: {
            timestamp: '',
            requestId: '',
          },
        },
        status: 200,
      });

      await expect(
        settingsService.getUserSettings({ userId: 1, productId: '' }, { clientId: '', secret: '' }),
      ).resolves.toEqual([]);
    });

    it('should convert unsupported country code to ZZ when getting user settings', async () => {
      const mockGetSettingsForUserAtProductLevel =
        mockSettingsApi.getModule('userSettingValue').getSettingsForUserAtProductLevel;
      mockGetSettingsForUserAtProductLevel.mockResolvedValueOnce({
        data: {
          response: {
            settings: [],
          },
          meta: {
            timestamp: '',
            requestId: '',
          },
        },
        status: 200,
      });

      await settingsService.getUserSettings(
        { userId: 1, productId: '', location: 'unsupported-country-code' as Tiso31662 },
        { clientId: '', secret: '' },
      );

      expect(mockGetSettingsForUserAtProductLevel).toHaveBeenCalledWith(
        {
          params: expect.any(Object),
          query: {
            format: expect.any(String),
            location: 'ZZ',
          },
        },
        {
          headers: expect.any(Object),
        },
      );
    });

    it('should should not send undefined', async () => {
      mockSettingsApi.getModule('userSettingValue').getSettingsForUserAtProductLevel.mockResolvedValueOnce({
        data: {
          response: {
            settings: [],
          },
          meta: {
            timestamp: '',
            requestId: '',
          },
        },
        status: 200,
      });

      await expect(
        settingsService.getUserSettings(
          { userId: 1, productId: '', dateOfBirth: undefined, location: 'AD' },
          { clientId: '', secret: '' },
        ),
      ).resolves.toEqual([]);
      expect(mockSettingsApi.getModule('userSettingValue').getSettingsForUserAtProductLevel).toHaveBeenCalledWith(
        {
          params: {
            userId: '1',
            productId: '',
          },
          query: {
            location: 'AD',
            format: EResponseFormat.FULL,
          },
        },
        { headers: expect.anything() },
      );
    });

    it('should return empty array when user settings not found (404)', async () => {
      const error = new AxiosError('Not Found', '404');
      error.response = { status: 404 } as any;
      error.status = 404;
      mockSettingsApi.getModule('userSettingValue').getSettingsForUserAtProductLevel.mockRejectedValueOnce(error);

      await expect(
          settingsService.getUserSettings({ userId: 1, productId: 'test' }, {
            clientId: '',
            secret: ''
          }),
      ).resolves.toEqual([]);
    });

    it('should propagate non-404 errors', async () => {
      const error = new AxiosError('Internal Server Error', '500');
      error.response = { status: 500 } as any;
      error.status = 500;
      mockSettingsApi.getModule('userSettingValue').getSettingsForUserAtProductLevel.mockRejectedValueOnce(error);

      await expect(
          settingsService.getUserSettings({ userId: 1, productId: 'test' }, {
            clientId: '',
            secret: ''
          }),
      ).rejects.toThrow('Internal Server Error');
    });
  });

  describe('removeTermsAndConditionsSetting', () => {
    it('should return empty for empty array', () => {
      expect(SettingsService.removeTermsAndConditionsSetting([])).toEqual([]);
    });

    it('should return empty if the only setting is terms and conditions', () => {
      expect(
        SettingsService.removeTermsAndConditionsSetting([
          {
            namespace: 'terms',
            settingName: 'app-terms-and-conditions',
            preferredValue: true,
            preferredValueFromOrgLevel: false,
            effectiveValue: true,
            isOrgLevel: false,
            effectiveSource: EUserSettingValueEffectiveSource.DEFAULT,
            definition: {
              ageBracket: {
                consentType: ESettingConsentType.OPT_OUT,
                defaultPreference: '',
              },
              orgId: '',
              namespace: '',
              settingName: '',
              valueType: ESettingValueType.BOOLEAN,
              translations: {},
              restrictiveOrder: ESettingBooleanOrder.FALSE_RESTRICTIVE_TRUE_PERMISSIVE,
              userHidden: false,
              userReadOnly: false,
              required: false,
            },
          },
        ] as UserSettingValueDTO[]),
      ).toEqual([]);
    });

    it('should return settings and skip terms and conditions', () => {
      const otherSetting: UserSettingValueDTO = {
        namespace: 'default',
        settingName: 'other setting',
        preferredValue: true,
        preferredValueFromOrgLevel: false,
        effectiveValue: true,
        isOrgLevel: false,
        effectiveSource: EUserSettingValueEffectiveSource.DEFAULT,
        definition: {
          ageBracket: {
            consentType: ESettingConsentType.OPT_OUT,
            defaultPreference: '',
          },
          orgId: '',
          namespace: '',
          settingName: '',
          valueType: ESettingValueType.BOOLEAN,
          translations: {},
          restrictiveOrder: ESettingBooleanOrder.FALSE_RESTRICTIVE_TRUE_PERMISSIVE,
          userHidden: false,
          userReadOnly: false,
          required: false,
        },
      };

      expect(
        SettingsService.removeTermsAndConditionsSetting([
          {
            namespace: 'terms',
            settingName: 'app-terms-and-conditions',
            preferredValue: true,
            preferredValueFromOrgLevel: false,
            effectiveValue: true,
            isOrgLevel: false,
            effectiveSource: EUserSettingValueEffectiveSource.DEFAULT,
            definition: {
              ageBracket: {
                consentType: ESettingConsentType.OPT_OUT,
                defaultPreference: '',
              },
              orgId: '',
              namespace: '',
              settingName: '',
              valueType: ESettingValueType.BOOLEAN,
              translations: {},
              restrictiveOrder: ESettingBooleanOrder.FALSE_RESTRICTIVE_TRUE_PERMISSIVE,
              userHidden: false,
              userReadOnly: false,
              required: false,
            },
          },
          otherSetting,
        ] as UserSettingValueDTO[]),
      ).toEqual([otherSetting]);
    });
  });

  describe('isOptIn', () => {
    it('should return false for empty setting', () => {
      expect(SettingsService.isOptIn()).toBeFalsy();
    });

    it('should return true for verified opt-in setting', () => {
      expect(
        SettingsService.isOptIn({
          definition: {
            ageBracket: {
              consentType: ESettingConsentType.OPT_IN_VERIFIED,
            },
          },
        } as UserSettingValueDTO),
      ).toBeTruthy();
    });

    it('should return true for unverified opt-in setting', () => {
      expect(
        SettingsService.isOptIn({
          definition: {
            ageBracket: {
              consentType: ESettingConsentType.OPT_IN_UNVERIFIED,
            },
          },
        } as UserSettingValueDTO),
      ).toBeTruthy();
    });

    it('should return false for opt-out setting', () => {
      expect(
        SettingsService.isOptIn({
          definition: {
            ageBracket: {
              consentType: ESettingConsentType.OPT_OUT,
            },
          },
        } as UserSettingValueDTO),
      ).toBeFalsy();
    });
  });

  describe('isOptOut', () => {
    it('should return false for empty setting', () => {
      expect(SettingsService.isOptOut(null as unknown as UserSettingValueDTO)).toBeFalsy();
    });

    it('should return true for opt-out setting', () => {
      expect(
        SettingsService.isOptOut({
          definition: {
            ageBracket: {
              consentType: ESettingConsentType.OPT_OUT,
            },
          },
        } as UserSettingValueDTO),
      ).toBeTruthy();
    });

    it('should return true for opt-in setting', () => {
      expect(
        SettingsService.isOptOut({
          definition: {
            ageBracket: {
              consentType: ESettingConsentType.OPT_IN_UNVERIFIED,
            },
          },
        } as UserSettingValueDTO),
      ).toBeFalsy();
    });
  });

  describe('transformSettingsToPermissions', () => {
    it('should return mapped permissions', () => {
      expect(
        SettingsService.transformSettingsToPermissions(
          [
            {
              namespace: 'chat',
              settingName: 'voice',
              preferredValue: true,
              preferredValueFromOrgLevel: false,
              effectiveValue: true,
              isOrgLevel: false,
              effectiveSource: EUserSettingValueEffectiveSource.DEFAULT,
              definition: {
                ageBracket: {
                  consentType: ESettingConsentType.OPT_OUT,
                  defaultPreference: '',
                },
                orgId: '',
                namespace: '',
                settingName: '',
                valueType: ESettingValueType.BOOLEAN,
                translations: {},
                restrictiveOrder: ESettingBooleanOrder.FALSE_RESTRICTIVE_TRUE_PERMISSIVE,
                userHidden: false,
                userReadOnly: false,
                required: false,
              },
            },
          ],
          ['chat.voice', 'default.whatever'],
        ),
      ).toEqual({
        'chat.voice': true,
        'default.whatever': null,
      });
    });

    it('should return permissions without namespace', () => {
      expect(
        SettingsService.transformSettingsToPermissions(
          [
            {
              namespace: 'chat',
              settingName: 'voice',
              preferredValue: true,
              preferredValueFromOrgLevel: false,
              effectiveValue: true,
              isOrgLevel: false,
              effectiveSource: EUserSettingValueEffectiveSource.DEFAULT,
              definition: {
                ageBracket: {
                  consentType: ESettingConsentType.OPT_OUT,
                  defaultPreference: '',
                },
                orgId: '',
                namespace: '',
                settingName: '',
                valueType: ESettingValueType.BOOLEAN,
                translations: {},
                restrictiveOrder: ESettingBooleanOrder.FALSE_RESTRICTIVE_TRUE_PERMISSIVE,
                userHidden: false,
                userReadOnly: false,
                required: false,
              },
            },
            {
              namespace: 'default',
              settingName: 'whatever',
              preferredValue: true,
              preferredValueFromOrgLevel: false,
              effectiveValue: true,
              isOrgLevel: false,
              effectiveSource: EUserSettingValueEffectiveSource.DEFAULT,
              definition: {
                ageBracket: {
                  consentType: ESettingConsentType.OPT_OUT,
                  defaultPreference: '',
                },
                orgId: '',
                namespace: '',
                settingName: '',
                valueType: ESettingValueType.BOOLEAN,
                translations: {},
                restrictiveOrder: ESettingBooleanOrder.FALSE_RESTRICTIVE_TRUE_PERMISSIVE,
                userHidden: false,
                userReadOnly: false,
                required: false,
              },
            },
          ],
          ['chat.voice', 'whatever'],
        ),
      ).toEqual({
        'chat.voice': true,
        whatever: true,
      });
    });
  });

  describe('transformSettingsToGroupedPermissions', () => {
    it('should throw an error if some permission is not found', () => {
      try {
        SettingsService.transformSettingsToGroupedPermissions([], ['chat.voice']);
      } catch (error) {
        expect(error).toBeInstanceOf(NotFoundException);
      }
    });

    it('should return mapped result permission', () => {
      expect(
        SettingsService.transformSettingsToGroupedPermissions(
          [
            {
              namespace: 'chat',
              settingName: 'voice',
              preferredValue: true,
              preferredValueFromOrgLevel: false,
              effectiveValue: true,
              isOrgLevel: false,
              definition: {
                ageBracket: {
                  consentType: ESettingConsentType.OPT_IN_VERIFIED,
                },
                translations: {
                  en: {
                    label: 'Voice chat',
                    parentNotice: 'Allow user to use voice chat',
                  },
                },
              },
            } as unknown as UserSettingValueDTO,
            {
              namespace: 'chat',
              settingName: 'text',
              preferredValue: true,
              preferredValueFromOrgLevel: false,
              effectiveValue: false,
              isOrgLevel: false,
              definition: {
                ageBracket: {
                  consentType: ESettingConsentType.OPT_IN_VERIFIED,
                },
                translations: {
                  en: {
                    label: 'Text chat',
                    parentNotice: 'Allow user to use text chat',
                  },
                },
              },
            } as unknown as UserSettingValueDTO,
            {
              namespace: 'default',
              settingName: 'whatever',
              preferredValue: true,
              preferredValueFromOrgLevel: false,
              effectiveValue: true,
              isOrgLevel: false,
              definition: {
                ageBracket: {
                  consentType: ESettingConsentType.OPT_OUT,
                },
                translations: {
                  en: {
                    label: 'Default permission',
                    parentNotice: 'Allow user to do everything',
                  },
                },
              },
            } as unknown as UserSettingValueDTO,
          ],
          ['chat.voice', 'chat.text', 'default.whatever'],
        ),
      ).toEqual({
        alreadyGrantedPerms: [
          {
            description: 'Allow user to do everything',
            displayName: 'Default permission',
            name: 'whatever',
          },
        ],
        automaticallyGrantedPerms: [],
        missingPermissions: expect.arrayContaining([
          {
            description: 'Allow user to use text chat',
            displayName: 'Text chat',
            name: 'chat.text',
          },
          {
            description: 'Allow user to use voice chat',
            displayName: 'Voice chat',
            name: 'chat.voice',
          },
        ]),
      });
    });
  });

  describe('resendConsentEmail', () => {
    it('should return revoked permissions', async () => {
      mockSettingsApi.getModule('userSettingValue').getSettingsForUserAtProductLevel.mockResolvedValueOnce({
        data: {
          response: {
            settings: [
              {
                namespace: 'chat',
                settingName: 'voice',
                preferredValue: true,
                preferredValueFromOrgLevel: false,
                effectiveValue: true,
                isOrgLevel: false,
                definition: {
                  ageBracket: {
                    consentType: ESettingConsentType.OPT_IN_VERIFIED,
                  },
                  translations: {
                    en: {
                      label: 'Voice chat',
                      parentNotice: 'Allow user to use voice chat',
                    },
                  },
                },
              } as unknown as UserSettingValueDTO,
              {
                namespace: 'chat',
                settingName: 'text',
                preferredValue: true,
                preferredValueFromOrgLevel: false,
                effectiveValue: false,
                isOrgLevel: false,
                definition: {
                  ageBracket: {
                    consentType: ESettingConsentType.OPT_IN_VERIFIED,
                  },
                  translations: {
                    en: {
                      label: 'Text chat',
                      parentNotice: 'Allow user to use text chat',
                    },
                  },
                },
              } as unknown as UserSettingValueDTO,
              {
                namespace: 'default',
                settingName: 'whatever',
                preferredValue: true,
                preferredValueFromOrgLevel: false,
                effectiveValue: true,
                isOrgLevel: false,
                consentRequestedAt: 1784393974,
                definition: {
                  ageBracket: {
                    consentType: ESettingConsentType.OPT_OUT,
                  },
                  translations: {
                    en: {
                      label: 'Default permission',
                      parentNotice: 'Allow user to do everything',
                    },
                  },
                },
              } as unknown as UserSettingValueDTO,
            ],
          },
          meta: {
            timestamp: '',
            requestId: '',
          },
        },
        status: 200,
      });
      mockSettingsApi.getModule('userSettingValue').sendConsentEmailAtProductLevel.mockResolvedValueOnce({
        data: {
          response: {
            consentRequestId: '',
            consents: [
              {
                consentId: '',
                consentName: '',
              },
            ],
            settings: [
              {
                namespace: 'default',
                settingName: 'whatever',
                preferredValue: true,
                preferredValueFromOrgLevel: false,
                effectiveValue: true,
                isOrgLevel: false,
                consentRequestedAt: 1784393974,
                definition: {
                  ageBracket: {
                    consentType: ESettingConsentType.OPT_OUT,
                  },
                  translations: {
                    en: {
                      label: 'Default permission',
                      parentNotice: 'Allow user to do everything',
                    },
                  },
                },
              } as unknown as UserSettingValueDTO,
            ],
          },
          meta: {
            timestamp: '',
            requestId: '',
          },
        },
        status: 200,
      });

      await expect(
        settingsService.resendConsentEmail(
          { userId: 1, productId: '1', parentEmail: 'email' },
          { clientId: '', secret: '' },
        ),
      ).resolves.toEqual(undefined);
    });

    it('should convert unsupported country code to ZZ in resend consent request', async () => {
      mockSettingsApi.getModule('userSettingValue').getSettingsForUserAtProductLevel.mockResolvedValueOnce({
        data: {
          response: {
            settings: [
              {
                namespace: 'default',
                settingName: 'whatever',
                preferredValue: true,
                preferredValueFromOrgLevel: false,
                effectiveValue: true,
                isOrgLevel: false,
                consentRequestedAt: 1784393974,
                definition: {
                  ageBracket: {
                    consentType: ESettingConsentType.OPT_OUT,
                  },
                  translations: {
                    en: {
                      label: 'Default permission',
                      parentNotice: 'Allow user to do everything',
                    },
                  },
                },
              } as unknown as UserSettingValueDTO,
            ],
          },
          meta: {
            timestamp: '',
            requestId: '',
          },
        },
        status: 200,
      });

      const mockSendConsentEmailAtProductLevel =
        mockSettingsApi.getModule('userSettingValue').sendConsentEmailAtProductLevel;
      mockSendConsentEmailAtProductLevel.mockResolvedValueOnce({
        data: {
          response: {
            consentRequestId: '',
            consents: [
              {
                consentId: '',
                consentName: '',
              },
            ],
            settings: [],
          },
          meta: {
            timestamp: '',
            requestId: '',
          },
        },
        status: 200,
      });

      await settingsService.resendConsentEmail(
        { userId: 1, productId: '1', parentEmail: 'email', location: 'unsupported-country-code' as Tiso31662 },
        { clientId: '', secret: '' },
      );

      expect(mockSendConsentEmailAtProductLevel).toHaveBeenCalledWith(
        {
          params: expect.any(Object),
          body: {
            parentEmail: expect.any(String),
            language: undefined,
            location: 'ZZ',
            dob: undefined,
            settings: expect.any(Array),
          },
        },
        {
          headers: expect.any(Object),
        },
      );
    });

    it('should throw an error if consent not requested', async () => {
      mockSettingsApi.getModule('userSettingValue').getSettingsForUserAtProductLevel.mockResolvedValueOnce({
        data: {
          response: {
            settings: [
              {
                namespace: 'chat',
                settingName: 'voice',
                preferredValue: true,
                preferredValueFromOrgLevel: false,
                effectiveValue: true,
                isOrgLevel: false,
                definition: {
                  ageBracket: {
                    consentType: ESettingConsentType.OPT_IN_VERIFIED,
                  },
                  translations: {
                    en: {
                      label: 'Voice chat',
                      parentNotice: 'Allow user to use voice chat',
                    },
                  },
                },
              } as unknown as UserSettingValueDTO,
              {
                namespace: 'chat',
                settingName: 'text',
                preferredValue: true,
                preferredValueFromOrgLevel: false,
                effectiveValue: false,
                isOrgLevel: false,
                definition: {
                  ageBracket: {
                    consentType: ESettingConsentType.OPT_IN_VERIFIED,
                  },
                  translations: {
                    en: {
                      label: 'Text chat',
                      parentNotice: 'Allow user to use text chat',
                    },
                  },
                },
              } as unknown as UserSettingValueDTO,
              {
                namespace: 'default',
                settingName: 'whatever',
                preferredValue: true,
                preferredValueFromOrgLevel: false,
                effectiveValue: true,
                isOrgLevel: false,
                consentRequestedAt: 1784393974,
                definition: {
                  ageBracket: {
                    consentType: ESettingConsentType.OPT_OUT,
                  },
                  translations: {
                    en: {
                      label: 'Default permission',
                      parentNotice: 'Allow user to do everything',
                    },
                  },
                },
              } as unknown as UserSettingValueDTO,
            ],
          },
          meta: {
            timestamp: '',
            requestId: '',
          },
        },
        status: 200,
      });
      mockSettingsApi.getModule('userSettingValue').sendConsentEmailAtProductLevel.mockResolvedValueOnce({
        data: {
          response: {
            consentRequestId: '',
            consents: [],
            settings: [
              {
                namespace: 'chat',
                settingName: 'voice',
                preferredValue: true,
                preferredValueFromOrgLevel: false,
                effectiveValue: true,
                effectiveSource: EUserSettingValueEffectiveSource.DEFAULT,
                isOrgLevel: false,
              } as UserSettingValueShortDTO,
            ],
          },
          meta: {
            timestamp: '',
            requestId: '',
          },
        },
        status: 200,
      });

      await expect(
        settingsService.resendConsentEmail(
          { userId: 1, productId: '1', parentEmail: 'email' },
          { clientId: '', secret: '' },
        ),
      ).rejects.toThrow(SettingsServiceConsentNotRequestedError);
    });

    it('should throw an error if parent not a part of user family', async () => {
      mockSettingsApi.getModule('userSettingValue').getSettingsForUserAtProductLevel.mockResolvedValueOnce({
        data: {
          response: {
            settings: [
              {
                namespace: 'chat',
                settingName: 'voice',
                preferredValue: true,
                preferredValueFromOrgLevel: false,
                effectiveValue: true,
                isOrgLevel: false,
                definition: {
                  ageBracket: {
                    consentType: ESettingConsentType.OPT_IN_VERIFIED,
                  },
                  translations: {
                    en: {
                      label: 'Voice chat',
                      parentNotice: 'Allow user to use voice chat',
                    },
                  },
                },
              } as unknown as UserSettingValueDTO,
              {
                namespace: 'chat',
                settingName: 'text',
                preferredValue: true,
                preferredValueFromOrgLevel: false,
                effectiveValue: false,
                isOrgLevel: false,
                definition: {
                  ageBracket: {
                    consentType: ESettingConsentType.OPT_IN_VERIFIED,
                  },
                  translations: {
                    en: {
                      label: 'Text chat',
                      parentNotice: 'Allow user to use text chat',
                    },
                  },
                },
              } as unknown as UserSettingValueDTO,
              {
                namespace: 'default',
                settingName: 'whatever',
                preferredValue: true,
                preferredValueFromOrgLevel: false,
                effectiveValue: true,
                isOrgLevel: false,
                consentRequestedAt: 1784393974,
                definition: {
                  ageBracket: {
                    consentType: ESettingConsentType.OPT_OUT,
                  },
                  translations: {
                    en: {
                      label: 'Default permission',
                      parentNotice: 'Allow user to do everything',
                    },
                  },
                },
              } as unknown as UserSettingValueDTO,
            ],
          },
          meta: {
            timestamp: '',
            requestId: '',
          },
        },
        status: 200,
      });
      mockSettingsApi.getModule('userSettingValue').sendConsentEmailAtProductLevel.mockRejectedValueOnce(
        new AxiosError<SettingsErrorResponse>(
          'ERR_BAD_REQUEST',
          '400',
          {},
          {},
          {
            data: {
              error: {
                statusCode: 400,
                code: '',
                errorCode: ESettingsServiceErrorCodes.CONSENT_REQUEST_PARENT_NOT_PART_OF_CHILD_FAMILY,
                message: '',
              },
              meta: {
                requestId: '',
                timestamp: new Date().toISOString(),
              },
            },
            status: 400,
            statusText: 'ERR_BAD_REQUEST',
            headers: {},
            config: {},
          },
        ),
      );

      await expect(
        settingsService.resendConsentEmail(
          { userId: 1, productId: '1', parentEmail: 'email' },
          { clientId: '', secret: '' },
        ),
      ).rejects.toThrow(SettingsServiceParentNotInFamilyError);
    });
  });

  describe('transformSettingsToAllPermissions', () => {
    it('maps permission value to null when value not set by parent and effective value is false', () => {
      const permissions = SettingsService.transformSettingsToAllPermissions([
        {
          namespace: 'chat',
          settingName: 'voice',
          effectiveValue: false,
          parentLimitUpdatedAt: undefined,
        } as UserSettingValueDTO,
      ]);
      expect(permissions).toEqual({
        'chat.voice': null,
      });
    });

    it('maps permission value to true', () => {
      const permissions = SettingsService.transformSettingsToAllPermissions([
        {
          namespace: 'chat',
          settingName: 'voice',
          effectiveValue: true,
          parentLimitUpdatedAt: 1,
          definition: optInDefinition,
        } as UserSettingValueDTO,
      ]);
      expect(permissions).toEqual({
        'chat.voice': true,
      });
    });

    it('maps permission value to false', () => {
      const permissions = SettingsService.transformSettingsToAllPermissions([
        {
          namespace: 'chat',
          settingName: 'voice',
          effectiveValue: false,
          parentLimitUpdatedAt: 1,
          definition: optInDefinition,
        } as UserSettingValueDTO,
      ]);
      expect(permissions).toEqual({
        'chat.voice': false,
      });
    });

    it('omits namespace when namespace is default', () => {
      const permissions = SettingsService.transformSettingsToAllPermissions([
        {
          namespace: 'default',
          settingName: 'voice',
          effectiveValue: false,
          parentLimitUpdatedAt: 1,
          definition: optInDefinition,
        } as UserSettingValueDTO,
      ]);
      expect(permissions).toEqual({
        voice: false,
      });
    });
  });

  describe('sendGenerateConsentRequest', () => {
    it('should trigger generation of a consent request', async () => {
      const userId = 1;
      const productId = '2';

      const generatedConsentRequest = jest.fn();
      mockSettingsApi.getModule('userSettingValue').generateConsentRequestAtProductLevelRequest =
        generatedConsentRequest;

      await settingsService.sendGenerateConsentRequest(userId, productId, {
        clientId: 'client-id',
        secret: 'top-secret',
      });

      expect(generatedConsentRequest).toHaveBeenCalledWith(
        {
          params: {
            userId: `${userId}`,
            productId: `${productId}`,
          },
        },
        {
          headers: {
            Authorization: `Bearer ${TOKEN}`,
          },
        },
      );
    });
  });

  describe('country codes', () => {
    test.each(['JA', null, undefined, '', ' '])(
      'unsupported codes should be converted to ZZ',
      async (countryCode?: string) => {
        expect(SettingsService.convertUnsupportedCountryCode(countryCode as Tiso31662)).toBe('ZZ');
      },
    );

    test.each(['AD', 'GB', 'ES', 'TW', 'ZZ', 'gb'])(
      'supported codes should not be converted',
      async (countryCode?: string) => {
        expect(SettingsService.convertUnsupportedCountryCode(countryCode as Tiso31662)).toBe(countryCode);
      },
    );
  });

  describe('deleteUserSettingsAtProductLevel', () => {
    it('should delete user settings at product level and return the response', async () => {
      const userId = 123;
      const productId = 'product-123';
      const settings = [
        { namespace: 'chat', settingName: 'voice' },
        { namespace: 'default', settingName: 'whatever' },
      ];
      const mockResponse = {
        data: {
          response: { success: true, settings: [] },
          meta: {
            timestamp: '',
            requestId: '',
          },
        },
        status: 200,
      };

      mockSettingsApi
        .getModule('userSettingValue')
        .deletePreferredValueRequestAtProductLevel.mockResolvedValueOnce(mockResponse);

      const result = await settingsService.deleteUserSettingsAtProductLevel(userId, productId, settings, {
        clientId: '',
        secret: '',
      });

      expect(
        mockSettingsApi.getModule('userSettingValue').deletePreferredValueRequestAtProductLevel,
      ).toHaveBeenCalledWith(
        {
          params: {
            userId: '123',
            productId: 'product-123',
          },
          body: {
            settings,
          },
        },
        {
          headers: {
            Authorization: `Bearer ${TOKEN}`,
          },
        },
      );
      expect(result).toEqual({ success: true, settings: [] });
    });

    it('should propagate errors when deleting user settings', async () => {
      const error = new Error('API Error');
      mockSettingsApi
        .getModule('userSettingValue')
        .deletePreferredValueRequestAtProductLevel.mockRejectedValueOnce(error);

      await expect(
        settingsService.deleteUserSettingsAtProductLevel(123, 'product-123', [], { clientId: '', secret: '' }),
      ).rejects.toThrow('API Error');
    });
  });

  describe('deleteUser', () => {
    it('should delete a user', async () => {
      const userId = 123;

      await settingsService.deleteUser(userId, { clientId: '', secret: '' });

      expect(mockSettingsApi.getModule('userSettingValue').deleteUserRequest).toHaveBeenCalledWith(
        {
          params: {
            userId: '123',
          },
        },
        {
          headers: {
            Authorization: `Bearer ${TOKEN}`,
          },
        },
      );
    });

    it('should propagate errors when deleting a user', async () => {
      const error = new Error('API Error');
      mockSettingsApi.getModule('userSettingValue').deleteUserRequest.mockRejectedValueOnce(error);

      await expect(settingsService.deleteUser(123, { clientId: '', secret: '' })).rejects.toThrow('API Error');
    });
  });
});
