import * as Amplitude from '@amplitude/node';
import { Response, Status } from '@amplitude/node';
import { Injectable } from '@nestjs/common';
import { SALogger } from '@superawesome/freekws-common-logger';
import { TKWSParentAccessToken, TKWSToken, TKWSTokenClaim } from '@superawesome/freekws-nestjs-guards';

import { ConfigService } from '../common/services/config/config.service';

type TEventProperties = {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [key: string]: any;
};

@Injectable()
export class AmplitudeService {
  private client: Amplitude.NodeClient;
  private logger = new SALogger();

  constructor(configService: ConfigService) {
    const { apiKey, serverUrl } = configService.config.amplitude;
    this.client = Amplitude.init(apiKey, {
      serverUrl: serverUrl,
      optOut: false,
    });
  }

  async logEvent(
    eventType: string,
    accessToken: TKWSParentAccessToken | TKWSToken<TKWSTokenClaim>,
    eventProperties?: TEventProperties,
  ): Promise<void> {
    const event: Amplitude.Event = {
      user_id: accessToken.content?.jti,
      event_type: eventType,
      event_properties: eventProperties,
      user_properties: {
        ...accessToken.content?.kws,
        repository: 'freekws-parent-portal-backend',
      },
    };

    await this.sendToAmplitude(event);
  }

  async logWithoutToken(eventType: string, eventProperties?: TEventProperties): Promise<void> {
    const event: Amplitude.Event = {
      user_id: 'missing_token',
      event_type: eventType,
      event_properties: eventProperties,
      user_properties: {
        repository: 'freekws-parent-portal-backend',
      },
    };

    await this.sendToAmplitude(event);
  }

  async sendToAmplitude(event: Amplitude.Event): Promise<void> {
    try {
      const logResponse = await this.client.logEvent(event);

      this.checkForError(logResponse);
      // Send any events that are currently queued for sending.
      // Will automatically happen on the next event loop.
      const flushResponse = await this.client.flush();
      if (flushResponse.status !== Status.Skipped) {
        this.checkForError(flushResponse);
      }
    } catch (error) {
      this.logger.error('amplitude logEvent error', error);
    }
  }

  private checkForError(response: Response) {
    if (response.status !== Status.Success) {
      this.logger.error(`Response code ${response.statusCode}, status: ${response.status}`);
      switch (response.status) {
        case Status.Invalid:
        case Status.PayloadTooLarge:
        case Status.RateLimit: {
          this.logger.error(`Response body: ${response.body}`);
          break;
        }
      }
    }
  }
}
