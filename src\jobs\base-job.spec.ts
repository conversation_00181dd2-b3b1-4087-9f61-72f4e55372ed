import { NestApplication } from '@nestjs/core';
import { SALogger } from '@superawesome/freekws-common-logger';

import { BaseJob } from './base-job';

jest.mock('@superawesome/freekws-common-logger');

describe('BaseJob', () => {
  let job: BaseJob;
  const appMock = jest.fn();

  it('should run in dry mode', async () => {
    const mockSALoggerInsatance = (SALogger as jest.Mock).mock.instances[0];
    const mockInfo = mockSALoggerInsatance.info;
    job = new BaseJob(appMock as unknown as NestApplication);
    await job.run();
    expect(SALogger).toHaveBeenCalled();
    expect(mockInfo.mock.calls[0][0]).toEqual('RUNNING IN DRY MODE');
  });

  it('should NOT run in dry mode', async () => {
    const mockSALoggerInsatance = (SALogger as jest.Mock).mock.instances[0];
    const mockInfo = mockSALoggerInsatance.info;
    job = new BaseJob(appMock as unknown as NestApplication, false);
    await job.run();
    expect(SALogger).toHaveBeenCalled();
    expect(mockInfo.mock.calls[1][0]).toEqual('RUNNING IN PRODUCTION MODE');
  });
});
