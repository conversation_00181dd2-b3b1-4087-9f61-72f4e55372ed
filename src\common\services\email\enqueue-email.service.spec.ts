﻿import { Test, TestingModule } from '@nestjs/testing';
import { BrandingDTO } from '@superawesome/freekws-branding-service-common';
import { SALogger } from '@superawesome/freekws-common-logger';
import {
  FreeKWSEmailModule,
  FreeKWSEmailService,
  TUserChildChangedPasswordEmailData,
  TUserForgottenPasswordEmailData,
} from '@superawesome/freekws-email-builder';
import { EmailDTO, EmailMessageService } from '@superawesome/freekws-queue-messages/email';
import { v4 as uuidv4 } from 'uuid';

import { EmailEnqueueService } from './email-enqueue.service';
import { IEnqueueChildChangedPasswordEmailParams, IEnqueueForgottenPasswordEmailParams } from './types';
import { Utils } from '../../../../test/utils';
import { BrandingService } from '../branding/branding.service';
import { ConfigService } from '../config/config.service';
import { KAFKA_PRODUCER } from '../kafka/kafka-producer.module';

const kafkaProducerMock = {
  sendMessage: jest.fn(),
};

const configServiceMock = {
  getEmailService: () => ({
    footer: {
      logoSrc: 'logo.png',
    },
    header: {
      headerSrc: 'logo.png',
    },
    kafkaTopic: 'testTopic',
  }),
  getLinks: () => ({
    kwsFaqLink: 'https://faq-link',
    kwsPrivacyPolicyLink: 'https://privacy-link',
    kwsTermsLink: 'https://terms-link',
  }),
  config: {
    environment: 'development',
  },
  getKafka: () => ({
    kafkaHost: 'testHost',
    outOfOrderTopic: 'out-of-order-topic',
  }),
};

const mockEmail = {
  html: 'whatever',
  subject: 'whatever',
  kind: 'whatever',
};

const freeKwsEmailServiceMock = {
  buildUserForgottenPasswordEmail: jest.fn().mockReturnValue(mockEmail),
  buildUserChildChangedPasswordEmail: jest.fn().mockReturnValue(mockEmail),
};

const emailMessageServiceMock = {
  setKafkaProducer: jest.fn(),
  send: jest.fn(),
};

const brandingServiceMock = {
  getBrandings: jest.fn(),
  getCustomCopy: jest.fn(),
};

const translationMock: BrandingDTO = {
  environmentId: '',
  language: '',
  bannerUrl: 'logo.png',
  customerSupportUrl: '',
  font: '',
  fromEmailAddress: '<EMAIL>',
  iconUrl: '',
  name: 'PLACEHOLDER name',
  primaryColor: '',
  privacyPolicyUrl: 'http://customer-privacy.com',
  theme: 'light',
  parentPortalBackgroundUrl: '',
  parentPortalLogoUrl: '',
  parentSupportUrl: 'https://parent.kidswebservices.com/support',
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
};

const orgId = uuidv4();
const orgName = 'fake-org-id';
const orgEnvId = uuidv4();

describe('EmailEnqueueService', () => {
  let serviceUnderTest: EmailEnqueueService;
  const saLogger = new SALogger();

  beforeEach(async () => {
    jest.clearAllMocks();

    const module: TestingModule = await Test.createTestingModule({
      imports: [FreeKWSEmailModule],
      providers: [
        EmailEnqueueService,
        {
          provide: FreeKWSEmailService,
          useValue: freeKwsEmailServiceMock,
        },
        {
          provide: SALogger,
          useValue: saLogger,
        },
        {
          provide: ConfigService,
          useValue: configServiceMock,
        },
        {
          provide: KAFKA_PRODUCER,
          useValue: kafkaProducerMock,
        },
        {
          provide: EmailMessageService,
          useValue: emailMessageServiceMock,
        },
        {
          provide: BrandingService,
          useValue: brandingServiceMock,
        },
      ],
    }).compile();

    serviceUnderTest = module.get<EmailEnqueueService>(EmailEnqueueService);
  });

  describe('constructor', () => {
    it('should set producer broker', async () => {
      expect(serviceUnderTest).toBeTruthy();
      expect(emailMessageServiceMock.setKafkaProducer).toHaveBeenCalledWith(kafkaProducerMock);
    });
  });

  describe('enqueueForgottenPasswordEmail', () => {
    const emailParamsInput: IEnqueueForgottenPasswordEmailParams = {
      language: 'en-CA',
      kwsFaqLink: 'https://faq-link',
      customerPrivacyPolicyLink: 'https://customer.com/privacy',
      customPrimaryColour: '#FFFFFF',
      resetPasswordLink: 'https://kws.com/reset-password',
      toEmailAddress: '<EMAIL>',
    };

    const expectedEmailBuilderAttributes: TUserForgottenPasswordEmailData = {
      footer: {
        logoSrc: configServiceMock.getEmailService().footer.logoSrc,
      },
      header: {
        src: configServiceMock.getEmailService().header.headerSrc,
      },
      kwsPrivacyPolicyLink: configServiceMock.getLinks().kwsPrivacyPolicyLink,
      kwsTermsLink: configServiceMock.getLinks().kwsTermsLink,
      kwsFaqLink: configServiceMock.getLinks().kwsFaqLink,
      appName: 'PLACEHOLDER name',
      orgName: orgName,
      customPrimaryColour: '',
      ctaLink: 'https://kws.com/reset-password',
      language: 'en-CA',
      customerPrivacyPolicyLink: translationMock.privacyPolicyUrl,
    };

    const expectedEmailAttributes: EmailDTO = {
      subject: mockEmail.subject,
      html: mockEmail.html,
      fromEmail: '<EMAIL>',
      toEmail: '<EMAIL>',
      fromName: 'PLACEHOLDER name',
      tags: {
        environment: configServiceMock.config.environment,
        repository: 'freekws-classic-wrapper-backend',
        kind: 'whatever',
        mode: '',
      },
      metadata: { orgId, orgEnvId },
      orgId: orgId,
    } as EmailDTO;

    it('should send correct attributes to email builder and email service', async () => {
      brandingServiceMock.getCustomCopy.mockRejectedValue(
        Utils.axiosErrorFactory(404, { error: { statusCode: 404, message: 'NOT_FOUND' } }),
      );

      await serviceUnderTest.enqueueForgottenPasswordEmail(orgId, orgName, orgEnvId, emailParamsInput, translationMock);

      expect(freeKwsEmailServiceMock.buildUserForgottenPasswordEmail).toHaveBeenCalledWith(
        'en-CA',
        expect.objectContaining(expectedEmailBuilderAttributes),
        undefined,
      );
      expect(emailMessageServiceMock.send).toHaveBeenCalledWith(
        expectedEmailAttributes,
        configServiceMock.getEmailService().kafkaTopic,
      );
    });

    it('should bubble the error if sending email fails', async () => {
      const expectedException = new Error('some error');
      emailMessageServiceMock.send.mockRejectedValueOnce(expectedException);

      await expect(
        serviceUnderTest.enqueueForgottenPasswordEmail(orgId, orgName, orgEnvId, emailParamsInput, translationMock),
      ).rejects.toThrow(expectedException);
    });

    it('should call branding service to get custom copy and send to email builder and email service', async () => {
      const customCopy = {
        environmentId: orgEnvId,
        language: 'en',
        terms: {
          userForgottenPasswordGreeting: 'Howdy!',
        },
        createdAt: '2025-02-27T10:05:41.179Z',
        updatedAt: '2025-02-27T10:05:41.179Z',
      };

      brandingServiceMock.getCustomCopy.mockResolvedValue(customCopy);

      await serviceUnderTest.enqueueForgottenPasswordEmail(orgId, orgName, orgEnvId, emailParamsInput, translationMock);

      expect(freeKwsEmailServiceMock.buildUserForgottenPasswordEmail).toHaveBeenCalledWith(
        'en-CA',
        expect.objectContaining(expectedEmailBuilderAttributes),
        { userForgottenPasswordGreeting: 'Howdy!' },
      );
      expect(emailMessageServiceMock.send).toHaveBeenCalledWith(
        expectedEmailAttributes,
        configServiceMock.getEmailService().kafkaTopic,
      );
    });
  });

  describe('enqueueChildChangedPasswordEmail', () => {
    const emailParamsInput: IEnqueueChildChangedPasswordEmailParams = {
      language: 'en-CA',
      kwsFaqLink: 'https://faq-link',
      customerPrivacyPolicyLink: 'https://customer.com/privacy',
      customPrimaryColour: '#FFFFFF',
      childUsername: 'child-username',
      supportEmailAddress: '<EMAIL>',
      toEmailAddress: '<EMAIL>',
    };

    const expectedEmailBuilderAttributes: TUserChildChangedPasswordEmailData = {
      footer: {
        logoSrc: configServiceMock.getEmailService().footer.logoSrc,
      },
      header: {
        src: configServiceMock.getEmailService().header.headerSrc,
      },
      kwsPrivacyPolicyLink: configServiceMock.getLinks().kwsPrivacyPolicyLink,
      kwsTermsLink: configServiceMock.getLinks().kwsTermsLink,
      kwsFaqLink: configServiceMock.getLinks().kwsFaqLink,
      appName: 'PLACEHOLDER name',
      orgName: orgName,
      customPrimaryColour: '',
      childUsername: 'child-username',
      language: 'en-CA',
      customerPrivacyPolicyLink: translationMock.privacyPolicyUrl,
      supportEmailAddress: '<EMAIL>',
    };

    const expectedEmailAttributes: EmailDTO = {
      subject: mockEmail.subject,
      html: mockEmail.html,
      fromEmail: '<EMAIL>',
      toEmail: '<EMAIL>',
      fromName: 'PLACEHOLDER name',
      tags: {
        environment: configServiceMock.config.environment,
        repository: 'freekws-classic-wrapper-backend',
        kind: 'whatever',
        mode: '',
      },
      metadata: { orgId, orgEnvId },
      orgId: orgId,
    } as EmailDTO;

    it('should send correct attributes to email builder and email service', async () => {
      brandingServiceMock.getCustomCopy.mockRejectedValue(
        Utils.axiosErrorFactory(404, { error: { statusCode: 404, message: 'NOT_FOUND' } }),
      );

      await serviceUnderTest.enqueueChildChangedPasswordEmail(
        orgId,
        orgName,
        orgEnvId,
        emailParamsInput,
        translationMock,
      );

      expect(freeKwsEmailServiceMock.buildUserChildChangedPasswordEmail).toHaveBeenCalledWith(
        'en-CA',
        expect.objectContaining(expectedEmailBuilderAttributes),
        undefined,
      );
      expect(emailMessageServiceMock.send).toHaveBeenCalledWith(
        expectedEmailAttributes,
        configServiceMock.getEmailService().kafkaTopic,
      );
    });

    it('should bubble the error if sending email fails', async () => {
      const expectedException = new Error('some error');
      emailMessageServiceMock.send.mockRejectedValueOnce(expectedException);

      await expect(
        serviceUnderTest.enqueueChildChangedPasswordEmail(orgId, orgName, orgEnvId, emailParamsInput, translationMock),
      ).rejects.toThrow(expectedException);
    });

    it('should call branding service to get custom copy and send to email builder and email service', async () => {
      const customCopy = {
        environmentId: orgEnvId,
        language: 'en',
        terms: {
          userChildChangedPasswordSubject: 'Howdy!',
        },
        createdAt: '2025-02-27T10:05:41.179Z',
        updatedAt: '2025-02-27T10:05:41.179Z',
      };

      brandingServiceMock.getCustomCopy.mockResolvedValue(customCopy);

      await serviceUnderTest.enqueueChildChangedPasswordEmail(
        orgId,
        orgName,
        orgEnvId,
        emailParamsInput,
        translationMock,
      );

      expect(freeKwsEmailServiceMock.buildUserChildChangedPasswordEmail).toHaveBeenCalledWith(
        'en-CA',
        expect.objectContaining(expectedEmailBuilderAttributes),
        { userChildChangedPasswordSubject: 'Howdy!' },
      );
      expect(emailMessageServiceMock.send).toHaveBeenCalledWith(
        expectedEmailAttributes,
        configServiceMock.getEmailService().kafkaTopic,
      );
    });
  });
});
