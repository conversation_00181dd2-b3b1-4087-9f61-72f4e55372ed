# Sequential Load Test

A simple, focused K6 load test that tests all endpoints sequentially with configurable QPS and ramp up/down patterns.

## Files

- **`requests.ts`** - Contains all endpoint request logic (reusable)
- **`sequential-load-test.ts`** - Main test that runs endpoints in sequence

## How It Works

1. **Tests all 14 endpoints sequentially** (one at a time)
2. **Uses K6's built-in `stages` option** for clean ramp up/down patterns:
   - 20% of time: Ramp up from 0 to target VUs
   - 60% of time: Steady state at target VUs
   - 20% of time: Ramp down from target VUs to 0
3. **60-second cooldown** between each endpoint (0 VUs)
4. **Configurable duration** per endpoint (default 60s)

## Configuration

| Environment Variable | Description | Default |
|---------------------|-------------|---------|
| `TARGET_QPS` | Target queries per second | `100` |
| `TEST_DURATION` | Seconds to test each endpoint | `60` |
| `COOLDOWN_DURATION` | Seconds between endpoints | `60` |
| `CLIENT_ID` | OAuth client ID | Required |
| `CLIENT_SECRET` | OAuth client secret | Required |
| `BASE_URL` | API base URL | `http://localhost:7001` |

## Usage

### Setup
```bash
# Copy environment template
cp .env.example .env

# Edit .env with your credentials
CLIENT_ID=your-actual-client-id
CLIENT_SECRET=your-actual-client-secret
BASE_URL=http://localhost:7001
```

### Run Tests

```bash
# Standard test (100 QPS)
npm run test:sequential

# Stress test (300 QPS)
npm run test:sequential-stress

# Custom configuration
npm run build:sequential && dotenv -- k6 run dist/sequential-load-test.js \
  --env TARGET_QPS=200 \
  --env TEST_DURATION=90 \
  --env COOLDOWN_DURATION=30
```

## Endpoint Sequence

The test runs these endpoints in order:

1. **healthcheck** (no auth)
2. **getJwks** (no auth)
3. **childAge** (no auth)
4. **checkUsername** (no auth)
5. **createUser** (auth required)
6. **createUserV2** (auth required)
7. **getUser** (auth required)
8. **activateUser** (auth required)
9. **requestPermissions** (auth required)
10. **getPermissions** (auth required)
11. **getPermissionsExtended** (auth required)
12. **reviewPermissions** (auth required)
13. **updateParentEmail** (auth required)
14. **deleteUser** (auth required)

## Load Pattern

For each endpoint, K6's stages create this VU pattern over the test duration:

```
VUs
 ↑
 │     ┌─────────────┐
 │    ╱               ╲
 │   ╱                 ╲
 │  ╱                   ╲
 │ ╱                     ╲
 └╱───────────────────────╲─→ Time
  20%      60%       20%    Cooldown
Ramp Up  Steady    Ramp Down  (0 VUs)
```

K6 automatically handles the ramp up/down of virtual users, making the load pattern much cleaner than manual QPS calculations.

## Example Output

```
Sequential load test starting:
- Target QPS: 100
- Target VUs: 10
- Test duration per endpoint: 60s
- Cooldown between endpoints: 60s
- Total endpoints: 14
- Estimated total time: 28.0 minutes

Starting endpoint: healthcheck
Finished endpoint: healthcheck
Starting endpoint: getJwks
...
```

## Estimated Runtime

- **14 endpoints** × (60s test + 60s cooldown) = **28 minutes total**
- Configurable via `TEST_DURATION` and `COOLDOWN_DURATION`

## Key Features

✅ **Simple and focused** - Does one thing well
✅ **Reusable request logic** - All endpoints in separate file
✅ **Proper authentication** - Headers included where needed
✅ **K6 native stages** - Uses built-in ramp up/down instead of manual logic
✅ **Configurable** - Easy to adjust QPS, duration, cooldown
✅ **Sequential testing** - Clear isolation between endpoints
✅ **Built-in cooldowns** - Prevents interference between tests
