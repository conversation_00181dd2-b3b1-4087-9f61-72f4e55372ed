-- segmenting ranges for DMS
WITH table_stats AS (
  SELECT
    c.relname AS table_name,
    (SELECT attname FROM pg_attribute WHERE attrelid = c.oid AND attnum > 0 AND attname = 'id') AS id_column,
    c.reltuples::bigint AS estimated_row_count
  FROM pg_class c
  JOIN pg_namespace n ON n.oid = c.relnamespace
  WHERE c.relkind = 'r' AND n.nspname = 'public'
    AND c.relname IN ('activations', 'apps', 'appsTranslations', 'jwks', 'refreshTokens','users','webhooks')
),
range_calc AS (
  SELECT
    table_name,
    id_column,
    estimated_row_count,
    CASE
      WHEN estimated_row_count < 1000000 THEN 1
      WHEN estimated_row_count < 10000000 THEN 4
      ELSE 8
    END AS num_segments
  FROM table_stats
  WHERE id_column IS NOT NULL
)
SELECT
  r.table_name,
  r.id_column,
  r.estimated_row_count,
  r.num_segments,
  segment,
  CASE
    WHEN segment = 1 THEN 'MIN(' || r.id_column || ')'
    ELSE (r.estimated_row_count * (segment - 1) / r.num_segments)::bigint::text
  END AS range_start,
  CASE
    WHEN segment = r.num_segments THEN 'MAX(' || r.id_column || ')'
    ELSE (r.estimated_row_count * segment / r.num_segments)::bigint::text
  END AS range_end
FROM range_calc r
CROSS JOIN generate_series(1, 8) AS segment
WHERE segment <= r.num_segments
ORDER BY r.table_name, segment;
