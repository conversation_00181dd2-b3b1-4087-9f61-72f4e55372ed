import { DataTable, setDefaultTimeout } from '@cucumber/cucumber';
import { HttpRequestConfig } from '@superawesome/freekws-common-http-client';
import { SALogger } from '@superawesome/freekws-common-logger';
import { FamiliesGuardianRequestExpiredPayloadDto } from '@superawesome/freekws-queue-messages/webhook';
import { EWebhookName } from '@superawesome/freekws-queue-messages/webhook/webhook.dto';
import assert from 'assert';
import { binding, given, then, when } from 'cucumber-tsflow';

import { OrgLevelWebhookPayload } from '../../src/webhook/types';
import { Utils } from '../../test/utils';
import { orgEnvHost } from '../fixtures/org-env.fixture';
import { UATUtils } from '../utils';

setDefaultTimeout(60 * 1000);
@binding([SALogger])
export class UserRemovedFromFamilySteps {
  userId: string;
  responseStatus: number;
  orgId: string;
  secret: string;

  @given('org exists with secret')
  org_exists_with_secret(table: DataTable) {
    const data = table.rowsHash() as {
      orgId: string;
      secret: string;
    };

    this.orgId = data.orgId;
    this.secret = data.secret;
  }

  @when('unresponsive-parent-deleted for {string} is triggered')
  async unresponsive_parent_deleted_triggered(userId: string): Promise<void> {
    const timestamp = Date.now();
    const body: OrgLevelWebhookPayload<FamiliesGuardianRequestExpiredPayloadDto> = {
      name: EWebhookName.FAMILIES_GUARDIAN_REQUEST_EXPIRED,
      time: timestamp,
      orgId: this.orgId,
      payload: {
        userId: userId,
        guardianRequestId: '2f5867b5-76fd-4ee1-ac48-10311a665c54',
      },
    };
    const signature = Utils.generateKwsSignature(timestamp, body, this.secret);

    const request = {
      url: `/v1/webhooks/guardian-request-expired`,
      method: 'POST',
      data: body,
      headers: {
        'x-kws-signature': `t=${timestamp},v1=${signature}`,
        'x-forwarded-host': orgEnvHost,
      },
    } as HttpRequestConfig;

    const response = await UATUtils.buildClassicWrapperClient().request(request);
    this.responseStatus = response.status;
  }

  @then('unresponsive-parent-deleted returns http status code of no-content')
  user_removed_from_family_returns_no_content() {
    assert.equal(this.responseStatus, 204);
  }
}
