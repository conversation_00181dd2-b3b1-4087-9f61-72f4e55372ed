import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddActivations1731486874551 implements MigrationInterface {
  name = 'AddActivations1731486874551';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "app"
            ALTER COLUMN "oauthClientId" SET NOT NULL
        `);
    await queryRunner.query(`
            ALTER TABLE "user"
            ADD "uuid" uuid NOT NULL DEFAULT uuid_generate_v4()
        `);
    await queryRunner.query(`
            CREATE TABLE "activation" (
                "id" SERIAL NOT NULL,
                "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
                "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
                "appId" integer,
                "appOrgEnv" character varying,
                "userId" integer,
                "userOrgEnv" character varying,
                CONSTRAINT "PK_bb655734797dc39eddfa0fcca92" PRIMARY KEY ("id")
            )
        `);
    await queryRunner.query(`
            ALTER TABLE "activation"
            ADD CONSTRAINT "FK_2c267e6a328184f8b1d5346fea1" FOREIGN KEY ("appId", "appOrgEnv") REFERENCES "app"("id", "orgEnvId") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
    await queryRunner.query(`
            ALTER TABLE "activation"
            ADD CONSTRAINT "FK_d7b6bac581603cff0accfd16c56" FOREIGN KEY ("userId", "userOrgEnv") REFERENCES "user"("id", "orgEnvId") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "app"
            ALTER COLUMN "oauthClientId" DROP NOT NULL
        `);
    await queryRunner.query(`
            ALTER TABLE "user"
            DROP COLUMN "uuid"
        `);
    await queryRunner.query(`
            ALTER TABLE "activation" DROP CONSTRAINT "FK_d7b6bac581603cff0accfd16c56"
        `);
    await queryRunner.query(`
            ALTER TABLE "activation" DROP CONSTRAINT "FK_2c267e6a328184f8b1d5346fea1"
        `);
    await queryRunner.query(`
            DROP TABLE "activation"
        `);
  }
}
