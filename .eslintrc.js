module.exports = {
  extends: '@superawesome/eslint-config-freekws/backend',
  plugins: ['unused-imports'],
  rules: {
    'import/order': ['error', {
      groups: [['builtin', 'external']],
      'newlines-between': 'always',
      alphabetize: {
        order: 'asc',
        caseInsensitive: true,
      },
    }],
    'unused-imports/no-unused-imports': 'error',
    "no-param-reassign": ["error", {
      "props": false
    }]
  },
};