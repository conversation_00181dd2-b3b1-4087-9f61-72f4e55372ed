import { setDefaultTimeout } from '@cucumber/cucumber';
import { ChildAgeResponseDTO } from '@superawesome/freekws-classic-wrapper-common';
import { SALogger } from '@superawesome/freekws-common-logger';
import assert from 'assert';
import { binding, given, then, when } from 'cucumber-tsflow';

import { UATUtils } from '../utils';

setDefaultTimeout(60 * 1000);
@binding([SALogger])
export class CountriesSteps {
  dob: string;
  country: string;
  domain: string;
  getChildAgeResponse: ChildAgeResponseDTO;

  @given('From domain {string}')
  setDomain(domain: string) {
    this.domain = domain;
  }

  @when('user with dob {string} from location {string} makes a child-age request')
  async getChildAge(dob: string, country: string): Promise<void> {
    const response = await UATUtils.buildClassicWrapperClient().request({
      url: '/v1/countries/child-age',
      method: 'GET',
      params: { dob, country },
      headers: { 'x-forwarded-host': this.domain },
    });

    this.dob = dob;
    this.country = country;

    assert.equal(response.status, 200);
    this.getChildAgeResponse = response.data;
  }

  @then(/child age values is returned with expected age of consent '(-?\w*)' and user age '(-?\w*)'$/)
  async verifyChildAge(expectedAgeOfConsent: number, expectedAge: number): Promise<void> {
    assert.equal(this.getChildAgeResponse.consentAgeForCountry, expectedAgeOfConsent);
    assert.equal(this.getChildAgeResponse.age, expectedAge);
  }
}
