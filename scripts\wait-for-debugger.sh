#!/bin/bash

DEFAULT_HOST="localhost"
DEFAULT_PORT=9229
WAIT_TIME=1   # Wait time between attempts (seconds)
DEBUG_MSG="{\"id\":1,\"method\":\"Runtime.evaluate\",\"params\":{\"expression\":\"1+1\"}}"  # Test request to debugger
RETRIES=0

if [ $# -eq 1 ]; then
    # Split input argument (format: host:port)
    IFS=":" read -r HOST PORT <<< "$1"
elif [ $# -eq 2 ]; then
    HOST=$1
    PORT=$2
else
    HOST=$DEFAULT_HOST
    PORT=$DEFAULT_PORT
fi

if ! [[ $PORT =~ ^[0-9]+$ ]]; then
    echo "❌ Error: Port must be a number. Got '$PORT'."
    exit 1
fi

echo "🔍 Waiting for the debugger to be ready on $HOST:$PORT..."

while true; do
        RESPONSE=$(curl -s -X POST -H "Content-Type: application/json" \
            --data "$DEBUG_MSG" "http://$HOST:$PORT/json" 2>/dev/null)

            echo "$RESPONSE"

        if [[ -n $RESPONSE ]]; then
            echo "✅  Debugger is ready and responding on $HOST:$PORT!"
            exit 0
        else
            echo "⏳  Attempt $RETRIES: Debugger not ready. Retrying in $WAIT_TIME seconds..."
            sleep $WAIT_TIME
            RETRIES=$((RETRIES+1))
        fi
done
