import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddMobileApiKeyToApp1734629974008 implements MigrationInterface {
  name = 'AddMobileApiKeyToApp1734629974008';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "app"
                ADD COLUMN "mobileApiKey" character varying;
        `);

    await queryRunner.query(`
            UPDATE "app"
            SET "mobileApiKey" = REPLACE(uuid_generate_v4()::text || uuid_generate_v4()::text, '-',
                                         '')
            WHERE
                "mobileApiKey" IS NULL;
        `);

    await queryRunner.query(`
            ALTER TABLE "app"
                ALTER COLUMN "mobileApiKey" SET NOT NULL;
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "app"
                DROP
                    COLUMN "mobileApiKey";
        `);
  }
}
