import { TestingModule } from '@nestjs/testing';
import { createClientMock } from '@superawesome/freekws-clients-base/src/test-utils/mock';
import { DEVPORTAL_BACKEND_API_CLIENT_INJECT_KEY, devportalApiPlugin } from '@superawesome/freekws-devportal-common';
import { ServiceWebhookListDTO } from '@superawesome/freekws-devportal-common/types/service-webhook/service-webhook.dto';
import { ServiceID } from '@superawesome/freekws-service-activation-service-common';

import { DevPortalService } from './dev-portal.service';
import { Testing } from '../../utils';

describe('DevPortalService', () => {
  const mockDevPortalApi = createClientMock(devportalApiPlugin, jest.fn);
  let service: DevPortalService;
  const response: ServiceWebhookListDTO = {
    webhooks: [
      {
        id: '',
        name: '<PERSON><PERSON><PERSON>',
        url: '',
        secretKey: 'SECRET',
      },
    ],
  };

  beforeEach(async () => {
    jest.resetAllMocks();

    const module: TestingModule = await Testing.createModule({
      providers: [DevPortalService, { provide: DEVPORTAL_BACKEND_API_CLIENT_INJECT_KEY, useValue: mockDevPortalApi }],
    });

    service = module.get<DevPortalService>(DevPortalService) as DevPortalService;

    mockDevPortalApi.getModule('webhook').listOrgWebhooks.mockResolvedValueOnce({
      data: {
        response: response,
        meta: { requestId: 'foo', timestamp: new Date().toISOString() },
      },
      status: 200,
    });
  });

  it('should get the expected secret', async () => {
    await expect(service.getWebhookSecret('Org1', 'Env1', ServiceID.SETTINGS, 'GraduateUser')).resolves.toEqual(
      'SECRET',
    );
  });

  it('should return undefined when the secret is not found', async () => {
    await expect(service.getWebhookSecret('Org1', 'Env1', ServiceID.SETTINGS, 'OtherUser')).resolves.toBeUndefined();
  });
});
