import { TestingModule } from '@nestjs/testing';
import {
  FamiliesGuardianRequestExpiredPayloadDto,
  FamiliesGroupDeletedPayloadDto,
  FamiliesUserAddedToFamilyDTO,
  SettingsEffectiveValuesChangedPayloadDTO,
} from '@superawesome/freekws-queue-messages/webhook';
import { FamiliesUserRemovedFromFamilyDTO } from '@superawesome/freekws-queue-messages/webhook/payloads/families-user-removed-from-family.payload.dto';
import { SettingsUserGraduatedPayloadDTO } from '@superawesome/freekws-queue-messages/webhook/payloads/settings-user-graduated.payload.dto';
import { EWebhookName } from '@superawesome/freekws-queue-messages/webhook/webhook.dto';

import { AppLevelWebhookPayload, OrgLevelWebhookPayload } from './types';
import { WebhookController } from './webhook.controller';
import { WebhookService } from './webhook.service';
import { Testing } from '../common/utils';

const mockService = {
  sendAccountGraduated: jest.fn(),
  sendSettingsEffectiveValuesChanged: jest.fn(),
  sendUserRemovedFromFamily: jest.fn(),
  sendUserAddedToFamily: jest.fn(),
  sendGuardianRequestExpired: jest.fn(),
  sendFamilyGroupDeleted: jest.fn(),
};

describe('WebhookController', () => {
  let controller: WebhookController;

  beforeEach(async () => {
    const module: TestingModule = await Testing.createModule({
      controllers: [WebhookController],
      providers: [{ provide: WebhookService, useValue: mockService }],
    });

    controller = module.get<WebhookController>(WebhookController) as WebhookController;
  });

  it('should return no content from child-account-graduated', async () => {
    const body: OrgLevelWebhookPayload<SettingsUserGraduatedPayloadDTO> = {
      name: EWebhookName.SETTINGS_USER_GRADUATED,
      time: Date.now(),
      orgId: '111',
      payload: {
        userId: '111',
      },
    };

    await expect(controller.sendAccountGraduated(body)).resolves.toBeUndefined();

    expect(mockService.sendAccountGraduated).toHaveBeenCalled();
  });

  it('should return no content from settings:effective-values-changed', async () => {
    const body: AppLevelWebhookPayload<SettingsEffectiveValuesChangedPayloadDTO<boolean>> = {
      name: EWebhookName.SETTINGS_EFFECTIVE_VALUES_CHANGED,
      time: Date.now(),
      orgId: '111',
      productId: '123',
      payload: {
        userId: '4334',
        settings: [
          {
            namespace: 'namespace',
            settingName: 'dataShare',
            effectiveValue: true,
            effectiveSource: 'source',
            trigger: 'trigger',
          },
        ],
      },
    };

    await expect(controller.sendSettingsEffectiveValuesChanged('hook-shot', body)).resolves.toBeUndefined();

    expect(mockService.sendSettingsEffectiveValuesChanged).toHaveBeenCalled();
  });

  it('should return no content from user-removed-from-family', async () => {
    const body: OrgLevelWebhookPayload<FamiliesUserRemovedFromFamilyDTO> = {
      name: EWebhookName.FAMILIES_USER_REMOVED_FROM_FAMILY,
      time: Date.now(),
      orgId: '111',
      payload: {
        userId: '4334',
      },
    };

    await expect(controller.sendUserRemovedFromFamily(body)).resolves.toBeUndefined();

    expect(mockService.sendUserRemovedFromFamily).toHaveBeenCalled();
  });

  it('should return no content from user-added-to-family', async () => {
    const body: OrgLevelWebhookPayload<FamiliesUserAddedToFamilyDTO> = {
      name: EWebhookName.FAMILIES_USER_ADDED_TO_FAMILY,
      time: Date.now(),
      orgId: '111',
      payload: {
        userId: '4334',
      },
    };

    await expect(controller.sendUserAddedToFamily(body)).resolves.toBeUndefined();

    expect(mockService.sendUserAddedToFamily).toHaveBeenCalled();
  });

  it('should return no content from guardian-request-expired', async () => {
    const body: OrgLevelWebhookPayload<FamiliesGuardianRequestExpiredPayloadDto> = {
      name: EWebhookName.FAMILIES_GUARDIAN_REQUEST_EXPIRED,
      time: Date.now(),
      orgId: '111',
      payload: {
        userId: '4334',
        guardianRequestId: '2f5867b5-76fd-4ee1-ac48-10311a665c54',
      },
    };

    await expect(controller.sendGuardianRequestExpired(body)).resolves.toBeUndefined();

    expect(mockService.sendGuardianRequestExpired).toHaveBeenCalled();
  });

  it('should return no content from families-group-deleted', async () => {
    const body: OrgLevelWebhookPayload<FamiliesGroupDeletedPayloadDto> = {
      name: EWebhookName.FAMILIES_FAMILY_GROUP_DELETED,
      time: Date.now(),
      orgId: '111',
      payload: {
        orgId: '111',
        orgEnvId: '123',
        familyGroupId: '8202e36e-0559-462a-b1fa-973be02b04db',
        members: [
          {
            id: '1',
            userId: '4334',
            role: 'user',
          },
        ],
      },
    };

    await expect(controller.sendFamilyGroupDeleted(body)).resolves.toBeUndefined();

    expect(mockService.sendFamilyGroupDeleted).toHaveBeenCalled();
  });
});
