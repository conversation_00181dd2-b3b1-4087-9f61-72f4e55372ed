import { ForbiddenException } from '@nestjs/common';
import { EOAuthScope } from '@superawesome/freekws-classic-wrapper-common';

import { HasScope } from './has-scope.policy';
import { OAuthFastifyRequest } from '../../oauth/types';

describe('HasScope', () => {
  it('should be defined', () => {
    expect(new HasScope()).toBeDefined();
  });

  it('should return false if JWT not presented in request', async () => {
    const policy = new HasScope();

    await expect(
      policy.handle({
        raw: {},
      } as OAuthFastifyRequest),
    ).resolves.toEqual(false);
  });

  it('should return tyoe for matched scope', async () => {
    const policy = new HasScope(EOAuthScope.APP);

    await expect(
      policy.handle({
        raw: {
          jwt: {
            scope: 'app',
          },
        },
      } as OAuthFastifyRequest),
    ).resolves.toEqual(true);
  });

  it('should throw an error if required scope missing', async () => {
    const policy = new HasScope(EOAuthScope.MOBILE_APP);

    await expect(
      policy.handle({
        raw: {
          jwt: {
            scope: 'app',
          },
        },
      } as OAuthFastifyRequest),
    ).rejects.toBeInstanceOf(ForbiddenException);
  });
});
