# OAuth 2.0 Flow Load Testing

This document explains the OAuth 2.0 flows implemented in the K6 load tests for the `/oauth/token` endpoint.

## 🔐 **OAuth 2.0 Grant Types Supported**

The classic wrapper backend supports **4 OAuth 2.0 grant types**, each serving different authentication scenarios:

### **1. Client Credentials Grant (`client_credentials`)**
**Purpose**: App-level authentication for server-to-server communication

**Flow**:
```
Client → POST /oauth/token
  ├─ client_id: app_client_id
  ├─ client_secret: app_client_secret
  ├─ grant_type: "client_credentials"
  └─ scope: "app"

Response:
  ├─ access_token: JWT for app-level access
  ├─ refresh_token: Token for refreshing access
  ├─ token_type: "bearer"
  └─ expires_in: 86400 (24 hours)
```

**Load Test**: `oauthTokenClientCredentials` & `oauthTokenClientCredentialsDomCraft`

### **2. Password Grant (`password`)**
**Purpose**: User authentication with username/password (legacy flow)

**Flow**:
```
Client → POST /oauth/token
  ├─ client_id: app_client_id
  ├─ client_secret: app_client_secret
  ├─ grant_type: "password"
  ├─ scope: "user"
  ├─ username: user_username
  └─ password: user_password

Response:
  ├─ access_token: JWT for user-scoped access
  ├─ refresh_token: Token for refreshing access
  ├─ token_type: "bearer"
  └─ expires_in: 86400 (24 hours)
```

**Load Test**: `oauthTokenPassword`

### **3. Refresh Token Grant (`refresh_token`)**
**Purpose**: Refresh expired access tokens without re-authentication

**Flow**:
```
Client → POST /oauth/token
  ├─ client_id: app_client_id
  ├─ client_secret: app_client_secret
  ├─ grant_type: "refresh_token"
  └─ refresh_token: existing_refresh_token

Response:
  ├─ access_token: New JWT access token
  ├─ refresh_token: New refresh token
  ├─ token_type: "bearer"
  └─ expires_in: 86400 (24 hours)
```

**Load Test**: `oauthTokenRefreshToken`

### **4. Authorization Code Grant (`authorization_code`)**
**Purpose**: OAuth 2.0 authorization code flow (most secure, requires prior authorization)

**Flow**:
```
Step 1: Get User Token (for authorization)
Client → POST /oauth/token
  ├─ client_id: app_client_id
  ├─ client_secret: app_client_secret
  ├─ grant_type: "password"
  ├─ scope: "user"
  ├─ username: user_username
  └─ password: user_password

Step 2: Get Authorization Code
Client → POST /oauth/authorise (with user token)
  ├─ response_type: "code"
  ├─ client_id: app_client_id
  ├─ redirect_uri: callback_url
  └─ state: csrf_token

Step 3: Exchange Code for Token
Client → POST /oauth/token
  ├─ client_id: app_client_id
  ├─ client_secret: app_client_secret
  ├─ grant_type: "authorization_code"
  ├─ code: authorization_code_from_step2
  └─ redirect_uri: same_callback_url

Response:
  ├─ access_token: JWT access token
  ├─ refresh_token: Refresh token
  ├─ token_type: "bearer"
  └─ expires_in: 86400 (24 hours)
```

**Load Test**: `oauthTokenAuthorizationCode` (tests all 3 steps)

## 🧪 **Load Test Implementation**

### **Test Constants (Replace with Real Values)**

```typescript
const OAUTH_TEST_CONSTANTS = {
    // For password grant type
    TEST_USERNAME: 'test-username-replace-me',
    TEST_PASSWORD: 'test-password-replace-me',

    // For authorization code flow
    TEST_REDIRECT_URI: 'https://example.com/callback',
    TEST_AUTHORIZATION_CODE: 'test-auth-code-replace-me',
    TEST_CODE_VERIFIER: 'test-code-verifier-replace-me',

    // For refresh token flow
    TEST_REFRESH_TOKEN: 'test-refresh-token-replace-me',
};
```

### **Test Sequence in Load Tests**

Currently, only the **authorization code flow** is enabled in the sequential scenarios:

1. **`oauthTokenAuthorizationCode`** (0s) - Test complete authorization code flow (3 steps)

**Other OAuth flows available but commented out:**
- `oauthTokenClientCredentials` - Test javi-test app authentication
- `oauthTokenPassword` - Test user password authentication
- `oauthTokenRefreshToken` - Test token refresh flow

### **Performance Expectations**

All OAuth flows use **authentication performance thresholds**:
- **≤ 100 QPS**: 99% of responses < 1 second
- **100-450 QPS**: 99% of responses < 1.5 seconds

### **Expected Responses**

| Flow | Success Status | Error Status | Notes |
|------|---------------|--------------|-------|
| **Client Credentials** | 200 | 401 | Should always work with valid credentials |
| **Password** | 200 | 401 | May fail if test user doesn't exist |
| **Refresh Token** | 200 | 400 | May fail if refresh token is invalid/expired |
| **Authorization Code** | 200 | 400 | May fail if authorization code is invalid/expired |
| **Invalid Client** | - | 401 | Expected to fail (error case test) |

## 🔧 **Setup Requirements**

### **1. Valid Client Credentials**
Ensure your `.env` file has valid OAuth client credentials:

```bash
CLIENT_ID=your-javi-test-client-id
CLIENT_SECRET=your-javi-test-client-secret
DOM_CRAFT_CLIENT_ID=your-dom-craft-client-id
DOM_CRAFT_CLIENT_SECRET=your-dom-craft-client-secret
```

### **2. Test User for Password Flow**
Update `OAUTH_TEST_CONSTANTS.TEST_USERNAME` and `OAUTH_TEST_CONSTANTS.TEST_PASSWORD` with a valid test user.

### **3. Valid Refresh Token**
To test refresh token flow:
1. First obtain a refresh token from any successful OAuth flow
2. Update `OAUTH_TEST_CONSTANTS.TEST_REFRESH_TOKEN` with the real token

### **4. Authorization Code for Code Flow**
To test authorization code flow:
1. First call `/oauth/authorize` to get an authorization code
2. Update `OAUTH_TEST_CONSTANTS.TEST_AUTHORIZATION_CODE` with the real code
3. Ensure `TEST_REDIRECT_URI` matches the one used in authorization

## 🚀 **Running OAuth Load Tests**

```bash
# Run all OAuth flows as part of sequential scenarios
npm run test:sequential-scenarios

# Stress test all OAuth flows
npm run test:sequential-scenarios-stress
```

## 🔍 **Monitoring OAuth Performance**

### **Key Metrics to Watch**
- **Token generation time** - Should be < 1s for most flows
- **Error rates** - Should be < 5% for valid credentials
- **Throughput** - Should handle target QPS without degradation
- **Token validation** - Ensure returned tokens are valid JWTs

### **Common Issues**
- **High latency** - May indicate JWT signing/database bottlenecks
- **401 errors** - Invalid client credentials or expired tokens
- **400 errors** - Malformed requests or invalid grant parameters
- **Rate limiting** - OAuth endpoint may have specific rate limits

## 📋 **OAuth Flow Dependencies**

- **Client Credentials**: No dependencies (self-contained)
- **Password**: Requires valid user in database
- **Refresh Token**: Requires valid refresh token from previous OAuth flow
- **Authorization Code**: Requires prior call to `/oauth/authorize` endpoint

The load tests are designed to handle expected failures gracefully, focusing on performance measurement rather than functional correctness for flows requiring external setup.
