import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddUniqueUsernameIndexToUser1741500800003 implements MigrationInterface {
    name = 'AddUniqueUsernameIndexToUser1741500800003';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            CREATE UNIQUE INDEX IF NOT EXISTS "user_username_idx"
                ON "user" (LOWER("username"))
                WHERE ("deletedAt" IS NULL) AND ("username" IS NOT NULL)
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            DROP INDEX IF EXISTS "user_username_idx"
        `);
    }
}