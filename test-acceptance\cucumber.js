// cucumber.js
module.exports = {
  default: {
    paths: process.env['FEATURES'] ? process.env['FEATURES'].split(/\s/) : ['test-acceptance/features/**/*.feature'],
    requireModule: ['ts-node/register'],
    require: ['test-acceptance/step-definitions/**/*.ts'],
    // Use tags if you want to run a specific set of tests
    // tags: '@Selected',
    publishQuiet: true,
    format: [
      '@cucumber/pretty-formatter',
      'html:reports/cucumber-report.html',
      './test-acceptance/formatters/junit-formatter.ts:reports/junit/junit.xml',
    ],
  },
};
