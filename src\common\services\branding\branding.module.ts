import { Module } from '@nestjs/common';
import {
  BRANDING_SERVICE_API_CLIENT_INJECT_KEY,
  brandingServicePlugin,
} from '@superawesome/freekws-branding-service-common';
import { BRANDING_SERVICE_API_CLIENT_UPSTREAM } from '@superawesome/freekws-branding-service-common/client-plugin';
import { NestJsClient, TNestJsHttpClientConfig } from '@superawesome/freekws-clients-nestjs';
import { MetricsService, MetricsServiceModule } from '@superawesome/freekws-metrics-nestjs-service';
import { AxiosError } from 'axios';
import CircuitBreaker from 'opossum';

import { BrandingService } from './branding.service';
import { ConfigModule } from '../config/config.module';
import { ConfigService } from '../config/config.service';

@Module({
  imports: [ConfigModule, MetricsServiceModule],
  providers: [
    MetricsService,
    {
      provide: BRANDING_SERVICE_API_CLIENT_INJECT_KEY,
      useFactory: (config: ConfigService, metricsService: MetricsService) => {
        const { baseURL, ...clientOptions } = config.getBrandingApiClient();
        const circuitBreakerConfig = config.getDevPortalServiceCircuitBreakerConfig();
        const clientConfig: TNestJsHttpClientConfig = {
          timeout: clientOptions.timeoutMs,
          upstream: BRANDING_SERVICE_API_CLIENT_UPSTREAM,
          retry: {
            retries: clientOptions.retries,
            initialRetryDelay: clientOptions.initalRetryDelay,
            bailOnStatus: clientOptions.bailOnStatus,
          },
          circuitBreaker: {
            timeout: circuitBreakerConfig.timeoutMs,
            errorThresholdPercentage: circuitBreakerConfig.errorThresholdPercentage,
            resetTimeout: circuitBreakerConfig.resetTimeoutMs,
            errorFilter: (error: AxiosError) => {
              return error.response?.status && error.response.status >= 400 && error.response.status < 500;
            },
          } as CircuitBreaker.Options,
        };
        const client = new NestJsClient(brandingServicePlugin, baseURL, metricsService.metrics, clientConfig);
        return client;
      },
      inject: [ConfigService, MetricsService],
    },
    BrandingService,
  ],
  exports: [BrandingService, BRANDING_SERVICE_API_CLIENT_INJECT_KEY],
})
export class BrandingModule {}
