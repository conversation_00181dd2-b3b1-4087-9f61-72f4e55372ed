import { Test, TestingModule } from '@nestjs/testing';
import { getDataSourceToken } from '@nestjs/typeorm';
import { SALogger } from '@superawesome/freekws-common-logger';

import { HealthcheckService } from './healthcheck.service';
import { ConfigService } from '../common/services/config/config.service';

const dataSourceMock = {
  manager: {
    connection: {
      query: jest.fn(),
    },
  },
};

const mockLogger = {
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};

const mockConfigService = {
  getHealthcheckConfig: jest.fn().mockReturnValue({ intervalMs: 60 }),
};

describe('HealthcheckService', () => {
  let service: HealthcheckService;
  let module: TestingModule;

  beforeEach(async () => {
    module = await Test.createTestingModule({
      providers: [
        HealthcheckService,
        { provide: getDataSourceToken(), useValue: dataSourceMock },
        { provide: SALogger, useValue: mockLogger },
        { provide: ConfigService, useValue: mockConfigService },
      ],
    }).compile();
    await module.init();

    service = module.get<HealthcheckService>(HealthcheckService);
  });

  afterEach(async () => {
    await module.close();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should return true if all services is healthy', () => {
    expect(service.isHealthy()).toBeTruthy();
  });

  it('should return false if postgress is unhealthy', async () => {
    dataSourceMock.manager.connection.query.mockRejectedValueOnce(new Error('ECONNREFUSED'));
    // Trigger healthcheck before interval tick
    await service.isPostgresHealthy();

    expect(service.isHealthy()).toBeFalsy();
    expect(mockLogger.warn).toHaveBeenCalledWith('Healthcheck failed due to unhealthy resources: postgres');
  });
});
