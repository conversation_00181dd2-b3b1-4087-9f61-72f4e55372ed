import { Inject, Injectable } from '@nestjs/common';
import { KeycloakService } from '@superawesome/freekws-auth-library';
import {
  CALLBACK_SERVICE_API_CLIENT_INJECT_KEY,
  callbackServicePlugin,
} from '@superawesome/freekws-callback-service-common';
import { NestJsClient } from '@superawesome/freekws-clients-nestjs';
import { ServiceID } from '@superawesome/freekws-service-activation-service-common';

import { KEYCLOAK_PROVIDER } from '../keycloak/keycloak.module';

@Injectable()
export class CallbackService {
  constructor(
    @Inject(CALLBACK_SERVICE_API_CLIENT_INJECT_KEY)
    private readonly callbackServiceClient: NestJsClient<typeof callbackServicePlugin>,
    @Inject(KEYCLOAK_PROVIDER)
    private readonly keycloakService: KeycloakService,
  ) {}

  async getWebhookAtOrgLevel(webhookName: string, orgEnvId: string) {
    const token = await this.keycloakService.getUpToDateServiceAccessToken();
    const headers = {
      Authorization: `Bearer ${token}`,
    };

    const webhooksResponse = await this.callbackServiceClient.getModule('webhooks').getWebhook(
      {
        params: {
          webhookName,
          environmentId: orgEnvId,
          serviceId: this.getServiceIdForWebhook(webhookName),
        },
      },
      { headers },
    );

    return webhooksResponse.data.response;
  }

  private getServiceIdForWebhook(webhooksName: string) {
    if (webhooksName.startsWith('families')) {
      return ServiceID.FAMILY_GROUP;
    }

    return ServiceID.SETTINGS;
  }
}
