import { addAuth<PERSON>eader, validateResponse, generateKwsSignature } from './utils';
import http from 'k6/http';
import {
    FamiliesGroupDeletedPayloadDto,
    FamiliesGuardianRequestExpiredPayloadDto,
    FamiliesUserAddedToFamilyDTO,
    FamiliesUserRemovedFromFamilyDTO,
    SettingsEffectiveValuesChangedPayloadDTO,
    SettingsUserGraduatedPayloadDTO,
    WebhookPayload
} from './endpoint-types';

export const BASE_URL = __ENV.BASE_URL || 'http://localhost:3000';
export const DEFAULT_HEADERS = {
    'x-forwarded-host': 'test.com',
    'Content-Type': 'application/json'
};

// OAuth endpoints
export function oauthToken(clientId: string, clientSecret: string, params = {}) {
    const defaultParams = {
        grant_type: 'client_credentials',
        scope: 'app'
    };

    const payload = {
        client_id: clientId,
        client_secret: clientSecret,
        ...defaultParams,
        ...params
    };

    const headers = {
        ...DEFAULT_HEADERS,
        'Content-Type': 'application/x-www-form-urlencoded'
    };

    const response = http.post(`${BASE_URL}/oauth/token`, payload, { headers });

    validateResponse(response);

    return response;
}

export function getOAuthToken(clientId: string, clientSecret: string, grantType = 'client_credentials', scope = 'app') {
    const payload = {
        client_id: clientId,
        client_secret: clientSecret,
        grant_type: grantType,
        scope: scope
    };

    const headers = {
        ...DEFAULT_HEADERS,
        'Content-Type': 'application/x-www-form-urlencoded'
    };

    const response = http.post(`${BASE_URL}/oauth/token`, payload, { headers });

    validateResponse(response);

    if (response.status === 200) {
        return response.json('access_token');
    }

    return null;
}

export function oauthAuthorize(token: string, body = {}, queryParams = {}) {
    const headers = addAuthHeader(token);

    const url = `${BASE_URL}/oauth/authorise${queryParams ? `?${new URLSearchParams(queryParams).toString()}` : ''}`;

    const response = http.post(url, JSON.stringify(body), { headers });

    validateResponse(response);

    return response;
}

// App endpoints
export function createUser(token: string, appId: string, body = {}) {
    const headers = addAuthHeader(token);

    const response = http.post(
        `${BASE_URL}/v2/apps/${appId}/users`,
        JSON.stringify(body),
        { headers }
    );

    validateResponse(response, { expectedStatus: 201, maxDuration: 500 });

    return response;
}

export function getUser(token: string, appId: string, userId: string) {
    const headers = addAuthHeader(token);

    const response = http.get(
        `${BASE_URL}/v2/apps/${appId}/users/${userId}`,
        { headers }
    );

    validateResponse(response);

    return response;
}

export function requestUserPermissions(token: string, appId: string, userId: string, body = {}) {
    const headers = addAuthHeader(token);

    const response = http.post(
        `${BASE_URL}/v2/apps/${appId}/users/${userId}/request-permissions`,
        JSON.stringify(body),
        { headers }
    );

    validateResponse(response);

    return response;
}

export function updateParentEmail(token: string, appId: string, userId: string, body = {}) {
    const headers = addAuthHeader(token);

    const response = http.post(
        `${BASE_URL}/v2/apps/${appId}/users/${userId}/update-parent-email`,
        JSON.stringify(body),
        { headers }
    );

    validateResponse(response, { expectedStatus: 204, maxDuration: 500 });

    return response;
}

export function getUserPermissions(token: string, appId: string, userId: string, extended = false) {
    const headers = addAuthHeader(token);

    const url = `${BASE_URL}/v2/apps/${appId}/users/${userId}/permissions${extended ? '?extended=true' : ''}`;

    const response = http.get(url, { headers });

    validateResponse(response);

    return response;
}

export function deleteUserActivation(token: string, appId: string, userId: string) {
    const headers = addAuthHeader(token);

    const response = http.del(
        `${BASE_URL}/v2/apps/${appId}/users/${userId}`,
        null,
        { headers }
    );

    validateResponse(response, { expectedStatus: 204, maxDuration: 500 });

    return response;
}

export function reviewPermissions(token: string, appId: string, userId: string) {
    const headers = addAuthHeader(token);

    const response = http.post(
        `${BASE_URL}/v2/apps/${appId}/users/${userId}/review-permissions`,
        null,
        { headers }
    );

    validateResponse(response, { expectedStatus: 201, maxDuration: 500 });

    return response;
}

// User endpoints
export function activateUser(token: string, userId: string, body = {}) {
    const headers = addAuthHeader(token);

    const response = http.post(
        `${BASE_URL}/v1/users/${userId}/apps`,
        JSON.stringify(body),
        { headers }
    );

    validateResponse(response);

    return response;
}

// Webhook endpoints
export function webhookChildAccountGraduated(body: WebhookPayload<SettingsUserGraduatedPayloadDTO>) {
    const signature = generateKwsSignature(body, "secretsecret");

    const headers = {
        ...DEFAULT_HEADERS,
        'x-kws-signature': signature,
    };

    const response = http.post(
        `${BASE_URL}/v1/webhooks/child-account-graduated`,
        JSON.stringify(body),
        { headers }
    );

    validateResponse(response, { expectedStatus: 204, maxDuration: 500 });

    return response;
}

export function webhookSettingsEffectiveValuesChanged(body: WebhookPayload<SettingsEffectiveValuesChangedPayloadDTO>) {
    const signature = generateKwsSignature(body, "secretsecret6");

    const headers = {
        ...DEFAULT_HEADERS,
        'x-kws-signature': signature,
    };

    const response = http.post(
        `${BASE_URL}/v1/webhooks/settings-effective-values-changed`,
        JSON.stringify(body),
        { headers }
    );

    validateResponse(response, { expectedStatus: 204, maxDuration: 500 });

    return response;
}

export function webhookUserRemovedFromFamily(body: WebhookPayload<FamiliesUserRemovedFromFamilyDTO>) {
    const signature = generateKwsSignature(body, "secretsecret4");

    const headers = {
        ...DEFAULT_HEADERS,
        'x-kws-signature': signature,
    };

    const response = http.post(
        `${BASE_URL}/v1/webhooks/user-removed-from-family`,
        JSON.stringify(body),
        { headers }
    );

    validateResponse(response, { expectedStatus: 204, maxDuration: 500 });

    return response;
}

export function webhookUserAddedToFamily(body: WebhookPayload<FamiliesUserAddedToFamilyDTO>) {
    const signature = generateKwsSignature(body, "secretsecret3");

    const headers = {
        ...DEFAULT_HEADERS,
        'x-kws-signature': signature,
    };

    const response = http.post(
        `${BASE_URL}/v1/webhooks/user-added-to-family`,
        JSON.stringify(body),
        { headers }
    );

    validateResponse(response, { expectedStatus: 204, maxDuration: 500 });

    return response;
}

export function webhookGuardianRequestExpired(body: WebhookPayload<FamiliesGuardianRequestExpiredPayloadDto>) {
    const signature = generateKwsSignature(body, "secretsecret7");

    const headers = {
        ...DEFAULT_HEADERS,
        'x-kws-signature': signature,
    };

    const response = http.post(
        `${BASE_URL}/v1/webhooks/guardian-request-expired`,
        JSON.stringify(body),
        { headers }
    );

    validateResponse(response, { expectedStatus: 204, maxDuration: 500 });

    return response;
}

export function webhookFamiliesGroupDeleted(body: WebhookPayload<FamiliesGroupDeletedPayloadDto>) {
    const signature = generateKwsSignature(body, "secretsecret");

    const headers = {
        ...DEFAULT_HEADERS,
        'x-kws-signature': signature,
    };

    const response = http.post(
        `${BASE_URL}/v1/webhooks/families-group-deleted`,
        JSON.stringify(body),
        { headers }
    );

    validateResponse(response, { expectedStatus: 204, maxDuration: 500 });

    return response;
}

// Countries endpoints
export function getChildAge(query = {}, customHeaders = {}) {
    const headers = {
        ...DEFAULT_HEADERS,
        ...customHeaders
    };

    let queryString = '';
    if (Object.keys(query).length > 0) {
        queryString = '?' + Object.entries(query)
            .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(String(value))}`)
            .join('&');
    }

    const url = `${BASE_URL}/v1/countries/child-age${queryString}`;

    const response = http.get(url, { headers });

    validateResponse(response);

    return response;
}

// Healthcheck endpoint
export function healthcheck() {
    const response = http.get(
        `${BASE_URL}/healthcheck`,
        { headers: DEFAULT_HEADERS }
    );

    validateResponse(response);

    return response;
}

// Internal admin endpoints
export function updateUserDOB(token: string, orgEnvId: string, userId: string, body = {}) {
    const headers = addAuthHeader(token);

    const response = http.put(
        `${BASE_URL}/internal-admin/org-envs/${orgEnvId}/users/${userId}/dob`,
        JSON.stringify(body),
        { headers }
    );

    validateResponse(response);

    return response;
}

export function deleteUserAccount(token: string, orgEnvId: string, userId: string, body = {}) {
    const headers = addAuthHeader(token);

    const response = http.post(
        `${BASE_URL}/internal-admin/org-envs/${orgEnvId}/users/${userId}/delete-account`,
        JSON.stringify(body),
        { headers }
    );

    validateResponse(response);

    return response;
}
