import { ExecutionContext } from '@nestjs/common';
import { ModuleRef, Reflector } from '@nestjs/core';

import { And, Not, Or } from '.';
import { PoliciesGuard } from './policies.guard';
import { IPolicyHandler } from './types';

class TruthyPolicy implements IPolicyHandler {
  async handle(): Promise<boolean> {
    return true;
  }
}
class FalsyPolicy implements IPolicyHandler {
  async handle(): Promise<boolean> {
    return false;
  }
}

describe('PolicyGuard', () => {
  let guard: PoliciesGuard;
  const reflectMock = { get: jest.fn().mockReturnValue([]) };
  const moduleRefMock = {
    get: jest.fn(),
  };
  const getRequestMock = { getRequest: jest.fn() };
  const executionContextMock = {
    switchToHttp: () => getRequestMock,
    getHandler: jest.fn(),
  };
  const requestMock = { foo: 'bar' };

  beforeEach(() => {
    guard = new PoliciesGuard(reflectMock as unknown as Reflector, moduleRefMock as unknown as ModuleRef);
    getRequestMock.getRequest.mockReturnValueOnce(requestMock);
    moduleRefMock.get.mockImplementation((ref) => {
      if (ref === TruthyPolicy) {
        return new TruthyPolicy();
      } else if (ref === FalsyPolicy) {
        return new FalsyPolicy();
      }
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(new PoliciesGuard(reflectMock as unknown as Reflector, moduleRefMock as unknown as ModuleRef)).toBeDefined();
  });

  describe('canActivate() with functions', () => {
    it('should return false for canActivate', async () => {
      const handlerMocks = [jest.fn(), jest.fn()];
      reflectMock.get.mockReturnValueOnce(handlerMocks);
      for (const mock of handlerMocks) {
        mock.mockResolvedValueOnce(false);
      }
      expect(await guard.canActivate(executionContextMock as unknown as ExecutionContext)).toBe(false);
    });

    it('should return true for canActivate', async () => {
      const handlerMocks = [jest.fn(), jest.fn()];
      reflectMock.get.mockReturnValueOnce(handlerMocks);
      for (const mock of handlerMocks) {
        mock.mockResolvedValueOnce(true);
      }
      expect(await guard.canActivate(executionContextMock as unknown as ExecutionContext)).toBe(true);
      for (const mock of handlerMocks) {
        expect(mock).toHaveBeenCalledWith(requestMock);
      }
    });
  });

  describe('canActivate() with classes', () => {
    class TruthyPolicy implements IPolicyHandler {
      async handle(): Promise<boolean> {
        return true;
      }
    }
    class FalsyPolicy implements IPolicyHandler {
      async handle(): Promise<boolean> {
        return false;
      }
    }
    let falsyHandler: FalsyPolicy;
    let truthyHandler: FalsyPolicy;
    let falsyHandlerSpy: jest.SpyInstance;
    let truthyHandlerSpy: jest.SpyInstance;

    beforeEach(() => {
      falsyHandler = new FalsyPolicy();
      truthyHandler = new TruthyPolicy();
      falsyHandlerSpy = jest.spyOn(falsyHandler, 'handle');
      truthyHandlerSpy = jest.spyOn(truthyHandler, 'handle');
    });

    afterEach(() => {
      jest.clearAllMocks();
    });

    it('should return false for canActivate', async () => {
      const handlerMocks = [falsyHandler, falsyHandler, truthyHandler];
      reflectMock.get.mockReturnValueOnce(handlerMocks);
      expect(await guard.canActivate(executionContextMock as unknown as ExecutionContext)).toBe(false);
      expect(falsyHandlerSpy).toHaveBeenCalledTimes(1);
      expect(falsyHandlerSpy).toHaveBeenCalledWith(requestMock);
      expect(truthyHandlerSpy).not.toHaveBeenCalled();
    });

    it('should return true for no handlers', async () => {
      const handlerMocks: FalsyPolicy[] = [];
      reflectMock.get.mockReturnValueOnce(handlerMocks);
      expect(await guard.canActivate(executionContextMock as unknown as ExecutionContext)).toBe(true);
    });

    it('should return true for canActivate if all handlers return true', async () => {
      const handlerMocks = [truthyHandler, truthyHandler, truthyHandler];
      reflectMock.get.mockReturnValueOnce(handlerMocks);
      expect(await guard.canActivate(executionContextMock as unknown as ExecutionContext)).toBe(true);
      expect(truthyHandlerSpy).toHaveBeenCalledTimes(3);
      expect(truthyHandlerSpy).toHaveBeenCalledWith(requestMock);
    });

    it('should wrap in an OR condition handlers passed in an array as an argument', async () => {
      const handlerMocks = [truthyHandler, [falsyHandler, truthyHandler]];
      reflectMock.get.mockReturnValueOnce(handlerMocks);
      expect(await guard.canActivate(executionContextMock as unknown as ExecutionContext)).toBe(true);
      expect(falsyHandlerSpy).toHaveBeenCalledTimes(1);
      expect(falsyHandlerSpy).toHaveBeenCalledWith(requestMock);
      expect(truthyHandlerSpy).toHaveBeenCalledTimes(2);
    });

    it('should not call more handlers in an OR condition if one already passed', async () => {
      const handlerMocks = [truthyHandler, [truthyHandler, falsyHandler]];
      reflectMock.get.mockReturnValueOnce(handlerMocks);
      expect(await guard.canActivate(executionContextMock as unknown as ExecutionContext)).toBe(true);
      expect(truthyHandlerSpy).toHaveBeenCalledTimes(2);
      expect(falsyHandlerSpy).toHaveBeenCalledTimes(0);
    });

    it('should return false if all handlers fail in an OR condition', async () => {
      const handlerMocks = [truthyHandler, [falsyHandler, falsyHandler]];
      reflectMock.get.mockReturnValueOnce(handlerMocks);
      expect(await guard.canActivate(executionContextMock as unknown as ExecutionContext)).toBe(false);
      expect(truthyHandlerSpy).toHaveBeenCalledTimes(1);
      expect(falsyHandlerSpy).toHaveBeenCalledTimes(2);
    });
  });

  describe('canActivate() with injection', () => {
    let falsyHandler: FalsyPolicy;
    let truthyHandler: FalsyPolicy;
    let falsyHandlerSpy: jest.SpyInstance;
    let truthyHandlerSpy: jest.SpyInstance;

    beforeEach(() => {
      falsyHandler = new FalsyPolicy();
      truthyHandler = new TruthyPolicy();
      falsyHandlerSpy = jest.spyOn(falsyHandler, 'handle');
      truthyHandlerSpy = jest.spyOn(truthyHandler, 'handle');
    });

    it('should return false for canActivate', async () => {
      const handlerMocks = [
        {
          useClass: And,
          inject: [TruthyPolicy, falsyHandler],
        },
      ];
      reflectMock.get.mockReturnValueOnce(handlerMocks);
      expect(await guard.canActivate(executionContextMock as unknown as ExecutionContext)).toBe(false);
      expect(falsyHandlerSpy).toHaveBeenCalledTimes(1);
      expect(falsyHandlerSpy).toHaveBeenCalledWith(requestMock);
      expect(truthyHandlerSpy).not.toHaveBeenCalled();
    });

    it('should return true for canActivate if all handlers return true', async () => {
      const handlerMocks = [
        {
          useClass: And,
          inject: [TruthyPolicy, truthyHandler],
        },
      ];
      reflectMock.get.mockReturnValueOnce(handlerMocks);
      expect(await guard.canActivate(executionContextMock as unknown as ExecutionContext)).toBe(true);
      expect(truthyHandlerSpy).toHaveBeenCalledTimes(1);
      expect(truthyHandlerSpy).toHaveBeenCalledWith(requestMock);
    });

    it('should return true for canActivate if all handlers return true with nested conditions', async () => {
      const handlerMocks = [
        {
          useClass: And,
          inject: [
            truthyHandler,
            {
              useClass: Or,
              inject: [
                falsyHandler,
                {
                  useClass: Or,
                  inject: [
                    falsyHandler,
                    {
                      useClass: And,
                      inject: [
                        truthyHandler,
                        {
                          useClass: Not,
                          inject: [
                            {
                              useClass: And,
                              inject: [truthyHandler, falsyHandler],
                            },
                          ],
                        },
                      ],
                    },
                  ],
                },
              ],
            },
          ],
        },
      ];
      reflectMock.get.mockReturnValueOnce(handlerMocks);
      expect(await guard.canActivate(executionContextMock as unknown as ExecutionContext)).toBe(true);
      expect(truthyHandlerSpy).toHaveBeenCalledTimes(3);
      expect(falsyHandlerSpy).toHaveBeenCalledTimes(3);
    });
  });
});
