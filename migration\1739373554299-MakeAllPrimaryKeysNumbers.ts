import { MigrationInterface, QueryRunner } from "typeorm";

export class MakeAllPrimaryKeysNumbers1739373554299 implements MigrationInterface {
    name = 'MakeAllPrimaryKeysNumbers1739373554299'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "jwk" DROP CONSTRAINT "PK_f7e463dc212c93c426852157839"
        `);
        await queryRunner.query(`
            ALTER TABLE "jwk" DROP COLUMN "id"
        `);
        await queryRunner.query(`
            ALTER TABLE "jwk"
            ADD "id" SERIAL NOT NULL
        `);
        await queryRunner.query(`
            ALTER SEQUENCE jwk_id_seq RESTART WITH 1000000000
        `);
        await queryRunner.query(`
            ALTER TABLE "jwk"
            ADD CONSTRAINT "PK_f7e463dc212c93c426852157839" PRIMARY KEY ("orgEnvId", "id")
        `);
        await queryRunner.query(`
            ALTER TABLE "webhook" DROP CONSTRAINT "PK_2b0218bbe54cf898a596645cdbc"
        `);
        await queryRunner.query(`
            ALTER TABLE "webhook" DROP COLUMN "id"
        `);
        await queryRunner.query(`
            ALTER TABLE "webhook"
            ADD "id" SERIAL NOT NULL
        `);
        await queryRunner.query(`
            ALTER SEQUENCE webhook_id_seq RESTART WITH 1000000000
        `);
        await queryRunner.query(`
            ALTER TABLE "webhook"
            ADD CONSTRAINT "PK_2b0218bbe54cf898a596645cdbc" PRIMARY KEY ("orgEnvId", "id")
        `);
        await queryRunner.query(`
            ALTER TABLE "refresh_token" DROP CONSTRAINT "PK_9d9239e21736fef205b3e0ce37f"
        `);
        await queryRunner.query(`
            ALTER TABLE "refresh_token" DROP COLUMN "id"
        `);
        await queryRunner.query(`
            ALTER TABLE "refresh_token"
            ADD "id" SERIAL NOT NULL
        `);
        await queryRunner.query(`
            ALTER SEQUENCE refresh_token_id_seq RESTART WITH 1000000000
        `);
        await queryRunner.query(`
            ALTER TABLE "refresh_token"
            ADD CONSTRAINT "PK_9d9239e21736fef205b3e0ce37f" PRIMARY KEY ("orgEnvId", "id")
        `);

        await queryRunner.query(`
            ALTER SEQUENCE activation_id_seq RESTART WITH 1000000000
        `);
        await queryRunner.query(`
            ALTER SEQUENCE app_translation_id_seq RESTART WITH 1000000000
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "refresh_token" DROP CONSTRAINT "PK_9d9239e21736fef205b3e0ce37f"
        `);
        await queryRunner.query(`
            ALTER TABLE "refresh_token" DROP COLUMN "id"
        `);
        await queryRunner.query(`
            ALTER TABLE "refresh_token"
            ADD "id" uuid NOT NULL DEFAULT uuid_generate_v4()
        `);
        await queryRunner.query(`
            ALTER TABLE "refresh_token"
            ADD CONSTRAINT "PK_9d9239e21736fef205b3e0ce37f" PRIMARY KEY ("id", "orgEnvId")
        `);
        await queryRunner.query(`
            ALTER TABLE "webhook" DROP CONSTRAINT "PK_2b0218bbe54cf898a596645cdbc"
        `);
        await queryRunner.query(`
            ALTER TABLE "webhook" DROP COLUMN "id"
        `);
        await queryRunner.query(`
            ALTER TABLE "webhook"
            ADD "id" uuid NOT NULL DEFAULT uuid_generate_v4()
        `);
        await queryRunner.query(`
            ALTER TABLE "webhook"
            ADD CONSTRAINT "PK_2b0218bbe54cf898a596645cdbc" PRIMARY KEY ("id", "orgEnvId")
        `);
        await queryRunner.query(`
            ALTER TABLE "jwk" DROP CONSTRAINT "PK_f7e463dc212c93c426852157839"
        `);
        await queryRunner.query(`
            ALTER TABLE "jwk" DROP COLUMN "id"
        `);
        await queryRunner.query(`
            ALTER TABLE "jwk"
            ADD "id" uuid NOT NULL DEFAULT uuid_generate_v4()
        `);
        await queryRunner.query(`
            ALTER TABLE "jwk"
            ADD CONSTRAINT "PK_f7e463dc212c93c426852157839" PRIMARY KEY ("id", "orgEnvId")
        `);
    }

}
