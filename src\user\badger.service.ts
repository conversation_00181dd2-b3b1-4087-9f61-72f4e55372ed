import { Injectable } from '@nestjs/common';
import { SALogger } from '@superawesome/freekws-common-logger';
import { HttpService } from '@superawesome/freekws-http-nestjs-service';
import { MetricsService } from '@superawesome/freekws-metrics-nestjs-service';

import { ConfigService } from '../common/services/config/config.service';
import { BadgerConfig } from '../common/services/config/types';

export enum EbadgerDecision {
  ALLOW = 'allow',
  REJECT = 'reject',
  REVIEW = 'review',
  NUDGE = 'nudge',
}

type TBadgerTag = {
  id: string;
  confidence?: number;
};

type TBadgerResponseCommonFields = {
  module_id: string;
  labels: string;
  tags: {
    id: string;
    confidence: number;
  }[];
  from_cache: boolean;
  cache_key_alias: string;
};

type TBadgerDisplayNameResponse = TBadgerResponseCommonFields & {
  response: {
    confidence: number;
    model_info: {
      inference_args: {
        conf_threshold: number;
        max_text_length: number;
      };
      model_name: string;
      service_name: string;
      service_version: string;
    };
    predication: number;
  };
};

type TBadgerLexiconResponse = TBadgerResponseCommonFields & {
  response: {
    confidence: number;
    hashed: string;
    prediction: number;
    matches: string[];
    matches_count: number;
    metadata: {
      repo_commit_sha: string;
      service_name: string;
      service_version: string;
    };
  };
  violations: string[];
};

type TBadgerBadWordsResponse = TBadgerResponseCommonFields & {
  response: {
    bad_words: string[];
  };
};

type TBadgerEGPIFilterResponse = TBadgerResponseCommonFields & {
  response: {
    context: string;
    rule_set: string;
    original_text: string;
    filtered_text: string;
    egpi_elements: string[];
  };
};

export type TBadgerResponse = {
  screen_id: string;
  workload: string;
  customer_facing: boolean;
  action: EbadgerDecision;
  request: {
    text: string;
  };
  modules: string[];
  tags: {
    id: string;
    confidence?: number;
  }[];
  text_reponse: {
    action: EbadgerDecision;
    sha256_hash: string;
    hash_decision: boolean;
    classifier_response: {
      't-eml-display-name'?: TBadgerDisplayNameResponse;
      't-eml-lexicon'?: TBadgerLexiconResponse;
      't-eml-screen'?: TBadgerDisplayNameResponse;
      't-epic-badwords'?: TBadgerBadWordsResponse;
      't-epic-egpi-filter'?: TBadgerEGPIFilterResponse;
    };
  };
};

const acceptedLanguages = new Set([
  'en',
  'de',
  'fr',
  'ru',
  'it',
  'pt',
  'pt-BR',
  'es',
  'zh-CN',
  'zh-TW',
  'ja',
  'ko',
  'th',
  'tr',
]);

@Injectable()
export class BadgerService {
  private readonly badgerConfig: BadgerConfig;
  private logger: SALogger;

  constructor(config: ConfigService, private metrics: MetricsService, private httpService: HttpService) {
    this.badgerConfig = config.getBadgerConfig();
    this.logger = new SALogger();
  }

  onModuleInit() {
    this.httpService.init({ upstream: '' });
  }

  async moderateUsername(username: string, language?: string) {
    const languageCode = this.getLanguageCode(language);
    try {
      const response = await this.httpService.request({
        method: 'POST',
        url: `${this.badgerConfig.baseURL}/v4/moderate/screen/kws_usernames`,
        data: {
          text: username,
        },
        headers: {
          'Content-Type': 'application/json',
          'X-Epic-Auth-Key': `${this.badgerConfig.apiKey}`,
        },
      });

      const responseData = response.data as TBadgerResponse;

      this.sendModerationMetrics(responseData.tags, languageCode, responseData.action);

      return responseData.action;
    } catch (error) {
      this.logger.error('Badger error response', error);
      return 'BADGER_ERROR_RESPONSE';
    }
  }

  private sendModerationMetrics(badgerTags: TBadgerTag[], language: string, badgerResult: EbadgerDecision) {
    const metricTags: string[] = [`decision:${badgerResult}`, `language:${language}`];
    const filteredTags = this.stripDelimitingTags(badgerTags);

    for (const tag of filteredTags) {
      metricTags.push(`badger_${tag.id}:true`);
    }

    this.metrics.increment('sa.classicwrapper.kws_badger_result', 1, metricTags);
  }

  private stripDelimitingTags(tags: TBadgerTag[]) {
    const tagsToIgnore = new Set([
      'eml-display-name-tags',
      'eml-screen-tags',
      'eml-lexicon-tags',
      'epic-badwords-text-tags',
    ]);

    if (tags) {
      return tags.filter((tag) => !tagsToIgnore.has(tag.id));
    }

    return [];
  }

  private getLanguageCode(language: string | undefined) {
    if (!language) {
      return 'en';
    }
    const languageLowerCase = language.trim();
    if (acceptedLanguages.has(languageLowerCase)) {
      return languageLowerCase;
    }
    const languageCode = languageLowerCase.padEnd(4, ' ').slice(0, 4).trim();
    if (acceptedLanguages.has(languageCode)) {
      return languageCode;
    }
    return 'en';
  }
}
