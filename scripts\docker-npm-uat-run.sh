#!/bin/bash -ex
docker compose -f docker-compose.uat.yml rm -fsv
docker compose rm -fsv # makes sure no containers from the regular docker compose file keep running

if [ -z "${CIRCLE_SHA1}" ]; then
  export TAG=local
  docker compose -f docker-compose.uat.yml build
else
  export TAG=${CIRCLE_SHA1}-builder
  docker compose -f docker-compose.uat.yml build stub # temporarily build the image on ci too, to avoid reusing images from other runs
  docker compose -f docker-compose.uat.yml pull -q

  if [ "$1" == "test:acceptance" ]; then
    export FEATURES=$(circleci tests glob "test-acceptance/**/**.feature" | circleci tests split --split-by=timings)
    echo "Testing Cucumber feature files: ${FEATURES}"
  fi
fi

./scripts/docker-compose-volume-perms.sh

docker compose -f docker-compose.uat.yml run --rm project npm run "$@"
