import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateMissingNullAndNotNullConstrains1739964900067 implements MigrationInterface {
  name = 'UpdateMissingNullAndNotNullConstrains1739964900067';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "app_translation" DROP CONSTRAINT "FK_5af0040c7cadd700bc5413f6b08"
        `);
    await queryRunner.query(`
            ALTER TABLE "app_translation"
            ALTER COLUMN "appId"
            SET NOT NULL
        `);
    await queryRunner.query(`
            ALTER TABLE "jwk"
            ALTER COLUMN "publicPem"
            SET NOT NULL
        `);
    await queryRunner.query(`
            ALTER TABLE "jwk"
            ALTER COLUMN "privatePem" DROP NOT NULL
        `);
    await queryRunner.query(`
            ALTER TABLE "activation" DROP CONSTRAINT "FK_fd3609535b844eec1cbc4f88c7f"
        `);
    await queryRunner.query(`
            ALTER TABLE "activation" DROP CONSTRAINT "FK_1f69cdad74ce54d97a35ac89ec9"
        `);
    await queryRunner.query(`
            ALTER TABLE "activation"
            ALTER COLUMN "appId"
            SET NOT NULL
        `);
    await queryRunner.query(`
            ALTER TABLE "activation"
            ALTER COLUMN "userId"
            SET NOT NULL
        `);
    await queryRunner.query(`
            ALTER TABLE "app_translation"
            ADD CONSTRAINT "FK_5af0040c7cadd700bc5413f6b08" FOREIGN KEY ("appId", "orgEnvId") REFERENCES "app"("id", "orgEnvId") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
    await queryRunner.query(`
            ALTER TABLE "activation"
            ADD CONSTRAINT "FK_fd3609535b844eec1cbc4f88c7f" FOREIGN KEY ("appId", "orgEnvId") REFERENCES "app"("id", "orgEnvId") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
    await queryRunner.query(`
            ALTER TABLE "activation"
            ADD CONSTRAINT "FK_1f69cdad74ce54d97a35ac89ec9" FOREIGN KEY ("userId", "orgEnvId") REFERENCES "user"("id", "orgEnvId") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
    await queryRunner.query(`
            ALTER TABLE "user" ALTER COLUMN "uuid" DROP NOT NULL
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "activation" DROP CONSTRAINT "FK_1f69cdad74ce54d97a35ac89ec9"
        `);
    await queryRunner.query(`
            ALTER TABLE "activation" DROP CONSTRAINT "FK_fd3609535b844eec1cbc4f88c7f"
        `);
    await queryRunner.query(`
            ALTER TABLE "app_translation" DROP CONSTRAINT "FK_5af0040c7cadd700bc5413f6b08"
        `);
    await queryRunner.query(`
            ALTER TABLE "activation"
            ALTER COLUMN "userId" DROP NOT NULL
        `);
    await queryRunner.query(`
            ALTER TABLE "activation"
            ALTER COLUMN "appId" DROP NOT NULL
        `);
    await queryRunner.query(`
            ALTER TABLE "activation"
            ADD CONSTRAINT "FK_1f69cdad74ce54d97a35ac89ec9" FOREIGN KEY ("userId", "orgEnvId") REFERENCES "user"("id", "orgEnvId") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
    await queryRunner.query(`
            ALTER TABLE "activation"
            ADD CONSTRAINT "FK_fd3609535b844eec1cbc4f88c7f" FOREIGN KEY ("appId", "orgEnvId") REFERENCES "app"("id", "orgEnvId") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
    await queryRunner.query(`
            ALTER TABLE "jwk"
            ALTER COLUMN "privatePem"
            SET NOT NULL
        `);
    await queryRunner.query(`
            ALTER TABLE "jwk"
            ALTER COLUMN "publicPem" DROP NOT NULL
        `);
    await queryRunner.query(`
            ALTER TABLE "app_translation"
            ALTER COLUMN "appId" DROP NOT NULL
        `);
    await queryRunner.query(`
            ALTER TABLE "app_translation"
            ADD CONSTRAINT "FK_5af0040c7cadd700bc5413f6b08" FOREIGN KEY ("appId", "orgEnvId") REFERENCES "app"("id", "orgEnvId") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
    await queryRunner.query(`
            ALTER TABLE "user" ALTER COLUMN "uuid" SET NOT NULL
        `);
  }
}
