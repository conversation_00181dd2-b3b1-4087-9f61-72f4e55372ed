function getUserSettingsResponseBuilder() {
  const settings = [
    { namespace: 'default', settingName: 'whatever' },
    { namespace: 'chat', settingName: 'voice' },
    { namespace: 'chat', settingName: 'text' },
  ];

  return {
    statusCode: 200,
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      response: {
        settings: settings
          .filter(({ namespace, settingName }) => !(namespace === 'default' && settingName === 'unknown'))
          .map(({ namespace, settingName }) => ({
            namespace,
            settingName,
            preferredValue: true,
            effectiveValue: namespace === 'default',
            consentRequestedAt: Date.now() - 1000,
            definition: {
              orgId: '6c039d20-8a03-45de-ba5e-54b3ef581d85',
              namespace,
              settingName,
              valueType: 'boolean',
              translations: {
                en: {
                  label: `${settingName} permission`,
                  parentNotice: `Setting from ${namespace} namespace.\\n\\nTEST ONLY!!!`,
                },
              },
              ageBracket: {
                consentType: namespace === 'default' ? 'opt-out' : 'opt-in-verified',
              },
            },
          })),
      },
    }),
  };
}
