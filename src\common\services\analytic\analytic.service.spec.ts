import { TestingModule } from '@nestjs/testing';
import { HttpService } from '@superawesome/freekws-http-nestjs-service';

import { AnalyticService } from './analytic.service';
import { Testing } from '../../utils';
import { ConfigService } from '../config/config.service';

describe('AnalyticService', () => {
  let service: AnalyticService;
  let http: HttpService;
  let config: ConfigService;

  beforeEach(async () => {
    const module: TestingModule = await Testing.createModule({
      providers: [AnalyticService],
    });

    service = module.get<AnalyticService>(AnalyticService);
    http = module.get(HttpService);
    config = module.get(ConfigService);

    jest.spyOn(config, 'getAnalytic').mockReturnValue({
      baseURL: '',
      upstream: '',
      authorizationHeader: '',
    });

    module.init();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('signupSuccess', () => {
    it('should send event', async () => {
      await expect(service.signupSuccess('US', 1)).resolves.toBeTruthy();
    });

    it('should return false if request fails', async () => {
      jest.spyOn(http, 'request').mockRejectedValueOnce(new Error('ECONNEFUSED'));

      await expect(service.signupSuccess('US', 1)).resolves.toBeFalsy();
    });
  });

  describe('activationSuccess', () => {
    it('should send event', async () => {
      await expect(service.activationSuccess('GB', 1, 'test-client', { language: 'en' })).resolves.toBeTruthy();
    });
  });
});
