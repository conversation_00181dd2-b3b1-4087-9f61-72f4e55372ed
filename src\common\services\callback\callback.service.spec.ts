import { TestingModule } from '@nestjs/testing';
import { CALLBACK_SERVICE_API_CLIENT_INJECT_KEY } from '@superawesome/freekws-callback-service-common';
import { ServiceID } from '@superawesome/freekws-service-activation-service-common';

import { CallbackService } from './callback.service';
import { Testing } from '../../utils';
import { KEYCLOAK_PROVIDER } from '../keycloak/keycloak.module';

describe('CallbackService', () => {
  let service: CallbackService;
  let mockKeycloakService: { getUpToDateServiceAccessToken: jest.Mock };
  let mockCallbackServiceClient: {
    getModule: jest.Mock;
  };
  let mockWebhooksModule: {
    getWebhook: jest.Mock;
  };

  beforeEach(async () => {
    mockWebhooksModule = {
      getWebhook: jest.fn(),
    };

    mockCallbackServiceClient = {
      getModule: jest.fn().mockReturnValue(mockWebhooksModule),
    };

    mockKeycloakService = {
      getUpToDateServiceAccessToken: jest.fn(),
    };

    const module: TestingModule = await Testing.createModule({
      providers: [
        CallbackService,
        {
          provide: CALLBACK_SERVICE_API_CLIENT_INJECT_KEY,
          useValue: mockCallbackServiceClient,
        },
        {
          provide: KEYCLOAK_PROVIDER,
          useValue: mockKeycloakService,
        },
      ],
    });

    service = module.get<CallbackService>(CallbackService);
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  describe('getWebhookAtOrgLevel', () => {
    it.each([
      ['families:user-added-to-group', ServiceID.FAMILY_GROUP],
      ['test-webhook', ServiceID.SETTINGS],
    ])('should get a webhook at the org level with proper authorization', async (webhookName, expectedServiceId) => {
      const orgEnvId = 'org-env-123';
      const mockToken = 'mock-access-token';
      const mockResponse = {
        data: {
          response: {
            id: 'webhook-123',
            name: webhookName,
            url: 'https://example.com/webhook',
          },
        },
      };

      mockKeycloakService.getUpToDateServiceAccessToken.mockResolvedValue(mockToken);
      mockWebhooksModule.getWebhook.mockResolvedValue(mockResponse);

      const result = await service.getWebhookAtOrgLevel(webhookName, orgEnvId);

      expect(mockKeycloakService.getUpToDateServiceAccessToken).toHaveBeenCalledTimes(1);
      expect(mockCallbackServiceClient.getModule).toHaveBeenCalledWith('webhooks');
      expect(mockWebhooksModule.getWebhook).toHaveBeenCalledWith(
        {
          params: {
            webhookName,
            environmentId: orgEnvId,
            serviceId: expectedServiceId,
          },
        },
        {
          headers: {
            Authorization: `Bearer ${mockToken}`,
          },
        },
      );
      expect(result).toEqual(mockResponse.data.response);
    });
  });
});
