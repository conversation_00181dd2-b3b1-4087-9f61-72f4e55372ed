import { OrgEnv } from '../../src/org-env/org-env.entity';

export const orgEnvHost = 'test.com';

const ORG_ENVS: OrgEnv[] = [
  {
    id: 'e67fed5f-da21-4a5c-b1bd-e35bfffe7101',
    orgId: '6c039d20-8a03-45de-ba5e-54b3ef581d85',
    clientId: 'e67fed5f-da21-4a5c-b1bd-e35bfffe7101',
    clientSecret: '25a17cc4-1709-5835-bc78-266d9744e534',
    host: orgEnvHost,
    jwks: [],
    apps: [],
    users: [],
    webhooks: [],
    createdAt: new Date(Date.now()),
    updatedAt: new Date(Date.now()),
  },
];

export default ORG_ENVS;
