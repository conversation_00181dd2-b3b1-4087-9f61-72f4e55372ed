import { TestingModule } from '@nestjs/testing';

import { Jwk<PERSON>ontroller } from './jwk.controller';
import { JWK } from './jwk.entity';
import { JwkRepository } from './jwk.repository';
import { Testing } from '../common/utils';

describe('JwkController', () => {
  let controller: JwkController;
  let jwkRepository: JwkRepository;

  beforeEach(async () => {
    const module: TestingModule = await Testing.createModule({
      controllers: [JwkController],
      providers: [
        {
          provide: JwkRepository,
          useValue: {
            getRecentJwks: jest.fn(),
          },
        },
      ],
    });

    controller = module.get<JwkController>(JwkController);
    jwkRepository = module.get<JwkRepository>(JwkRepository);
  });

  describe('getJwks', () => {
    it('should return JWKs in the correct format', async () => {
      const mockJwks = [
        {
          id: 1,
          algorithm: 'RS256',
          keyType: 'RSA',
          use: 'sig',
          modulus: 'test-modulus',
          exponent: 'test-exponent',
          keyId: 'test-key-id',
          publicPem: 'test-public-pem',
          privatePem: 'test-private-pem',
          certThumbprint: 'test-thumbprint',
          createdAt: new Date('2023-01-01'),
        },
        {
          id: 2,
          algorithm: 'RS256',
          keyType: 'RSA',
          use: 'sig',
          modulus: 'test-modulus-2',
          exponent: 'test-exponent-2',
          keyId: 'test-key-id-2',
          publicPem: 'test-public-pem-2',
          privatePem: 'test-private-pem-2',
          certThumbprint: 'test-thumbprint-2',
          createdAt: new Date('2023-01-02'),
        },
      ] as JWK[];

      jest.spyOn(jwkRepository, 'getRecentJwks').mockResolvedValue(mockJwks);

      const result = await controller.getJwks();

      expect(result).toEqual({
        keys: [
          {
            alg: 'RS256',
            kty: 'RSA',
            use: 'sig',
            n: 'test-modulus',
            e: 'test-exponent',
            kid: 'test-key-id',
          },
          {
            alg: 'RS256',
            kty: 'RSA',
            use: 'sig',
            n: 'test-modulus-2',
            e: 'test-exponent-2',
            kid: 'test-key-id-2',
          },
        ],
      });
    });

    it('should return empty keys array when no JWKs are found', async () => {
      jest.spyOn(jwkRepository, 'getRecentJwks').mockResolvedValue([]);

      const result = await controller.getJwks();

      expect(result).toEqual({
        keys: [],
      });
    });
  });
});
