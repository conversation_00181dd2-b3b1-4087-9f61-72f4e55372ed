import { ValidationPipe } from '@nestjs/common';
import { NestFastifyApplication } from '@nestjs/platform-fastify';
import { SALogger } from '@superawesome/freekws-common-logger';
import { MetricsService } from '@superawesome/freekws-metrics-nestjs-service';
import { startHttpServer } from '@superawesome/freekws-nestjs-http-interceptors';

import { ConfigService } from './common/services/config/config.service';

const logger = new SALogger();

export const bootstrap = async (
  app: NestFastifyApplication,
  listen = true,
  trustCloudfrontProxy = false,
): Promise<void> => {
  if (listen) {
    const configService = app.get<ConfigService>(ConfigService);
    const httpConfig = configService.getHttpConfig();

    await startHttpServer(app, {
      port: httpConfig.port,
      globalPipes: [new ValidationPipe({ whitelist: true, transform: true })],
      corsOptions: httpConfig.cors,
      trustCloudfrontProxy,
      requestIDPassthrough: true,
      requestIdHeader: 'x-epic-correlation-id',
      filterExceptions: false,
      transformResponse: false,
    });

    logger.info(`Server started listening on port ${httpConfig.port}`);
    app.get(MetricsService).increment('sa.classicwrapper.started', 1, []);
  }
};
