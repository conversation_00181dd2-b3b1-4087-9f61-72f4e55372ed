import { getRepositoryToken } from '@nestjs/typeorm';
import { SALogger } from '@superawesome/freekws-common-logger';
import { Repository, LessThan, In } from 'typeorm';

import { RefreshToken } from '../../oauth/refresh-token.entity';
import { BaseJob } from '../base-job';

const logger = new SALogger();

export const REFRESH_TOKEN_RETENTION_DAYS = 30;

export class DeleteOldRefreshTokens extends BaseJob {
  private refreshTokenRepo: Repository<RefreshToken>;
  private retentionDays: number;
  private batchSize: number;

  async run(): Promise<void> {
    logger.info('Starting old refresh token deletion job');
    await super.run();

    this.refreshTokenRepo = this.app.get(getRepositoryToken(RefreshToken));
    this.retentionDays = Number(process.env.REFRESH_TOKEN_RETENTION_DAYS ?? REFRESH_TOKEN_RETENTION_DAYS);
    this.batchSize = Number(process.env.REFRESH_TOKEN_DELETE_BATCH_SIZE ?? 1000);

    await this.deleteOldRefreshTokens();
  }

  private async deleteOldRefreshTokens(): Promise<void> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - this.retentionDays);
    cutoffDate.setHours(0, 0, 0, 0); // Set to start of day (UTC)

    logger.info(`Deleting refresh tokens created before ${cutoffDate.toISOString()}`, {
      retentionDays: this.retentionDays,
      batchSize: this.batchSize,
      dryMode: this.dryMode,
    });

    try {
      let totalDeleted = 0;
      let batchCount = 0;
      let moreToDelete = true;

      while (moreToDelete) {
        const tokensToDelete = await this.refreshTokenRepo.find({
          where: {
            createdAt: LessThan(cutoffDate),
          },
          take: this.batchSize,
          select: ['id', 'orgEnvId', 'createdAt'],
        });

        if (tokensToDelete.length === 0) {
          moreToDelete = false;
          break;
        }

        batchCount++;
        logger.info(`Processing batch ${batchCount}: found ${tokensToDelete.length} tokens to delete`);

        const tokensToDeleteByOrgEnvId = this.tokensByOrgEnvId(tokensToDelete);

        if (this.dryMode) {
          totalDeleted += tokensToDelete.length;
          logger.info(`Batch ${batchCount} (DRY RUN): would delete ${tokensToDelete.length} tokens`);
          break; // Break since dry run mode would be endless
        } else {
          for (const orgEnvId in tokensToDeleteByOrgEnvId) {
            const tokensToDelete = tokensToDeleteByOrgEnvId[orgEnvId];
            if (tokensToDelete.length === 0) {
              continue;
            }

            const deleteResult = await this.refreshTokenRepo.delete({
              id: In(tokensToDelete.map((token) => token.id)),
              orgEnvId: orgEnvId,
            });

            const deletedCount = deleteResult.affected ?? 0;
            totalDeleted += deletedCount;
            logger.info(`Batch ${batchCount} completed: deleted ${deletedCount} tokens by ${orgEnvId}`);
          }
        }
      }

      const action = this.dryMode ? 'would be deleted' : 'deleted';
      logger.info(`Job completed: ${totalDeleted} refresh tokens ${action} in ${batchCount} batches`);
    } catch (error) {
      logger.error('Error occurred while deleting old refresh tokens', {
        error: error.message,
        retentionDays: this.retentionDays,
        batchSize: this.batchSize,
        dryMode: this.dryMode,
      });
      throw error;
    }
  }

  private tokensByOrgEnvId(tokensToDelete: RefreshToken[]) {
    const tokensToDeleteByOrgEnvId = tokensToDelete.reduce((acc, token) => {
      const orgEnvId = token.orgEnvId;
      if (!acc[orgEnvId]) {
        acc[orgEnvId] = [];
      }
      acc[orgEnvId].push(token);
      return acc;
    }, {} as Record<string, RefreshToken[]>);
    return tokensToDeleteByOrgEnvId;
  }
}
