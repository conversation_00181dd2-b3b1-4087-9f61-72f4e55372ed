-- get all the constrains on the Wrapper database
SELECT 
    tc.table_name AS table_name,
    tc.constraint_name,
    tc.constraint_type,
    STRING_AGG(DISTINCT kcu.column_name, ',' ORDER BY kcu.column_name) AS foreign_keys,
    ccu.table_name AS parent_table,
    STRING_AGG(DISTINCT ccu.column_name, ',' ORDER BY ccu.column_name) AS parent_table_keys
FROM 
    information_schema.table_constraints tc
JOIN 
    information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN 
    information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
WHERE 
    tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_schema = 'public'
GROUP BY 
    tc.table_name,
    tc.constraint_name,
    tc.constraint_type,
    ccu.table_name
ORDER BY 
    tc.table_name,
    tc.constraint_name;

-- Drop all the constrains on the database
ALTER TABLE activation DROP CONSTRAINT IF EXISTS FK_1f69cdad74ce54d97a35ac89ec9;
ALTER TABLE activation DROP CONSTRAINT IF EXISTS FK_fd36069535bb44eeec1bc4f886c7;
ALTER TABLE app DROP CONSTRAINT IF EXISTS FK_fa37e2f382350479c9e4071e46c4;
ALTER TABLE app_translation DROP CONSTRAINT IF EXISTS FK_5af0040c7cadd700bc5413f6b08;
ALTER TABLE jwk DROP CONSTRAINT IF EXISTS FK_94f6862e99cb2f6b181e65c0a1d;
ALTER TABLE "user" DROP CONSTRAINT IF EXISTS FK_0e0b81a5feb15f4054d632bb389;
ALTER TABLE webhook DROP CONSTRAINT IF EXISTS FK_701b67b8f1eac2cb20b3fa2cf53;
ALTER TABLE webhook DROP CONSTRAINT IF EXISTS FK_7ef492f8898bb08cd663e4fa15e;



-- Restore foreign key constraints
ALTER TABLE activation ADD CONSTRAINT FK_1f69cdad74ce54d97a35ac89ec9 FOREIGN KEY ("orgEnvId", "userId") REFERENCES "user" ("orgEnvId", id);
ALTER TABLE activation ADD CONSTRAINT FK_fd36069535bb44eeec1bc4f886c7 FOREIGN KEY ("appId", "orgEnvId") REFERENCES app (id, "orgEnvId");
ALTER TABLE app ADD CONSTRAINT FK_fa37e2f382350479c9e4071e46c4 FOREIGN KEY ("orgEnvId") REFERENCES org_env (id);
ALTER TABLE app_translation ADD CONSTRAINT FK_5af0040c7cadd700bc5413f6b08 FOREIGN KEY ("appId", "orgEnvId") REFERENCES app (id, "orgEnvId");
ALTER TABLE jwk ADD CONSTRAINT FK_94f6862e99cb2f6b181e65c0a1d FOREIGN KEY ("orgEnvId") REFERENCES org_env (id);
ALTER TABLE "user" ADD CONSTRAINT FK_0e0b81a5feb15f4054d632bb389 FOREIGN KEY ("orgEnvId") REFERENCES org_env (id);
ALTER TABLE webhook ADD CONSTRAINT FK_701b67b8f1eac2cb20b3fa2cf53 FOREIGN KEY ("appId", "orgEnvId") REFERENCES app (id, "orgEnvId");
ALTER TABLE webhook ADD CONSTRAINT FK_7ef492f8898bb08cd663e4fa15e FOREIGN KEY ("orgEnvId") REFERENCES org_env (id);


