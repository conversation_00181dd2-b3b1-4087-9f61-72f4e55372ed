import { BadRequestException, Body, Controller, HttpCode, HttpStatus, Param, Post, UseGuards } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import {
  FamiliesGuardianRequestExpiredPayloadDto,
  FamiliesGroupDeletedPayloadDto,
  FamiliesUserAddedToFamilyDTO,
  SettingsEffectiveValuesChangedPayloadDTO,
} from '@superawesome/freekws-queue-messages/webhook';
import { FamiliesUserRemovedFromFamilyDTO } from '@superawesome/freekws-queue-messages/webhook/payloads/families-user-removed-from-family.payload.dto';
import { SettingsUserGraduatedPayloadDTO } from '@superawesome/freekws-queue-messages/webhook/payloads/settings-user-graduated.payload.dto';

import { AppLevelWebhookPayload, OrgLevelWebhookPayload } from './types';
import { WebhookService } from './webhook.service';
import { WebhookGuard } from '../common/guards/webhook/webhook.guard';
import { EAPITags } from '../common/types';
import { isNumeric } from '../utils';

@Controller('v1/webhooks/:orgEnvId')
export class WebhookController {
  constructor(private readonly webhookService: WebhookService) {}

  @ApiOperation({
    summary: 'Graduate a child account',
    description: `This endpoint graduates a child account of the given user id. 
    After this the account will no longer be marked as a child account.`,
  })
  @ApiTags(EAPITags.Webhooks)
  @Post('/child-account-graduated')
  @HttpCode(HttpStatus.NO_CONTENT)
  @UseGuards(WebhookGuard)
  async sendAccountGraduated(@Body() data: OrgLevelWebhookPayload<SettingsUserGraduatedPayloadDTO>) {
    await this.webhookService.sendAccountGraduated(data);
  }

  @ApiOperation({
    summary: 'Change effective value for a user',
    description: `This endpoint picks up effective value changes for a given user.`,
  })
  @ApiTags(EAPITags.Webhooks)
  @Post('/settings-effective-values-changed')
  @HttpCode(HttpStatus.NO_CONTENT)
  @UseGuards(WebhookGuard)
  async sendSettingsEffectiveValuesChanged(
    @Param('orgEnvId') orgEnvId: string,
    @Body() data: AppLevelWebhookPayload<SettingsEffectiveValuesChangedPayloadDTO<boolean>>,
  ) {
    if (!isNumeric(data.payload.userId)) {
      throw new BadRequestException('userId must be a number');
    }
    await this.webhookService.sendSettingsEffectiveValuesChanged(data, orgEnvId);
  }

  @ApiOperation({
    summary: 'Remove a user from a family',
    description: `This endpoint listens for messages removing a user from a family.`,
  })
  @ApiTags(EAPITags.Webhooks)
  @Post('/user-removed-from-family')
  @HttpCode(HttpStatus.NO_CONTENT)
  @UseGuards(WebhookGuard)
  async sendUserRemovedFromFamily(@Body() data: OrgLevelWebhookPayload<FamiliesUserRemovedFromFamilyDTO>) {
    await this.webhookService.sendUserRemovedFromFamily(data);
  }

  @ApiOperation({
    summary: 'Adds a user to a family',
    description: `This endpoint listens for messages adding a user to a family.`,
  })
  @ApiTags(EAPITags.Webhooks)
  @Post('/user-added-to-family')
  @HttpCode(HttpStatus.NO_CONTENT)
  @UseGuards(WebhookGuard)
  async sendUserAddedToFamily(@Body() data: OrgLevelWebhookPayload<FamiliesUserAddedToFamilyDTO>) {
    await this.webhookService.sendUserAddedToFamily(data);
  }

  @ApiOperation({
    summary: 'Guardian request expired',
    description: `This endpoint listens for guardian request expired messages.`,
  })
  @ApiTags(EAPITags.Webhooks)
  @Post('/guardian-request-expired')
  @HttpCode(HttpStatus.NO_CONTENT)
  @UseGuards(WebhookGuard)
  async sendGuardianRequestExpired(@Body() data: OrgLevelWebhookPayload<FamiliesGuardianRequestExpiredPayloadDto>) {
    await this.webhookService.sendGuardianRequestExpired(data);
  }

  @ApiOperation({
    summary: 'Removes a family group',
    description: `This endpoint listens for messages removing a family group for a given parent id.`,
  })
  @ApiTags(EAPITags.Webhooks)
  @Post('/families-group-deleted')
  @HttpCode(HttpStatus.NO_CONTENT)
  @UseGuards(WebhookGuard)
  async sendFamilyGroupDeleted(@Body() data: OrgLevelWebhookPayload<FamiliesGroupDeletedPayloadDto>) {
    await this.webhookService.sendFamilyGroupDeleted(data);
  }
}
