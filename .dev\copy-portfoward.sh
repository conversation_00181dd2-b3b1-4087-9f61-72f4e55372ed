#!/bin/bash

# Kubernetes Port-Forward Manager for Windows Git Bash
# This script manages multiple port-forwarding connections to Kubernetes pods

# Color definitions
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color
BOLD='\033[1m'

# Default Kubernetes context (can be overridden with -c flag)
DEFAULT_CONTEXT="$(kubectl config current-context)"

# Array to store background process PIDs and their names
declare -a PIDS=()
declare -a NAMES=()
declare -a PORTS=()
declare -a CONTEXTS=()

check_jq() {
  if ! command -v jq &> /dev/null; then
    print_error "jq is required but not installed."
    echo -e "Please install jq to parse JSON configuration:"
    echo -e "  - For Windows/Git Bash: Download from https://stedolan.github.io/jq/download/"
    echo -e "  - For Linux: sudo apt-get install jq"
    echo -e "  - For macOS: brew install jq"
    exit 1
  fi
}

usage() {
  echo -e "${BOLD}Usage:${NC} $0 [-c CONTEXT] [-f CONFIG_FILE]"
  echo "  -c CONTEXT      Default Kubernetes context to use (default: current context)"
  echo "  -f CONFIG_FILE  JSON configuration file with port-forward definitions (default: ~/.k8s-portforward.json)"
  echo "  -h              Display this help message"
  exit 1
}

print_status() {
  echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
  echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
  echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
  echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
  echo -e "\n${BOLD}${PURPLE}$1${NC}\n"
}

is_port_in_use() {
  local port=$1
  if command -v ss >/dev/null 2>&1; then
    ss -tuln | grep -qE ".*:$port\s"
  else
    netstat -an | grep -qE ".*:$port\s"
  fi
  return $?
}


# Function to clean up background processes on exit
cleanup() {
  print_header "Cleaning up port-forward processes..."

  local success_count=0
  local fail_count=0

  for i in "${!PIDS[@]}"; do
    local pid=${PIDS[$i]}
    local name=${NAMES[$i]}

    if ps -p "$pid" > /dev/null 2>&1; then
      echo -e "${CYAN}Stopping${NC} $name (PID: $pid)"
      kill "$pid" 2>/dev/null
      if [ $? -eq 0 ]; then
        ((success_count++))
      else
        print_error "Failed to kill process $pid ($name)"
        ((fail_count++))
      fi
    fi
  done

  print_success "Successfully terminated $success_count connections"
  if [ $fail_count -gt 0 ]; then
    print_error "Failed to terminate $fail_count connections"
  fi

  exit 0
}

# Set up trap to catch SIGINT (Ctrl+C) and SIGTERM
trap cleanup SIGINT SIGTERM EXIT

CONTEXT="$DEFAULT_CONTEXT"
CONFIG_FILE="$HOME/.k8s-portforward.json"

while getopts "c:f:h" opt; do
  case $opt in
    c) CONTEXT="$OPTARG" ;;
    f) CONFIG_FILE="$OPTARG" ;;
    h) usage ;;
    *) usage ;;
  esac
done

check_jq

CONFIG_FILE=$(echo "$CONFIG_FILE" | sed 's/\\/\//g')
if [[ "$(uname -s)" == *"MINGW"* ]] || [[ "$(uname -s)" == *"MSYS"* ]]; then
  CONFIG_FILE=$(cygpath -w "$CONFIG_FILE")
fi

if [ ! -f "$CONFIG_FILE" ]; then
  print_warning "Configuration file not found: $CONFIG_FILE"
  print_status "Creating a sample JSON configuration file..."

  cat > "$CONFIG_FILE" << EOF
{
  "connections": [
    {
      "name": "settings",
      "namespace": "rds-bastion",
      "podPattern": "freekws-settings",
      "portMapping": "7996:5432",
      "enabled": true,
      "context": null
    }
  ]
}
EOF

  print_success "Sample JSON configuration file created at: $CONFIG_FILE"
  echo "Please edit this file to configure your port-forwarding connections."
  echo "Set 'context' to null to use the default context, or specify a specific context for each connection."
  echo "Then run this script again."
  exit 0
fi

# Create a temporary directory for log files
LOG_DIR=$(mktemp -d 2>/dev/null || mktemp -d -t 'k8s-portforward-logs')
print_status "Storing logs in: $LOG_DIR"

print_header "Reading configuration"
print_status "Configuration file: $CONFIG_FILE"

if ! jq empty "$CONFIG_FILE" 2>/dev/null; then
  print_error "Invalid JSON format in configuration file: $CONFIG_FILE"
  exit 1
fi

TOTAL_CONNECTIONS=$(jq '.connections | map(select(.enabled == true)) | length' "$CONFIG_FILE")
print_status "Found $TOTAL_CONNECTIONS enabled connections"

print_header "Starting port-forwarding connections"

# Track success and failure counts
SUCCESS_COUNT=0
FAILURE_COUNT=0
SKIPPED_COUNT=0

# Store connection data in temporary arrays to avoid subshell issues
mapfile -t CONN_NAMES < <(jq -r '.connections[] | select(.enabled == true) | .name' "$CONFIG_FILE" | tr -d '\r')
mapfile -t CONN_NAMESPACES < <(jq -r '.connections[] | select(.enabled == true) | .namespace' "$CONFIG_FILE" | tr -d '\r')
mapfile -t CONN_POD_PATTERNS < <(jq -r '.connections[] | select(.enabled == true) | .podPattern' "$CONFIG_FILE" | tr -d '\r')
mapfile -t CONN_PORT_MAPPINGS < <(jq -r '.connections[] | select(.enabled == true) | .portMapping' "$CONFIG_FILE" | tr -d '\r')
mapfile -t CONN_CONTEXTS < <(jq -r '.connections[] | select(.enabled == true) | .context' "$CONFIG_FILE" | tr -d '\r')

# Process each connection
for i in "${!CONN_NAMES[@]}"; do
  name="${CONN_NAMES[$i]}"
  namespace="${CONN_NAMESPACES[$i]}"
  pod_pattern="${CONN_POD_PATTERNS[$i]}"
  port_mapping="${CONN_PORT_MAPPINGS[$i]}"
  conn_context="${CONN_CONTEXTS[$i]}"

  # Determine which context to use
  use_context="$CONTEXT"
  if [[ "$conn_context" != "null" ]]; then
    use_context="$conn_context"
    print_status "Using connection-specific context: $use_context"
  fi

  # Extract local port for checking
  local_port=$(echo "$port_mapping" | cut -d':' -f1)

  echo -e "\n${BOLD}${CYAN}Setting up:${NC} $name"
  echo -e "  ${BOLD}Context:${NC} $use_context"
  echo -e "  ${BOLD}Namespace:${NC} $namespace"
  echo -e "  ${BOLD}Pod Pattern:${NC} $pod_pattern"
  echo -e "  ${BOLD}Port Mapping:${NC} $port_mapping"

  # Check if port is already in use
  if is_port_in_use "$local_port"; then
    print_error "Port $local_port is already in use. Cannot set up $name."
    ((FAILURE_COUNT++))
    continue
  fi

  # Switch to the specified context for this connection
  kubectl config use-context "$use_context"
  if [ $? -ne 0 ]; then
    print_error "Failed to switch to context: $use_context"
    ((FAILURE_COUNT++))
    continue
  fi

  # Get the first pod matching the pattern
  pod=$(kubectl -n "$namespace" get pods | grep "$pod_pattern" | awk '{print $1}' | head -n 1)

  if [ -z "$pod" ]; then
    print_error "No pod found matching pattern '$pod_pattern' in namespace '$namespace'"
    ((FAILURE_COUNT++))
    continue
  fi

  print_status "Found pod: $pod"

  # Create log file for this connection
  LOG_FILE="$LOG_DIR/${name}.log"

  # Start port-forwarding in the background
  kubectl --context="$use_context" -n "$namespace" port-forward "$pod" "$port_mapping" > "$LOG_FILE" 2>&1 &

  # Store the PID and name
  pid=$!
  PIDS+=("$pid")
  NAMES+=("$name")
  PORTS+=("$local_port")
  CONTEXTS+=("$use_context")

  # Give kubectl a moment to establish the connection
  sleep 2

  # Check if the process is still running (connection successful)
  if ps -p "$pid" > /dev/null 2>&1; then
    # Check log file for success message
    if grep -q "Forwarding from" "$LOG_FILE"; then
      print_success "Port-forward for $name started successfully (PID: $pid)"
      echo -e "  ${BOLD}Forwarding:${NC} $(grep "Forwarding from" "$LOG_FILE" | head -n 1 | sed 's/Forwarding from //')"
      ((SUCCESS_COUNT++))
    else
      print_warning "Port-forward for $name started (PID: $pid) but no forwarding confirmation yet"
      ((SUCCESS_COUNT++))
    fi
  else
    print_error "Port-forward for $name failed to start"
    echo -e "  ${BOLD}Error:${NC} $(tail -n 2 "$LOG_FILE" | tr -d '\n')"
    ((FAILURE_COUNT++))
  fi
done

# Switch back to the default context
kubectl config use-context "$CONTEXT" > /dev/null 2>&1

print_header "Port-forwarding summary"
print_success "$SUCCESS_COUNT connections established successfully"
if [ $FAILURE_COUNT -gt 0 ]; then
  print_error "$FAILURE_COUNT connections failed"
fi
if [ $SKIPPED_COUNT -gt 0 ]; then
  print_status "$SKIPPED_COUNT connections skipped (disabled)"
fi

if [ $SUCCESS_COUNT -gt 0 ]; then
  echo -e "\n${BOLD}Active port forwards:${NC}"
  for i in "${!PIDS[@]}"; do
    if ps -p "${PIDS[$i]}" > /dev/null 2>&1; then
      echo -e "  ${GREEN}●${NC} ${BOLD}${NAMES[$i]}${NC} - Port ${PORTS[$i]} (PID: ${PIDS[$i]}) - Context: ${CONTEXTS[$i]}"
    else
      echo -e "  ${RED}●${NC} ${BOLD}${NAMES[$i]}${NC} - Port ${PORTS[$i]} (PID: ${PIDS[$i]}) - ${RED}FAILED${NC}"
    fi
  done
fi

echo -e "\n${YELLOW}Press Ctrl+C to stop all connections${NC}"

# Keep the script running until interrupted
while true; do
  sleep 5

  # Periodically check if processes are still running
  for i in "${!PIDS[@]}"; do
    if ! ps -p "${PIDS[$i]}" > /dev/null 2>&1; then
      print_error "${NAMES[$i]} connection lost (PID: ${PIDS[$i]})"
      # Remove from arrays
      unset "PIDS[$i]"
      unset "NAMES[$i]"
      unset "PORTS[$i]"
      unset "CONTEXTS[$i]"
      # Reindex arrays
      PIDS=("${PIDS[@]}")
      NAMES=("${NAMES[@]}")
      PORTS=("${PORTS[@]}")
      CONTEXTS=("${CONTEXTS[@]}")
    fi
  done

  # If all connections are lost, exit
  if [ ${#PIDS[@]} -eq 0 ]; then
    print_error "All connections lost. Exiting."
    exit 1
  fi
done