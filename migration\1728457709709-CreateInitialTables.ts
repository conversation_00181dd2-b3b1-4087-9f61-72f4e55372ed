import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateInitialTables1728457709709 implements MigrationInterface {
  name = 'CreateInitialTables1728457709709';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            CREATE TABLE "audit" (
                "transactionId" integer NOT NULL,
                "requestId" character varying(36),
                "message" text,
                CONSTRAINT "PK_cb5c9a032dbf801c67f6b5c49ac" PRIMARY KEY ("transactionId")
            )
        `);
    await queryRunner.query(`
            CREATE TABLE "app_translation" (
                "id" SERIAL NOT NULL,
                "language" character varying NOT NULL,
                "description" character varying,
                "termsAndConditionsUrl" character varying,
                "privacyPolicyUrl" character varying,
                "logoUrl" character varying,
                "iconUrl" character varying,
                "headerLogoUrl" character varying,
                "faviconUrl" character varying,
                "splashScreenLogoUrl" character varying,
                "splashScreenBgImageUrl" character varying,
                "mainContainerBgImageUrl" character varying,
                "mainContainerBgImageFill" character varying,
                "brandingEmailHeaderBgImg" character varying,
                "emailFooterCopy" character varying,
                "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
                "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
                "appId" integer,
                "appOrgEnv" character varying,
                CONSTRAINT "PK_0a1a7d59f78dc64e527ab180e80" PRIMARY KEY ("id")
            )
        `);
    await queryRunner.query(`
            CREATE TABLE "jwk" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "algorythm" character varying NOT NULL,
                "keyType" character varying NOT NULL,
                "use" character varying NOT NULL,
                "certThumbprint" character varying NOT NULL,
                "keyId" character varying NOT NULL,
                "publicPem" character varying NOT NULL,
                "privatePem" character varying NOT NULL,
                "exponent" character varying NOT NULL,
                "modulus" character varying NOT NULL,
                "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
                "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
                "appId" integer,
                "appOrgEnv" character varying,
                CONSTRAINT "PK_b8bf5700bbb0af854f57892382e" PRIMARY KEY ("id")
            )
        `);
    await queryRunner.query(`
            CREATE TABLE "app" (
                "id" SERIAL NOT NULL,
                "orgEnvId" character varying NOT NULL,
                "productId" character varying NOT NULL,
                "productEnvId" character varying NOT NULL,
                "name" character varying NOT NULL,
                "mode" character varying NOT NULL,
                "oauthClientId" character varying,
                "oauthCallback" character varying,
                "apiKey" character varying NOT NULL,
                "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
                "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
                CONSTRAINT "PK_a64f5da71122d7dfecd5a26ed15" PRIMARY KEY ("id", "orgEnvId")
            )
        `);
    await queryRunner.query(`
            CREATE TABLE "user" (
                "id" SERIAL NOT NULL,
                "orgEnvId" character varying NOT NULL,
                "username" character varying,
                "password" character varying,
                "externalId" character varying,
                "passwordResetToken" character varying,
                "passwordResetTokenExpiry" TIMESTAMP,
                "dateOfBirth" date,
                "language" character varying,
                "signUpCountry" character varying,
                "parentVerified" boolean NOT NULL DEFAULT false,
                "parentExpired" boolean NOT NULL DEFAULT false,
                "parentRejected" boolean NOT NULL DEFAULT false,
                "parentIdVerified" boolean NOT NULL DEFAULT false,
                "parentDeleted" boolean NOT NULL DEFAULT false,
                "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
                "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
                CONSTRAINT "PK_eaa00d91db9b1af4fc1be18e6b1" PRIMARY KEY ("id", "orgEnvId")
            )
        `);
    await queryRunner.query(`
            CREATE UNIQUE INDEX "IDX_bfb14a1cc741c1fb77d99e7110" ON "user" ("passwordResetToken")
        `);
    await queryRunner.query(`
            CREATE TABLE "org_env" (
                "id" character varying NOT NULL,
                "orgId" character varying NOT NULL,
                "clientId" character varying NOT NULL,
                "clientSecret" character varying NOT NULL,
                "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
                "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
                CONSTRAINT "PK_3a6a2a1c99ece46e33539808056" PRIMARY KEY ("id")
            )
        `);
    await queryRunner.query(`
            CREATE TABLE "refresh_token" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "expires" TIMESTAMP NOT NULL,
                "token" character varying NOT NULL,
                "clientId" character varying NOT NULL,
                "scope" character varying NOT NULL,
                "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
                "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
                "userId" integer,
                "userOrgEnv" character varying,
                "appId" integer,
                "appOrgEnv" character varying,
                CONSTRAINT "PK_b575dd3c21fb0831013c909e7fe" PRIMARY KEY ("id")
            )
        `);
    await queryRunner.query(`
            CREATE UNIQUE INDEX "IDX_c31d0a2f38e6e99110df62ab0a" ON "refresh_token" ("token")
        `);
    await queryRunner.query(`
            CREATE TABLE "webhook" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "name" character varying NOT NULL,
                "description" character varying,
                "url" character varying NOT NULL,
                "secretKey" character varying NOT NULL,
                "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
                "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
                "appId" integer,
                "appOrgEnv" character varying,
                CONSTRAINT "PK_e6765510c2d078db49632b59020" PRIMARY KEY ("id")
            )
        `);
    await queryRunner.query(`
            ALTER TABLE "app_translation"
            ADD CONSTRAINT "FK_287389edc365c4d1bcd1041ab24" FOREIGN KEY ("appId", "appOrgEnv") REFERENCES "app"("id", "orgEnvId") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
    await queryRunner.query(`
            ALTER TABLE "jwk"
            ADD CONSTRAINT "FK_ac120b7c6b7bba4a098323fb26a" FOREIGN KEY ("appId", "appOrgEnv") REFERENCES "app"("id", "orgEnvId") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
    await queryRunner.query(`
            ALTER TABLE "app"
            ADD CONSTRAINT "FK_fa37e2f382530479c9e40714e46" FOREIGN KEY ("orgEnvId") REFERENCES "org_env"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
    await queryRunner.query(`
            ALTER TABLE "user"
            ADD CONSTRAINT "FK_0e0b81a5feb15f4054d632bb389" FOREIGN KEY ("orgEnvId") REFERENCES "org_env"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    await queryRunner.query(`
            ALTER TABLE "refresh_token"
            ADD CONSTRAINT "FK_665a6d621b53a4479de3ff1237d" FOREIGN KEY ("userId", "userOrgEnv") REFERENCES "user"("id", "orgEnvId") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
    await queryRunner.query(`
            ALTER TABLE "refresh_token"
            ADD CONSTRAINT "FK_92f2908f07f30cc7641d6107e55" FOREIGN KEY ("appId", "appOrgEnv") REFERENCES "app"("id", "orgEnvId") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
    await queryRunner.query(`
            ALTER TABLE "webhook"
            ADD CONSTRAINT "FK_f49964ba6614aba451bc1dd027b" FOREIGN KEY ("appId", "appOrgEnv") REFERENCES "app"("id", "orgEnvId") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "webhook" DROP CONSTRAINT "FK_f49964ba6614aba451bc1dd027b"
        `);
    await queryRunner.query(`
            ALTER TABLE "refresh_token" DROP CONSTRAINT "FK_92f2908f07f30cc7641d6107e55"
        `);
    await queryRunner.query(`
            ALTER TABLE "refresh_token" DROP CONSTRAINT "FK_665a6d621b53a4479de3ff1237d"
        `);
    await queryRunner.query(`
            ALTER TABLE "user" DROP CONSTRAINT "FK_0e0b81a5feb15f4054d632bb389"
        `);
    await queryRunner.query(`
            ALTER TABLE "app" DROP CONSTRAINT "FK_fa37e2f382530479c9e40714e46"
        `);
    await queryRunner.query(`
            ALTER TABLE "jwk" DROP CONSTRAINT "FK_ac120b7c6b7bba4a098323fb26a"
        `);
    await queryRunner.query(`
            ALTER TABLE "app_translation" DROP CONSTRAINT "FK_287389edc365c4d1bcd1041ab24"
        `);
    await queryRunner.query(`
            DROP TABLE "webhook"
        `);
    await queryRunner.query(`
            DROP INDEX "public"."IDX_c31d0a2f38e6e99110df62ab0a"
        `);
    await queryRunner.query(`
            DROP TABLE "refresh_token"
        `);
    await queryRunner.query(`
            DROP TABLE "org_env"
        `);
    await queryRunner.query(`
            DROP INDEX "public"."IDX_bfb14a1cc741c1fb77d99e7110"
        `);
    await queryRunner.query(`
            DROP TABLE "user"
        `);
    await queryRunner.query(`
            DROP TABLE "app"
        `);
    await queryRunner.query(`
            DROP TABLE "jwk"
        `);
    await queryRunner.query(`
            DROP TABLE "app_translation"
        `);
    await queryRunner.query(`
            DROP TABLE "audit"
        `);
  }
}
