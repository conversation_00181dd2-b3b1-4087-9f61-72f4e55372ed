import { PostgresConnectionOptions } from 'typeorm/driver/postgres/PostgresConnectionOptions';

import { TEST_DATABASE_URL } from './constants';
import { ConfigService } from '../../src/common/services/config/config.service';

export class TestConfigService extends ConfigService {
  override getTypeormConfig(): PostgresConnectionOptions {
    const typeormConfig: PostgresConnectionOptions = super.getTypeormConfig() as PostgresConnectionOptions;
    if (process.env.NODE_ENV !== 'myhost' && typeormConfig.replication?.master.url !== TEST_DATABASE_URL) {
      // Just a safeguard in case running tests against a real db
      throw new Error('not running against a test db');
    }
    return typeormConfig;
  }
}
