import { NestApplication } from '@nestjs/core';
import { SALogger } from '@superawesome/freekws-common-logger';

const logger = new SALogger();

export class BaseJob {
  constructor(protected app: NestApplication, protected dryMode = true) {}

  async run(): Promise<void> {
    if (this.dryMode) {
      logger.info('RUNNING IN DRY MODE');
    } else {
      logger.info('RUNNING IN PRODUCTION MODE');
    }
  }
}
