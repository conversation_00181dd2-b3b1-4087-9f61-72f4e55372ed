import { BadRequestException } from '@nestjs/common';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { OrgEnv } from './org-env.entity';
import { OrgEnvService } from './org-env.service';
import { UnauthenticatedFastifyRequest } from '../common/guards/inject-org-env/types';
import { Testing } from '../common/utils';

describe('OrgEnvRepository', () => {
  let service: OrgEnvService;
  let repository: Repository<OrgEnv>;

  beforeEach(async () => {
    const module = await Testing.createModule({
      providers: [OrgEnvService],
    });

    service = module.get(OrgEnvService);
    repository = module.get(getRepositoryToken(OrgEnv));
  });

  describe('getOrgEnvByHost', () => {
    it('should return OrgEnv by hostname', async () => {
      jest.spyOn(repository, 'findOneBy').mockResolvedValueOnce({} as OrgEnv);

      await expect(service.getOrgEnvByHost('test.com')).resolves.toBeDefined();
    });
  });

  describe('getById', () => {
    it('should return org-env by id', async () => {
      jest.spyOn(repository, 'findOneBy').mockResolvedValueOnce({} as OrgEnv);

      await expect(service.getById('1')).resolves.toBeDefined();
    });
  });

  describe('getOrgEnvFromRequest', () => {
    it('should throw Bad Request Exception if the request header x-forwarded-host is missing', async () => {
      const request = {
        headers: {},
      } as unknown as UnauthenticatedFastifyRequest;

      await expect(service.getOrgEnvFromRequest(request)).rejects.toThrow(BadRequestException);
    });

    it("throws Bad Request Exception if the host value doesn't match any clients", async () => {
      const host = 'test.com';
      const request = {
        headers: {
          'x-forwarded-host': host,
        },
      } as unknown as UnauthenticatedFastifyRequest;
      jest.spyOn(repository, 'findOneBy').mockResolvedValueOnce(null);
      const getOrgEnvByHost = jest.spyOn(service, 'getOrgEnvByHost');

      await expect(service.getOrgEnvFromRequest(request)).rejects.toThrow(BadRequestException);
      expect(getOrgEnvByHost).toHaveBeenCalledWith(host);
    });

    it('injects credentials into request and return true', async () => {
      const request = {
        headers: {
          'x-forwarded-host': 'test.com',
        },
      } as unknown as UnauthenticatedFastifyRequest;
      const orgEnvMockReturn = { id: 'arkveld' } as OrgEnv;
      jest.spyOn(repository, 'findOneBy').mockResolvedValueOnce(orgEnvMockReturn);

      const orgEnv = await service.getOrgEnvFromRequest(request);
      expect(orgEnv).toBe(orgEnvMockReturn);
    });
  });
});
