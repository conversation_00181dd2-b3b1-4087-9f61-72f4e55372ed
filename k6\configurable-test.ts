import { sleep } from 'k6';
import { Options } from 'k6/options';

import {
    getChildAge, getOAuthToken,
    getUser,
    getUserPermissions,
    healthcheck,
    requestUserPermissions
} from './endpoints';

const CLIENT_ID = __ENV.CLIENT_ID || 'your-client-id';
const CLIENT_SECRET = __ENV.CLIENT_SECRET || 'your-client-secret';

export const options: Options = {
    vus: parseInt(__ENV.VUS || '1'),
    duration: __ENV.DURATION || '30s',
    thresholds: {
        http_req_duration: ['p(95)<500'],
        http_req_failed: ['rate<0.01'],
    },
};

type WorkflowKey = keyof typeof workflows;

const APP_ID = __ENV.APP_ID || '207800543';
const USER_ID = __ENV.USER_ID || '**********';
const TEST_WORKFLOWS = __ENV.WORKFLOWS ? __ENV.WORKFLOWS.split(',') as WorkflowKey[] : ['basic'] as WorkflowKey[];

const workflows = {
    basic: (token: string) => {
        healthcheck();
        sleep(1);
    },

    userData: (token: string) => {
        getUser(token, APP_ID, USER_ID);
        sleep(1);

        getUserPermissions(token, APP_ID, USER_ID);
        sleep(1);

        getUserPermissions(token, APP_ID, USER_ID, true); // Extended permissions
        sleep(1);
    },

    childAge: (token: string) => {
        getChildAge({ country: 'US' });
        sleep(1);

        getChildAge({ country: 'GB', dateOfBirth: '2010-01-01' });
        sleep(1);
    },

    permissions: (token: string) => {
        const permissionsBody = {
            parentEmail: '<EMAIL>',
            permissions: ['chat.voice', 'display-name']
        };

        requestUserPermissions(token, APP_ID, USER_ID, permissionsBody);
        sleep(1);
    }
};

export function setup() {
    const token = getOAuthToken(CLIENT_ID, CLIENT_SECRET);

    if (!token) {
        throw new Error('Failed to get OAuth token');
    }

    return { token };
}

export default function ({ token }: { token: string }) {
    for (const workflow of TEST_WORKFLOWS) {
        if (workflows[workflow]) {
            console.log(`Running workflow: ${workflow}`);
            workflows[workflow](token);
        } else {
            console.error(`Workflow '${workflow}' not found`);
        }
    }
} 