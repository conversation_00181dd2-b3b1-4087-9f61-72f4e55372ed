BEGIN;

SET LOCAL my.orgEnvId = 'cf52457d-1a3b-4305-ac43-ff7269329703';
-- Delete orphaned users
DELETE FROM "user"
WHERE id IN (
    SELECT primary_key::integer
    FROM audit_orphaned_table
    WHERE table_name = 'user'
    AND orgEnvId = current_setting('my.orgEnvId')::VARCHAR
);

-- Delete orphaned activations
DELETE FROM activation
WHERE id IN (
    SELECT primary_key::integer
    FROM audit_orphaned_table
    WHERE table_name = 'activation'
    AND orgEnvId = current_setting('my.orgEnvId')::VARCHAR
);

-- Delete orphaned app_translations
DELETE FROM app_translation
WHERE id IN (
    SELECT primary_key::integer
    FROM audit_orphaned_table
    WHERE table_name = 'app_translation'
    AND orgEnvId = current_setting('my.orgEnvId')::VARCHAR
);

-- Delete orphaned webhooks
DELETE FROM webhook
WHERE id IN (
    SELECT primary_key::integer
    FROM audit_orphaned_table
    WHERE table_name = 'webhook'
    AND orgEnvId = current_setting('my.orgEnvId')::VARCHAR
);

COMMIT;

