import { Injectable, OnModuleInit } from '@nestjs/common';
import { HttpService } from '@superawesome/freekws-http-nestjs-service';
import { Span } from 'nestjs-ddtrace';

import { AnalyticsEventsData } from './types';
import { ConfigService } from '../config/config.service';

@Injectable()
@Span()
export class AnalyticService implements OnModuleInit {
  constructor(private readonly http: HttpService, private readonly config: ConfigService) {}

  onModuleInit() {
    const { baseURL, upstream, authorizationHeader } = this.config.getAnalytic();
    this.http.init({
      baseURL: `${baseURL}/v2/events`,
      upstream,
      headers: {
        Authorization: authorizationHeader,
      },
    });
  }

  private async sendEvent(data: AnalyticsEventsData): Promise<boolean> {
    try {
      await this.http.request({
        method: 'POST',
        data,
        timeout: 60000,
      });
      return true;
    } catch {
      return false;
    }
  }

  async signupSuccess(
    signupCountry: string,
    userId: number,
    sessionId?: string,
    userAge?: number,
    extraUserProperties: object = {},
  ): Promise<boolean> {
    const eventsData: AnalyticsEventsData = {
      time: Date.now(),
      geo: signupCountry,
      session: {
        userId,
        id: sessionId,
        user: extraUserProperties,
      },
      events: [
        {
          name: 'signup_success',
          data: {
            childUserId: userId,
            age: userAge,
          },
        },
      ],
    };
    return this.sendEvent(eventsData);
  }

  async activationSuccess(country: string, userId: number, clientId: string, extraUserProperties: object = {}) {
    return this.sendEvent({
      time: Date.now(),
      geo: country,
      session: { userId, user: extraUserProperties },
      events: [
        {
          name: 'activation_success',
          data: {
            clientId,
            childUserId: userId,
          },
        },
      ],
    });
  }
}
