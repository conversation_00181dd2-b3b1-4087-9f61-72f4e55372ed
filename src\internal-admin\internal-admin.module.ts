import { Module } from '@nestjs/common';

import { InternalAdminController } from './internal-admin.controller';
import { InternalAdminService } from './internal-admin.service';
import { AppModule } from '../app/app.module';
import { SettingsModule } from '../common/services/settings/settings.module';
import { UserModule } from '../user/user.module';

@Module({
  imports: [SettingsModule, AppModule, UserModule],
  providers: [InternalAdminService],
  controllers: [InternalAdminController],
  exports: [],
})
export class InternalAdminModule {}
