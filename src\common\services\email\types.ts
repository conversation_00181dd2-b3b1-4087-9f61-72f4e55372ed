﻿export interface IEnqueueForgottenPasswordEmailParams {
  kwsFaqLink: string;
  customerPrivacyPolicyLink?: string;
  customPrimaryColour?: string;
  resetPasswordLink: string;
  language: string;
  toEmailAddress: string;
}

export interface IEnqueueChildChangedPasswordEmailParams {
  kwsFaqLink: string;
  customerPrivacyPolicyLink?: string;
  customPrimaryColour?: string;
  childUsername: string;
  language: string;
  supportEmailAddress: string;
  toEmailAddress: string;
}
