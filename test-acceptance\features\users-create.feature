# features/users-create.feature
@AppUsers
@OAuth
Feature: Create user
  Scenario: Developer requests to create user for an app
    Given app with credentials
      | appId    | 1852416263                           |
      | clientId | b3e00544-5b64-48ab-a7a9-48e1a1dfa062 |
      | secret   | top-secret                           |
    When developer requests oauth token
    When developer create user for app with country '<country>', language '<language>', dob '<dateOfBirth>', email '<email>', parentEmail '<parentEmail>', permissions '<permissions>'
    Then new user values is returned
      And user is minor: '<isMinor>'
    Examples:
    | country | language | dateOfBirth |    email     |   parentEmail   | permissions | isMinor |
    | US      | en       | 2020-10-10  | <EMAIL> | <EMAIL> | chat.voice  | true    |