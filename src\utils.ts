import { AxiosError } from 'axios';

export function isNumeric(value: string) {
  return !Number.isNaN(Number(value)) && value.trim() !== '';
}

export function extractFirstIp(forwardedForHeader: string) {
  if (!forwardedForHeader) {
    return null;
  }

  const ips = forwardedForHeader.split(',').map((ip) => ip.trim());

  return ips.length > 0 ? ips[0] : null;
}

export function yearsSince(dateString: string) {
  const now = new Date();
  const then = new Date(dateString);

  let years = now.getFullYear() - then.getFullYear();

  // Adjust if the current month/day hasn't yet reached the month/day of the original date
  const nowMonth = now.getMonth();
  const thenMonth = then.getMonth();
  if (nowMonth < thenMonth || (nowMonth === thenMonth && now.getDate() < then.getDate())) {
    years--;
  }

  return years;
}

export function isAxiosError<T>(error: unknown): error is AxiosError<T> {
  return error != null && typeof error === 'object' && 'name' in error && error.name === 'AxiosError';
}
