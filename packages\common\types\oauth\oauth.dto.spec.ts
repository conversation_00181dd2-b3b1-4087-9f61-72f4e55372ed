import { instanceToPlain, plainToInstance } from 'class-transformer';
import { validate } from 'class-validator';

import { OAuthTokenDTO, OAuthTokenResponseDTO } from './oauth.dto';

describe(`OAuth DTO's`, () => {
  describe('test OAuthTokenDTO', () => {
    it('should map underscored values', async () => {
      const result = plainToInstance(OAuthTokenDTO, {
        client_id: 'client-id',
        client_secret: 'secret',
        scope: 'app',
        grant_type: 'client_credentials',
      });
      await expect(validate(result)).resolves.toEqual([]);
    });
  });

  describe('test OAuthTokenResponseDTO', () => {
    it('should transform all exposed props', async () => {
      const instance = plainToInstance(OAuthTokenResponseDTO, {
        token_type: 'bearer',
        access_token: 'token',
        refresh_token: 'refresh',
        expires_in: 3600,
      });
      const resp = instanceToPlain(instance);

      expect(resp).toEqual({
        token_type: 'bearer',
        access_token: 'token',
        refresh_token: 'refresh',
        expires_in: 3600,
      });
      expect(resp.tokenType).toBeUndefined();
    });
  });
});
