import { MigrationInterface, QueryRunner } from 'typeorm';

export class DropFKConstrainsOnRefreshTokensForAppAndUser1740131471083 implements MigrationInterface {
  name = 'DropFKConstrainsOnRefreshTokensForAppAndUser1740131471083';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "refresh_token" DROP CONSTRAINT "FK_a8feec0e8cdd14dbf4ed6f4d768"
        `);
    await queryRunner.query(`
            ALTER TABLE "refresh_token" DROP CONSTRAINT "FK_6622c272c93597f3048654d5425"
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "refresh_token"
            ADD CONSTRAINT "FK_6622c272c93597f3048654d5425" FOREIGN KEY ("appId", "orgEnvId") REFERENCES "app"("id", "orgEnvId") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
    await queryRunner.query(`
            ALTER TABLE "refresh_token"
            ADD CONSTRAINT "FK_a8feec0e8cdd14dbf4ed6f4d768" FOREIGN KEY ("userId", "orgEnvId") REFERENCES "user"("id", "orgEnvId") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
  }
}
