import {
  DynamicModule,
  InjectionToken,
  Module,
  ModuleMetadata,
  OptionalFactoryDependency,
  Provider,
} from '@nestjs/common';
import { KeycloakService as KeycloakClient, TKeycloakConfig } from '@superawesome/freekws-auth-library';
import { SALogger } from '@superawesome/freekws-common-logger';
import { MetricsService, MetricsServiceModule } from '@superawesome/freekws-metrics-nestjs-service';
import { EServiceProviders, IKeycloakConfigProvider } from '@superawesome/freekws-nestjs-guards';
import { default as KeycloakConnect } from 'keycloak-connect';

import { ClientKeycloakService } from './client-keycloak.service';
import { ConfigService } from '../config/config.service';

interface KeycloakModuleAsyncOptions extends Pick<ModuleMetadata, 'imports'> {
  inject?: InjectionToken[] | OptionalFactoryDependency[];
  useFactory: (...args: unknown[]) => Promise<TKeycloakConfig> | TKeycloakConfig;
}
export const KEYCLOAK_INSTANCE = EServiceProviders.Keycloak;
export const KEYCLOAK_PROVIDER = 'freekws/keycloak-provider';
export const KEYCLOAK_CONFIG = EServiceProviders.Config;
export const KEYCLOAK_OPTIONS = 'freekws/keycloak-options';

@Module({
  imports: [MetricsServiceModule],
  providers: [ConfigService, ClientKeycloakService],
  exports: [ClientKeycloakService],
})
export class KeycloakModule {
  static forRootAsync(options: KeycloakModuleAsyncOptions): DynamicModule {
    const optionsProvider: Provider = {
      provide: KEYCLOAK_OPTIONS,
      useFactory: options.useFactory,
      inject: options.inject,
    };
    const keycloakProvider: Provider = {
      provide: KEYCLOAK_PROVIDER,
      inject: [KEYCLOAK_OPTIONS, MetricsService],
      useFactory: (config: TKeycloakConfig, metrics: MetricsService) => {
        return new KeycloakClient(config, new SALogger(), metrics);
      },
    };

    const keycloakConfigServiceProvider: Provider = {
      provide: KEYCLOAK_CONFIG,
      inject: options.inject,
      useFactory: (config: ConfigService): IKeycloakConfigProvider => {
        return config;
      },
    };
    const keycloakConnectProvider: Provider = {
      provide: KEYCLOAK_INSTANCE,
      inject: options.inject,
      useFactory: (config: ConfigService): KeycloakConnect.Keycloak => {
        const keycloakConfig = config.getKeycloak();
        return new KeycloakConnect({}, {
          'auth-server-url': keycloakConfig.authServerUrl,
          resource: keycloakConfig.clientId,
          realm: keycloakConfig.realm,
        } as KeycloakConnect.KeycloakConfig);
      },
    };

    return {
      module: KeycloakModule,
      providers: [optionsProvider, keycloakProvider, keycloakConfigServiceProvider, keycloakConnectProvider],
      exports: [keycloakProvider, keycloakConfigServiceProvider, keycloakConnectProvider],
      global: true,
    };
  }
}
