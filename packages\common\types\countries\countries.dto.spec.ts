import { plainToInstance } from 'class-transformer';
import { validate } from 'class-validator';

import { ChildAgeQueryDTO } from './countries.dto';

describe('Countries DTOs', () => {
  describe('test ChildAgeRequest DTO', () => {
    it('should return instance for valid input', async () => {
      const result = plainToInstance(ChildAgeQueryDTO, { dob: '2010-10-10', country: 'US' });
      await expect(validate(result)).resolves.toEqual([]);
      expect(result.dob).toEqual('2010-10-10');
    });

    it('should return an error for invalid date', async () => {
      const result = plainToInstance(ChildAgeQueryDTO, { dob: '2010/10/10', country: 'US' });

      await expect(validate(result)).resolves.toHaveLength(1);
    });

    it('should return an error for invalid date with time', async () => {
      const result = plainToInstance(ChildAgeQueryDTO, { dob: '2010-10-10 03:00:00', country: 'US' });
      await expect(validate(result)).resolves.toHaveLength(1);
    });

    it('should return an error for invalid country', async () => {
      const result = plainToInstance(ChildAgeQueryDTO, { dob: '2010-10-10', country: 'JJ' });
      await expect(validate(result)).resolves.toHaveLength(1);
    });
  });
});
