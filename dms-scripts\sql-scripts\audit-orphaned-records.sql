BEGIN;

SET LOCAL my.orgEnvId = 'cf52457d-1a3b-4305-ac43-ff7269329703';
CREATE TABLE IF NOT EXISTS audit_orphaned_table
(
    id          SERIAL,
    orgEnvId    VARCHAR(255),
    table_name  VARCHAR(255),
    primary_key VARCHAR(255),
    PRIMARY KEY (id, orgEnvId)
);

INSERT INTO audit_orphaned_table (table_name, primary_key, orgEnvId)
SELECT 'user', u.id, u."orgEnvId"
FROM "user" u
WHERE u."orgEnvId" = current_setting('my.orgEnvId')::VARCHAR
  AND (
    NOT EXISTS (SELECT 1
                FROM org_env oe
                WHERE u."orgEnvId" = oe.id)
        OR NOT EXISTS (SELECT 1
                       FROM activation a
                                INNER JOIN app sa ON a."appId" = sa.id
                       WHERE u.id = a."userId"
                         AND u."orgEnvId" = a."orgEnvId")
    );


INSERT INTO audit_orphaned_table (table_name, primary_key, orgEnvId)
SELECT 'activation', a.id, a."orgEnvId"
FROM activation a
WHERE a."orgEnvId" = current_setting('my.orgEnvId')::VARCHAR
  AND (
    NOT EXISTS (SELECT 1
                FROM app sa
                WHERE sa.id = a."appId"
                  AND sa."orgEnvId" = a."orgEnvId")
        OR NOT EXISTS (SELECT 1
                       FROM "user" su
                       WHERE su.id = a."userId"
                         AND su."orgEnvId" = a."orgEnvId")
    );


INSERT INTO audit_orphaned_table (table_name, primary_key, orgEnvId)
SELECT 'app_translation', at.id, at."orgEnvId"
FROM app_translation at
WHERE at."orgEnvId" = current_setting('my.orgEnvId')::VARCHAR
  AND (
    NOT EXISTS (SELECT 1
                FROM app ap
                WHERE at."appId" = ap.id
                  AND at."orgEnvId" = ap."orgEnvId")
    );

INSERT INTO audit_orphaned_table (table_name, primary_key, orgEnvId)
SELECT 'webhook', w.id, w."orgEnvId"
FROM webhook w
WHERE w."orgEnvId" = current_setting('my.orgEnvId')::VARCHAR
  AND (
    NOT EXISTS (SELECT 1
                FROM org_env oe
                WHERE w."orgEnvId" = oe.id)
        OR (
        w."appId" IS NOT NULL
            AND NOT EXISTS (SELECT 1
                            FROM app ap
                                     INNER JOIN app sa ON ap.id = sa.id AND ap."orgEnvId" = sa."orgEnvId"
                            WHERE w."appId" = ap.id
                              AND w."orgEnvId" = ap."orgEnvId")
        )
        OR w."secretKey" IS NULL
    );


SELECT table_name,
       COUNT(*) AS orphaned_count,
       orgEnvId
FROM audit_orphaned_table
WHERE orgEnvId = current_setting('my.orgEnvId')::VARCHAR
GROUP BY table_name, orgEnvId
ORDER BY table_name;


COMMIT;

