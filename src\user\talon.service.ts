import { Injectable, OnModuleInit } from '@nestjs/common';
import { SALogger } from '@superawesome/freekws-common-logger';
import { HttpService } from '@superawesome/freekws-http-nestjs-service';

import { ConfigService } from '../common/services/config/config.service';
import { extractFirstIp } from '../utils';

const TALON_BASE_URL = 'https://talon-service-prod.ol.epicgames.com';
const TALON_BYPASS_HEADER = 'x-kws-bypass-talon';

@Injectable()
export class TalonService implements OnModuleInit {
  private readonly talonAPIKey: string;
  private readonly talonFlowId: string;
  private readonly environment: string;
  private logger: SALogger;

  constructor(config: ConfigService, private httpService: HttpService) {
    this.logger = new SALogger();
    const talonConfig = config.config.talon;
    this.talonAPIKey = talonConfig.apiKey;
    this.talonFlowId = talonConfig.flowId;
    this.environment = config.config.environment;
  }

  onModuleInit() {
    this.httpService.init({ upstream: '' });
  }

  async verify(solveToken: string, headers: Record<string, string>, parentEmail?: string): Promise<boolean> {
    if (this.environment === 'staging' && headers[TALON_BYPASS_HEADER] === 'true') {
      this.logger.info('Bypassing Talon in staging environment');
      return true;
    }

    const endpoint = '/v1/verify';
    const forwardForHeader = headers['x-forwarded-for'];
    const userIp = extractFirstIp(forwardForHeader);

    try {
      const response = await this.httpService.request({
        method: 'POST',
        url: `${TALON_BASE_URL}${endpoint}`,
        data: {
          flow_id: this.talonFlowId,
          solve_token: solveToken,
          user_ip: userIp,
          headers: Object.entries(headers).map(([name, value]) => ({ name, value })),
          form_data: [
            {
              name: 'email',
              value: parentEmail,
            },
          ],
        },
        headers: {
          'X-Epic-Auth-Key': this.talonAPIKey,
          'Content-Type': 'application/json',
        },
      });

      this.logger.info('Talon response', { response: response.data });

      return response.data.solved;
    } catch (error: unknown) {
      this.logger.error('Failed to call Talon', error);

      // Fail open
      return true;
    }
  }
}
