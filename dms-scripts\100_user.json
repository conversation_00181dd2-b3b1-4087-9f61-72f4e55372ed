[{"rule-type": "selection", "rule-id": "100", "rule-name": "Include users table", "object-locator": {"schema-name": "public", "table-name": "users"}, "rule-action": "include", "filters": [{"filter-type": "source", "column-name": "deletedAt", "filter-conditions": [{"filter-operator": "null"}]}]}, {"rule-type": "transformation", "rule-id": "101", "rule-name": "Rename users to user", "rule-action": "rename", "rule-target": "table", "object-locator": {"schema-name": "public", "table-name": "users"}, "value": "user"}, {"rule-type": "transformation", "rule-id": "102", "rule-name": "Add orgEnvId column to users", "rule-target": "column", "object-locator": {"schema-name": "public", "table-name": "users"}, "rule-action": "add-column", "value": "orgEnvId", "expression": "'org-env-id-value'", "data-type": {"type": "string", "length": 255}}, {"rule-type": "transformation", "rule-id": "110", "rule-name": "Remove email column from Users", "rule-action": "remove-column", "rule-target": "column", "object-locator": {"schema-name": "public", "table-name": "users", "column-name": "email"}}, {"rule-type": "transformation", "rule-id": "111", "rule-name": "Remove firstName column from Users", "rule-action": "remove-column", "rule-target": "column", "object-locator": {"schema-name": "public", "table-name": "users", "column-name": "firstName"}}, {"rule-type": "transformation", "rule-id": "112", "rule-name": "Remove lastName column from Users", "rule-action": "remove-column", "rule-target": "column", "object-locator": {"schema-name": "public", "table-name": "users", "column-name": "lastName"}}, {"rule-type": "transformation", "rule-id": "113", "rule-name": "Remove phoneNumber column from Users", "rule-action": "remove-column", "rule-target": "column", "object-locator": {"schema-name": "public", "table-name": "users", "column-name": "phoneNumber"}}, {"rule-type": "transformation", "rule-id": "114", "rule-name": "Remove city column from Users", "rule-action": "remove-column", "rule-target": "column", "object-locator": {"schema-name": "public", "table-name": "users", "column-name": "city"}}, {"rule-type": "transformation", "rule-id": "115", "rule-name": "Remove postalCode column from Users", "rule-action": "remove-column", "rule-target": "column", "object-locator": {"schema-name": "public", "table-name": "users", "column-name": "postalCode"}}, {"rule-type": "transformation", "rule-id": "116", "rule-name": "Remove streetAddress column from Users", "rule-action": "remove-column", "rule-target": "column", "object-locator": {"schema-name": "public", "table-name": "users", "column-name": "streetAddress"}}, {"rule-type": "transformation", "rule-id": "117", "rule-name": "Remove country column from Users", "rule-action": "remove-column", "rule-target": "column", "object-locator": {"schema-name": "public", "table-name": "users", "column-name": "country"}}, {"rule-type": "transformation", "rule-id": "118", "rule-name": "Remove parentEmail column from Users", "rule-action": "remove-column", "rule-target": "column", "object-locator": {"schema-name": "public", "table-name": "users", "column-name": "parentEmail"}}, {"rule-type": "transformation", "rule-id": "119", "rule-name": "Remove isDeleted column from Users", "rule-action": "remove-column", "rule-target": "column", "object-locator": {"schema-name": "public", "table-name": "users", "column-name": "isDeleted"}}, {"rule-type": "transformation", "rule-id": "120", "rule-name": "Remove ownerAppId column from Users", "rule-action": "remove-column", "rule-target": "column", "object-locator": {"schema-name": "public", "table-name": "users", "column-name": "ownerAppId"}}, {"rule-type": "transformation", "rule-id": "121", "rule-name": "Remove parentVerified column from Users", "rule-action": "remove-column", "rule-target": "column", "object-locator": {"schema-name": "public", "table-name": "users", "column-name": "parentVerified"}}, {"rule-type": "transformation", "rule-id": "122", "rule-name": "Remove parentExpired column from Users", "rule-action": "remove-column", "rule-target": "column", "object-locator": {"schema-name": "public", "table-name": "users", "column-name": "parentExpired"}}, {"rule-type": "transformation", "rule-id": "123", "rule-name": "Remove parentRejected column from Users", "rule-action": "remove-column", "rule-target": "column", "object-locator": {"schema-name": "public", "table-name": "users", "column-name": "parentRejected"}}, {"rule-type": "transformation", "rule-id": "124", "rule-name": "Remove parentIdVerified column from Users", "rule-action": "remove-column", "rule-target": "column", "object-locator": {"schema-name": "public", "table-name": "users", "column-name": "parentIdVerified"}}, {"rule-type": "transformation", "rule-id": "125", "rule-name": "Remove parentDeleted column from Users", "rule-action": "remove-column", "rule-target": "column", "object-locator": {"schema-name": "public", "table-name": "users", "column-name": "parentDeleted"}}, {"rule-type": "transformation", "rule-id": "126", "rule-name": "Remove graduationProcesses column from Users", "rule-action": "remove-column", "rule-target": "column", "object-locator": {"schema-name": "public", "table-name": "users", "column-name": "graduationProcesses"}}]