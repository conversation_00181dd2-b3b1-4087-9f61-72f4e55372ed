import { Inject, Injectable } from '@nestjs/common';
import {
  AGE_GATE_API_CLIENT_INJECT_KEY,
  ageGateApiPlugin,
  AgeGateDTO,
  AgeGateQueryDTO,
} from '@superawesome/freekws-agegate-api-common';
import { NestJsClient } from '@superawesome/freekws-clients-nestjs';
import { Tiso31662 } from '@superawesome/freekws-regional-config';
import { isUndefined, omitBy } from 'lodash';
import { Span } from 'nestjs-ddtrace';

import { IClientCredentials } from '../../../org-env/types';
import { ClientKeycloakService } from '../keycloak/client-keycloak.service';

@Injectable()
@Span()
export class AgeGateService {
  constructor(
    @Inject(AGE_GATE_API_CLIENT_INJECT_KEY)
    private readonly ageGateApiClient: NestJsClient<typeof ageGateApiPlugin>,
    private readonly keycloakService: ClientKeycloakService,
  ) {}

  async getConsentAgeForCountry(params: AgeGateQueryDTO, credentials: IClientCredentials): Promise<AgeGateDTO> {
    const token = await this.keycloakService.getClientToken(credentials);
    const query: AgeGateQueryDTO = {};

    if (params.location) {
      query.location = params.location.toUpperCase() as Tiso31662;
    } else {
      query.ip = params.ip;
    }

    if (params.age) {
      query.age = params.age;
    } else {
      query.dob = params.dob;
    }

    const {
      data: { response },
    } = await this.ageGateApiClient
      .getModule('age')
      .getAgeOfDigitalConsent({ query: omitBy(query, isUndefined) }, { headers: { Authorization: `Bearer ${token}` } });

    return response;
  }
}
