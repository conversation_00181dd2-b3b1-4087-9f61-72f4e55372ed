import { INestApplication } from '@nestjs/common';
import request from 'supertest';

export async function makeRequest(
  app: INestApplication,
  method: 'get' | 'post' | 'put' | 'delete' | 'patch',
  url: string,
  options: {
    headers?: Record<string, string>;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    body?: any;
    query?: Record<string, unknown>;
    expectedStatus?: number;
  } = {},
) {
  const { headers = {}, body, expectedStatus = 200, query } = options;

  const req = request(app.getHttpServer())[method](url);

  for (const [key, value] of Object.entries(headers)) {
    req.set(key, value);
  }

  if (query) {
    req.query(query);
  }

  if (['post', 'put', 'patch'].includes(method) && body) {
    req.send(body);
  }

  const response = await req;

  if (response.status !== expectedStatus) {
    console.error(`
    ===== REQUEST ERROR =====
    URL: ${method.toUpperCase()} ${url}
    Headers: ${JSON.stringify(headers, null, 2)}
    Body sent: ${JSON.stringify(body, null, 2)}
    Response body: ${JSON.stringify(response.body, null, 2)}
    Response text: ${response.text}
    =========================
    `);
    expect(response.status).toBe(expectedStatus);
  }

  return response;
}
