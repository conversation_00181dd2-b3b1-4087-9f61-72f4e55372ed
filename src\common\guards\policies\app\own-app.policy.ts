import { ForbiddenException } from '@nestjs/common';

import { OAuthFastifyRequest } from '../../oauth/types';
import { IPolicyHandler } from '../types';

export class IsOwnApp implements IPolicyHandler {
  async handle(request: OAuthFastifyRequest): Promise<boolean> {
    if (!request.raw.jwt) {
      return false;
    }

    if (request.params.appId !== `${request.raw.jwt.appId}`) {
      throw new ForbiddenException('operation not supported for this client');
    }

    return true;
  }
}
