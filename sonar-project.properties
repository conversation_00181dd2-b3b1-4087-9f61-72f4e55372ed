sonar.projectKey=SuperAwesomeLTD_${env.CIRCLE_PROJECT_REPONAME}
sonar.organization=kws

# This is the name and version displayed in the SonarCloud UI.
sonar.projectName=${env.CIRCLE_PROJECT_REPONAME}
sonar.projectVersion=${env.CIRCLE_SHA1}

# Path is relative to the sonar-project.properties file.
sonar.sources=src
sonar.javascript.lcov.reportPaths=coverage/lcov.info

# Other configuration
sonar.pullrequest.github.summary_comment=false
sonar.exclusions=test/**,**/*.spec.ts,**/*.spec.js,**/fixtures/*.ts,**/fixtures/*.js,**/fixtures.ts,**/fixtures.js,**/*.provider.ts,*.yml
sonar.coverage.exclusions=test/**,**/*.spec.ts,**/*.spec.js,**/fixtures/*.ts,**/fixtures/*.js,**/*.module.ts,**/fixtures.ts,**/fixtures.js,**/*.provider.ts,**/*.entity.ts,src/main.ts,src/jobs/run-job.ts,**helm/*.yaml,test-acceptance/**,*.yml
sonar.cpd.exclusions=**/*.test-utils.ts,test-acceptance/*.ts
