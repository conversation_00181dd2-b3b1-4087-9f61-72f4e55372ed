import { createParamDecorator, ExecutionContext } from '@nestjs/common';

import { UnauthenticatedFastifyRequest } from './types';
import { OrgEnv } from '../../../org-env/org-env.entity';

export const ExtractOrgEnv = createParamDecorator((_data: void, ctx: ExecutionContext): OrgEnv | undefined => {
  const request = ctx.switchToHttp().getRequest<UnauthenticatedFastifyRequest>();
  return request.raw.orgEnv;
});
