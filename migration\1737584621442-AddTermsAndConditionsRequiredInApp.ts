import { MigrationInterface, QueryRunner } from 'typeorm';
export class AddTermsAndConditionsRequiredInApp1737584621442 implements MigrationInterface {
  name = 'AddTermsAndConditionsRequiredInApp1737584621442';
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "app"
            ADD "termsAndConditionsRequired" boolean DEFAULT false
        `);
  }
  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "app" DROP COLUMN "termsAndConditionsRequired"
        `);
  }
}
