function verificationsResponseBuilder(config) {
  const { orgId } = config.request.query;

  return {
    statusCode: 200,
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      response: {
        emialHash: 'hash',
        verifications: [{
          emialHash: 'hash',
          verificationMethod: 'stripe',
          userContext: 'parent',
          verificationDate: new Date('2024-10-10'),
          createdAt: new Date('2024-10-10'),
          expiresAt: new Date(Date.now() + 10_000),
          userLocation: 'US',
          verificationLocation: 'US',
          verificationType: 'payment-card',
          verificationStandard: 'basic',
          truncatedDateOfBirth: new Date('1990-01-01'),
          minimumAge: 16,
          orgId: orgId,
        }],
        consents: [],
      },
    }),
  };
}
