{"compilerOptions": {"module": "commonjs", "moduleResolution": "node", "declaration": true, "declarationDir": "dist", "esModuleInterop": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2022", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strictNullChecks": true, "noImplicitAny": true, "strictBindCallApply": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "types": ["jest", "node"], "lib": ["ES2022"]}, "references": [{"path": "packages/common"}], "include": ["src/**/*.ts", "golden-master/**/*.ts", "test/**/*.ts", "migration/**/*.ts", "test-acceptance/**/*.ts", "ormconfig.ts"], "watchOptions": {"excludeFiles": ["golden-master/**/*"]}}