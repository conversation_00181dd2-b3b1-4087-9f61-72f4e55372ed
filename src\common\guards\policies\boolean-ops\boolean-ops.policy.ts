// This policy allows you to group multiple policies in an OR construct.

import { FastifyRequest } from 'fastify';

import { IPolicyHandler } from '../types';

class PolicyFailure extends Error {
  constructor() {
    super('Sub-policy failure');
  }
}

// It will allow access if any of the passed policies grants access.
export class Or implements IPolicyHandler {
  private readonly policies;
  constructor(...policies: IPolicyHandler[]) {
    this.policies = policies;
  }
  async handle(request: FastifyRequest): Promise<boolean> {
    try {
      // Resolve when first policy resolves - reject if all policies reject
      return await Promise.any(
        this.policies.map(async (policy) => {
          const result = await policy.handle(request);
          if (result) {
            return true;
          }
          throw new PolicyFailure();
        }),
      );
    } catch (error) {
      // if noone policy was satisfied
      if (error instanceof PolicyFailure) {
        return false;
      }
      // if all policies rejected with specific errors we'll throw the last of them
      if (error instanceof AggregateError) {
        throw error.errors.at(-1);
      }
      throw error;
    }
  }
}

// This policy allows you to group multiple policies in an AND construct.
// It will allow access if all the policies grant access.
export class And implements IPolicyHandler {
  private readonly policies;
  constructor(...policies: IPolicyHandler[]) {
    this.policies = policies;
  }
  async handle(request: FastifyRequest): Promise<boolean> {
    // Fail when first policy fails - resolve if all policies resolve
    for (const policy of this.policies) {
      const result = await policy.handle(request);
      if (!result) {
        return false;
      }
    }
    return true;
  }
}

// This policy allows you to negate a policy.
// It will allow access the passed policy does not
export class Not implements IPolicyHandler {
  constructor(private readonly policy: IPolicyHandler) {}

  async handle(request: FastifyRequest): Promise<boolean> {
    const result = await this.policy.handle(request);
    return !result;
  }
}
