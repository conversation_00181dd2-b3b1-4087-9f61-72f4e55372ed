import { INestApplication } from '@nestjs/common';
import { getDataSourceToken } from '@nestjs/typeorm';
import { randomUUID } from 'crypto';
import { DataSource } from 'typeorm';

import { OrgEnv } from '../../src/org-env/org-env.entity';
import { User } from '../../src/user/user.entity';
import { Utils } from '../utils';

describe('User entity (e2e)', () => {
  let app: INestApplication;
  let dataSource: DataSource;

  beforeAll(async () => {
    app = await Utils.createTestServer();
    dataSource = app.get<DataSource>(getDataSourceToken());
  });

  jest.setTimeout(50000);

  beforeEach(async () => {
    await Utils.cleanDb();
  });

  afterAll(async () => {
    await Utils.stopTestServer(app);
    jest.clearAllMocks();
  });

  describe('Insert new user', () => {
    it('should assign a pseudorandom user id', async () => {
      const orgEnv = await dataSource.getRepository(OrgEnv).save({
        id: randomUUID(),
        clientId: randomUUID(),
        clientSecret: 'verysecret',
        orgId: randomUUID(),
        host: 'test.com',
      });
      const user = await dataSource.getRepository(User).save({ orgEnv });

      expect(user.id).toBeGreaterThan(1000000000); // all our pseudorandom ids are higher than this
      expect(user.id).toBeLessThan(2000000000000); // all our pseudorandom ids are lower than this
    });
  });
});
