#!/bin/bash

declare -A packages=(
  ["@superawesome/freekws-agegate-api-common"]="^3.8.2"
  ["@superawesome/freekws-devportal-common"]="^20.2.1"
  ["@superawesome/freekws-family-service-common"]="^5.6.0"
  ["@superawesome/freekws-preverification-service-common"]="^1.5.0"
  ["@superawesome/freekws-service-activation-service-common"]="^2.1.0"
)

echo "Fetching latest versions from npm..."
echo

for package in "${!packages[@]}"; do
  latest=$(npm info "$package" version 2>/dev/null)
  if [ -z "$latest" ]; then
    echo "$package: ❌ Not found or unable to fetch info"
  else
    echo "$package:"
    echo "  Current: ${packages[$package]}"
    echo "  Latest:  $latest"
    echo
  fi
done
