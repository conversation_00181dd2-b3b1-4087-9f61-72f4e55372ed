{
    "port": 4750,
    "protocol": "http",
    "name": "preverification-service",
    "recordRequests": true,
    "defaultResponse": {
      "statusCode": 404,
      "body": {
        "error": {
          "message": "Resource not found"
        }
      },
      "headers": {
        "content-type": "application/json"
      }
    },
    "stubs": [
        {
          "predicates": [ <% include preverification.predicates.ejs %> ],
          "responses": [ <% include preverification.responses.ejs %> ]
        }
    ]
  }
