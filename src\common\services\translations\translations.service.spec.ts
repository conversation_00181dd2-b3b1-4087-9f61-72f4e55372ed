﻿import { ConflictException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { BrandingDTO, TranslationTheme } from '@superawesome/freekws-branding-service-common';

import { TranslationsService } from './translations.service';
import { BrandingService } from '../branding/branding.service';

describe('TranslationsService', () => {
  let translationsService: TranslationsService;

  const orgEnvId = 'org-env-id';
  const mockTranslation: BrandingDTO = {
    language: 'en',
    theme: TranslationTheme.LIGHT,
    iconUrl: 'https://1664530809058-icon',
    bannerUrl: 'https://1667822009045-banner',
    privacyPolicyUrl: 'https://www.epicgames.bo/site/en-US/privacypolicy',
    customerSupportUrl: 'https://www.epicgames.bo/site/en-US/epic-games-store-faq',
    fromEmailAddress: '<EMAIL>',
    font: 'openSans',
    primaryColor: '#298AE8',
    name: 'FortBright',
    parentPortalBackgroundUrl: 'https://cllb220s900004m7683tlxhqi-background',
    parentPortalLogoUrl: 'https://01H7TC39ZYVCYV1R04GKMJMW0Y-logo',
    parentSupportUrl: 'https://www.epicgames.bo/site/en-US/parentSupport',
    environmentId: 'some-env-id',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };
  const multipleTranslationsMock = [
    mockTranslation,
    { ...mockTranslation, language: 'es' },
    { ...mockTranslation, language: 'es-MX' },
  ];

  const brandingServiceMock = {
    getBrandings: jest.fn(),
    getCustomCopy: jest.fn(),
  };

  beforeEach(async () => {
    jest.clearAllMocks();
    jest.resetAllMocks();

    const module: TestingModule = await Test.createTestingModule({
      providers: [TranslationsService, { provide: BrandingService, useValue: brandingServiceMock }],
    }).compile();

    translationsService = module.get(TranslationsService);
  });

  it('should instantiate', () => {
    expect(translationsService).toBeTruthy();
  });

  describe('getTranslationWithBestMatch', () => {
    it('should return the expected data when no language is specified', async () => {
      brandingServiceMock.getBrandings.mockResolvedValueOnce([mockTranslation]);
      const result = await translationsService.getTranslationWithBestMatch(orgEnvId);

      expect(result).toEqual(mockTranslation);
    });

    it('should return the expected data when exact language is specified and exists', async () => {
      brandingServiceMock.getBrandings.mockResolvedValueOnce([mockTranslation]);
      const result = await translationsService.getTranslationWithBestMatch(orgEnvId, mockTranslation.language);

      expect(result).toEqual(mockTranslation);
    });

    it('should return the expected data when exact language is specified and exists amongst multiple langauges', async () => {
      brandingServiceMock.getBrandings.mockResolvedValueOnce(multipleTranslationsMock);
      const result = await translationsService.getTranslationWithBestMatch(orgEnvId, 'es');

      expect(result?.language).toEqual('es');
    });

    it('should return the best match data when no translation for specified language exists', async () => {
      brandingServiceMock.getBrandings.mockResolvedValueOnce(multipleTranslationsMock);
      const result = await translationsService.getTranslationWithBestMatch(orgEnvId, 'fr');

      expect(result?.language).toEqual('en');
    });

    it('should throw when no translations are found for org env', async () => {
      brandingServiceMock.getBrandings.mockResolvedValueOnce([]);
      await expect(translationsService.getTranslationWithBestMatch(orgEnvId, 'en')).rejects.toThrow(
        new ConflictException('No translations found, add translations at organisation level'),
      );
    });
  });
});
