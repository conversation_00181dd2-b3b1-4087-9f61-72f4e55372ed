import { ForbiddenException } from '@nestjs/common';
import { EOAuthScope } from '@superawesome/freekws-classic-wrapper-common';

import { OAuthFastifyRequest } from '../../oauth/types';
import { IPolicyHandler } from '../types';

export class HasScope implements IPolicyHandler {
  private readonly scopes: EOAuthScope[];
  constructor(...scopes: EOAuthScope[]) {
    this.scopes = scopes || [];
  }

  async handle(request: OAuthFastifyRequest): Promise<boolean> {
    if (!request.raw.jwt) {
      return false;
    }
    const scopes = request.raw.jwt.scope.split(',');
    if (this.scopes.some((scope) => scopes.includes(scope))) {
      return true;
    }

    throw new ForbiddenException('operation not supported for this user or client');
  }
}
