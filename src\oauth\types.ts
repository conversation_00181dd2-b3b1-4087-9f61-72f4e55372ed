import { EOAuthScope, ParentState } from '@superawesome/freekws-classic-wrapper-common';
import { JwtPayload } from 'jsonwebtoken';

export type TJwtPayload = JwtClientCredentialsPayload | JwtUserPayload | JwtUserAuthCodePayload;

export type JwtClientCredentialsPayload = {
  clientId: string;
  appId: number;
  scope: EOAuthScope.APP | EOAuthScope.MOBILE_APP;
};

export type JwtUserPayload = {
  userId: number;
  appId: number;
  clientId: string;
  scope: EOAuthScope.USER;
  appPermissions: string[];
  signUpCountry?: string;
  parentState?: ParentState;
  parentVerified?: boolean;
  isMinor: boolean;
};

export type JwtUserAuthCodePayload = {
  userId: number;
  appId?: never;
  clientId: string;
  scope: EOAuthScope.USER;
  codeChallenge?: string;
  codeChallengeMethod?: string;
};

export type TJWT = JwtPayload & TJwtPayload;
