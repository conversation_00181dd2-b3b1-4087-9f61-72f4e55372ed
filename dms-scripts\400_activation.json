[{"rule-type": "selection", "rule-id": "400", "rule-name": "Include activations table", "object-locator": {"schema-name": "public", "table-name": "activations"}, "rule-action": "include", "filters": [{"filter-type": "source", "column-name": "deletedAt", "filter-conditions": [{"filter-operator": "null"}]}]}, {"rule-type": "transformation", "rule-id": "401", "rule-name": "Rename activations to activation", "rule-action": "rename", "rule-target": "table", "object-locator": {"schema-name": "public", "table-name": "activations"}, "value": "activation"}, {"rule-type": "transformation", "rule-id": "402", "rule-name": "Add orgEnvId column to activations", "rule-target": "column", "object-locator": {"schema-name": "public", "table-name": "activations"}, "rule-action": "add-column", "value": "orgEnvId", "expression": "'org-env-id-value'", "data-type": {"type": "string", "length": 255}}, {"rule-type": "transformation", "rule-id": "410", "rule-name": "Remove deletedAt column from activations", "rule-action": "remove-column", "rule-target": "column", "object-locator": {"schema-name": "public", "table-name": "activations", "column-name": "deletedAt"}}, {"rule-type": "transformation", "rule-id": "411", "rule-name": "Remove displayName column from activations", "rule-action": "remove-column", "rule-target": "column", "object-locator": {"schema-name": "public", "table-name": "activations", "column-name": "displayName"}}, {"rule-type": "transformation", "rule-id": "412", "rule-name": "Remove permEmail column from activations", "rule-action": "remove-column", "rule-target": "column", "object-locator": {"schema-name": "public", "table-name": "activations", "column-name": "permEmail"}}, {"rule-type": "transformation", "rule-id": "413", "rule-name": "Remove permFirstName column from activations", "rule-action": "remove-column", "rule-target": "column", "object-locator": {"schema-name": "public", "table-name": "activations", "column-name": "permFirstName"}}, {"rule-type": "transformation", "rule-id": "414", "rule-name": "Remove permLastName column from activations", "rule-action": "remove-column", "rule-target": "column", "object-locator": {"schema-name": "public", "table-name": "activations", "column-name": "permLastName"}}, {"rule-type": "transformation", "rule-id": "415", "rule-name": "Remove permPhoneNumber column from activations", "rule-action": "remove-column", "rule-target": "column", "object-locator": {"schema-name": "public", "table-name": "activations", "column-name": "permPhoneNumber"}}, {"rule-type": "transformation", "rule-id": "416", "rule-name": "Remove permCity column from activations", "rule-action": "remove-column", "rule-target": "column", "object-locator": {"schema-name": "public", "table-name": "activations", "column-name": "permCity"}}, {"rule-type": "transformation", "rule-id": "417", "rule-name": "Remove permPostalCode column from activations", "rule-action": "remove-column", "rule-target": "column", "object-locator": {"schema-name": "public", "table-name": "activations", "column-name": "permPostalCode"}}, {"rule-type": "transformation", "rule-id": "418", "rule-name": "Remove permStreetAddress column from activations", "rule-action": "remove-column", "rule-target": "column", "object-locator": {"schema-name": "public", "table-name": "activations", "column-name": "permStreetAddress"}}, {"rule-type": "transformation", "rule-id": "419", "rule-name": "Remove permCountry column from activations", "rule-action": "remove-column", "rule-target": "column", "object-locator": {"schema-name": "public", "table-name": "activations", "column-name": "permCountry"}}, {"rule-type": "transformation", "rule-id": "420", "rule-name": "Remove permPushNotification column from activations", "rule-action": "remove-column", "rule-target": "column", "object-locator": {"schema-name": "public", "table-name": "activations", "column-name": "permPushNotification"}}, {"rule-type": "transformation", "rule-id": "421", "rule-name": "Remove permNewsletter column from activations", "rule-action": "remove-column", "rule-target": "column", "object-locator": {"schema-name": "public", "table-name": "activations", "column-name": "permNewsletter"}}, {"rule-type": "transformation", "rule-id": "422", "rule-name": "Remove permEnterCompetitions column from activations", "rule-action": "remove-column", "rule-target": "column", "object-locator": {"schema-name": "public", "table-name": "activations", "column-name": "permEnterCompetitions"}}, {"rule-type": "transformation", "rule-id": "423", "rule-name": "Remove permHasAcceptedTermsAndConditions column from activations", "rule-action": "remove-column", "rule-target": "column", "object-locator": {"schema-name": "public", "table-name": "activations", "column-name": "permHasAcceptedTermsAndConditions"}}, {"rule-type": "transformation", "rule-id": "424", "rule-name": "Remove lastActivity column from activations", "rule-action": "remove-column", "rule-target": "column", "object-locator": {"schema-name": "public", "table-name": "activations", "column-name": "lastActivity"}}, {"rule-type": "transformation", "rule-id": "425", "rule-name": "Remove deletionNotifiedAt column from activations", "rule-action": "remove-column", "rule-target": "column", "object-locator": {"schema-name": "public", "table-name": "activations", "column-name": "deletionNotifiedAt"}}]