[{"rule-type": "selection", "rule-id": "600", "rule-name": "Include refreshTokens table", "object-locator": {"schema-name": "public", "table-name": "refreshTokens"}, "rule-action": "include", "filters": [{"filter-type": "source", "column-name": "deletedAt", "filter-conditions": [{"filter-operator": "null"}]}, {"filter-type": "source", "column-name": "expires", "filter-conditions": [{"filter-operator": "gte", "value": "'today-date'"}]}]}, {"rule-type": "transformation", "rule-id": "601", "rule-name": "Rename refreshTokens to refresh_token", "rule-action": "rename", "rule-target": "table", "object-locator": {"schema-name": "public", "table-name": "refreshTokens"}, "value": "refresh_token"}, {"rule-type": "transformation", "rule-id": "602", "rule-name": "Add orgEnvId column to refreshTokens", "rule-target": "column", "object-locator": {"schema-name": "public", "table-name": "refreshTokens"}, "rule-action": "add-column", "value": "orgEnvId", "expression": "'org-env-id-value'", "data-type": {"type": "string", "length": 255}}, {"rule-type": "transformation", "rule-id": "610", "rule-name": "Remove deletedAt column from refreshTokens", "rule-action": "remove-column", "rule-target": "column", "object-locator": {"schema-name": "public", "table-name": "refreshTokens", "column-name": "deletedAt"}}]