import { MigrationInterface, QueryRunner } from 'typeorm';

export class RemoveUniqueIndexToActivations1741500800001 implements MigrationInterface {
    name = 'RemoveUniqueIndexToActivations1741500800001';

    // Stops the migration from being wrapped in a transaction, as adding indexes concurrently is
    // not supported in transactions
    // Which TypeORM automatically wraps everything in by default
    transaction = false;

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            DROP INDEX IF EXISTS idx_unique_activation;
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            CREATE UNIQUE INDEX CONCURRENTLY IF NOT EXISTS idx_unique_activation
                ON "activation" ("userId", "appId", "orgEnvId");
        `);
    }
}
