function sendConsentEmailResponseBuilder(request, state, logger) {
  let body;
  try {
    body = JSON.parse(request.body);
  } catch (err) {
    logger.warn(`Can't parse ${request.body}`);
    logger.error(err);
    throw err;
  }
  const { parentEmail, settings = [] } = body;

  if (!parentEmail) {
    return {
      statusCode: 400,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        error: {
          freekwsErrorCode: 'consent_request_missing_parent_email'
        }
      })
    }
  }

  return {
    statusCode: 200,
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      response: {
        consentRequestId: `mocked-request-id-${Math.round(Math.random() * 1000)}`,
        consents: [{ consentId: 'new-consent-id', consentName: 'consent-name' }],
        settings: settings.filter(({namespace, settingName}) => !(namespace === 'default' && settingName === 'unknown'))
          .map(({namespace, settingName}) => ({
            namespace,
            settingName,
            preferredValue: true,
            effectiveValue: namespace === 'default',
          })),
      }
    })
  }
}