import { ServiceUnavailableException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';

import { HealthcheckController } from './healthcheck.controller';
import { HealthcheckService } from './healthcheck.service';

describe('HealthcheckController', () => {
  let controller: HealthcheckController;

  const healthcheckServiceMock = {
    isHealthy: jest.fn(),
  };

  beforeEach(async () => {
    jest.resetAllMocks();

    const module: TestingModule = await Test.createTestingModule({
      controllers: [HealthcheckController],
      providers: [{ provide: HealthcheckService, useValue: healthcheckServiceMock }],
    }).compile();

    controller = module.get(HealthcheckController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('get', () => {
    it('should return success if the service is healthy', async () => {
      healthcheckServiceMock.isHealthy.mockReturnValueOnce(true);
      const result = await controller.get();
      expect(result).toEqual({
        name: 'Success',
        message: 'What a day to be alive!',
      });
    });

    it('should throw an exception if the service is not healthy', async () => {
      healthcheckServiceMock.isHealthy.mockReturnValueOnce(false);
      await expect(controller.get()).rejects.toThrow(ServiceUnavailableException);
    });
  });
});
