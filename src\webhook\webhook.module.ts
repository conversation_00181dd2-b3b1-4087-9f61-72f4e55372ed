import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { WebhookController } from './webhook.controller';
import { Webhook } from './webhook.entity';
import { WebhookService } from './webhook.service';
import { CommonModule } from '../common/common.module';
import { KafkaProducerModule } from '../common/services/kafka/kafka-producer.module';
import { KwsSignatureModule } from '../common/services/kws-signature/kws-signature.module';
import { SettingsModule } from '../common/services/settings/settings.module';
import { OrgEnv } from '../org-env/org-env.entity';
import { OrgEnvModule } from '../org-env/org-env.module';
import { User } from '../user/user.entity';

@Module({
  imports: [
    KafkaProducerModule,
    TypeOrmModule.forFeature([Webhook, User, OrgEnv]),
    CommonModule,
    KwsSignatureModule,
    OrgEnvModule,
    SettingsModule,
  ],
  providers: [WebhookService],
  exports: [WebhookService],
  controllers: [WebhookController],
})
export class WebhookModule {}
