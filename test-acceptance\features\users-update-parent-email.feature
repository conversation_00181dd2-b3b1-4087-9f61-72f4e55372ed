# feaetures/users-update-parent-email.feature
@AppUsers
Feature: Update parent email
  Scenario: Developer updates parent email for user
    Given app with credentials
      | appId    | 1852416263                           |
      | clientId | b3e00544-5b64-48ab-a7a9-48e1a1dfa062 |
      | secret   | top-secret                           |
    When developer requests oauth token
    When developer requests update parent email for user '<userId>' to '<newParentEmail>'
    Then the parent email is updated
    Examples:
    |   userId   |   newParentEmail    |
    | 1852416260 | <EMAIL> |