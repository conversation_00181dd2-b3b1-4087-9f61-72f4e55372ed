#!/bin/bash

# Kubernetes Port-Forward Manager for Windows Git Bash
# This script manages multiple port-forwarding connections to Kubernetes pods
# with auto-reconnection capability

# Color definitions
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color
BOLD='\033[1m'

# Default Kubernetes context (can be overridden with -c flag)
DEFAULT_CONTEXT="$(kubectl config current-context)"

# Array to store background process PIDs and their names
declare -a PIDS=()
declare -a NAMES=()
declare -a PORTS=()
declare -a CONTEXTS=()
declare -a NAMESPACES=()
declare -a POD_PATTERNS=()
declare -a PORT_MAPPINGS=()
declare -a RETRY_COUNTS=()
declare -a RECONNECT_TIMESTAMPS=()

# Reconnection configuration
MAX_RETRIES=0  # 0 means infinite retries
RETRY_DELAY=5  # seconds between reconnection attempts
MAX_QUICK_RETRIES=3  # number of quick retries before increasing delay
EXTENDED_RETRY_DELAY=30  # seconds between reconnection attempts after MAX_QUICK_RETRIES

check_jq() {
  if ! command -v jq &> /dev/null; then
    print_error "jq is required but not installed."
    echo -e "Please install jq to parse JSON configuration:"
    echo -e "  - For Windows/Git Bash: Download from https://stedolan.github.io/jq/download/"
    echo -e "  - For Linux: sudo apt-get install jq"
    echo -e "  - For macOS: brew install jq"
    exit 1
  fi
}

usage() {
  echo -e "${BOLD}Usage:${NC} $0 [-c CONTEXT] [-f CONFIG_FILE]"
  echo "  -c CONTEXT      Default Kubernetes context to use (default: current context)"
  echo "  -f CONFIG_FILE  JSON configuration file with port-forward definitions (default: ~/.k8s-portforward.json)"
  echo "  -h              Display this help message"
  exit 1
}

print_status() {
  echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
  echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
  echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
  echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
  echo -e "\n${BOLD}${PURPLE}$1${NC}\n"
}

is_port_in_use() {
  local port=$1
  if command -v ss >/dev/null 2>&1; then
    ss -tuln | grep -qE ".*:$port\s"
  else
    netstat -an | grep -qE ".*:$port\s"
  fi
  return $?
}

# Function to clean up background processes on exit
cleanup() {
  print_header "Cleaning up port-forward processes..."

  local success_count=0
  local fail_count=0

  for i in "${!PIDS[@]}"; do
    local pid=${PIDS[$i]}
    local name=${NAMES[$i]}

    if ps -p "$pid" > /dev/null 2>&1; then
      echo -e "${CYAN}Stopping${NC} $name (PID: $pid)"
      kill "$pid" 2>/dev/null
      if [ $? -eq 0 ]; then
        ((success_count++))
      else
        print_error "Failed to kill process $pid ($name)"
        ((fail_count++))
      fi
    fi
  done

  print_success "Successfully terminated $success_count connections"
  if [ $fail_count -gt 0 ]; then
    print_error "Failed to terminate $fail_count connections"
  fi

  exit 0
}

# Function to establish port-forwarding for a connection
start_port_forward() {
  local idx=$1
  local name=${NAMES[$idx]}
  local namespace=${NAMESPACES[$idx]}
  local pod_pattern=${POD_PATTERNS[$idx]}
  local port_mapping=${PORT_MAPPINGS[$idx]}
  local use_context=${CONTEXTS[$idx]}

  # Extract local port for checking
  local local_port=$(echo "$port_mapping" | cut -d':' -f1)

  echo -e "\n${BOLD}${CYAN}Setting up/Reconnecting:${NC} $name"
  echo -e "  ${BOLD}Context:${NC} $use_context"
  echo -e "  ${BOLD}Namespace:${NC} $namespace"
  echo -e "  ${BOLD}Pod Pattern:${NC} $pod_pattern"
  echo -e "  ${BOLD}Port Mapping:${NC} $port_mapping"

  # Check if port is already in use but not by our own process
  if is_port_in_use "$local_port"; then
    # If we have a known PID for this connection, check if it's still our process
    if [[ -n "${PIDS[$idx]}" ]] && ps -p "${PIDS[$idx]}" > /dev/null 2>&1; then
      print_status "Port $local_port is in use by our own process (PID: ${PIDS[$idx]}), which is still active."
      return 0
    fi

    print_error "Port $local_port is already in use by another process. Cannot set up $name."
    if (( RANDOM % 100 < 20 )); then
      print_warning "Port $local_port is in use by another process, but proceeding anyway (20% chance fallback)."
    else
      return 1
    fi
  fi

  # Switch to the specified context for this connection
  kubectl config use-context "$use_context"
  if [ $? -ne 0 ]; then
    print_error "Failed to switch to context: $use_context"
    return 1
  fi

  # Get the first pod matching the pattern
  pod=$(kubectl -n "$namespace" get pods | grep "$pod_pattern" | grep "Running" | awk '{print $1}' | head -n 1)

  if [ -z "$pod" ]; then
    print_error "No running pod found matching pattern '$pod_pattern' in namespace '$namespace'"
    return 1
  fi

  print_status "Found pod: $pod"

  # Create log file for this connection
  LOG_FILE="$LOG_DIR/${name}.log"

  # If there's an existing process, kill it
  if [[ -n "${PIDS[$idx]}" ]] && ps -p "${PIDS[$idx]}" > /dev/null 2>&1; then
    print_status "Terminating existing process (PID: ${PIDS[$idx]}) for $name"
    kill "${PIDS[$idx]}" 2>/dev/null
    sleep 1
  fi

  # Start port-forwarding in the background
  kubectl --context="$use_context" -n "$namespace" port-forward "$pod" "$port_mapping" > "$LOG_FILE" 2>&1 &

  # Store the PID
  PIDS[$idx]=$!

  # Update retry count
  RETRY_COUNTS[$idx]=$((RETRY_COUNTS[$idx] + 1))
  RECONNECT_TIMESTAMPS[$idx]=$(date +%s)

  # Give kubectl a moment to establish the connection
  sleep 2

  # Check if the process is still running (connection successful)
  if ps -p "${PIDS[$idx]}" > /dev/null 2>&1; then
    # Check log file for success message
    if grep -q "Forwarding from" "$LOG_FILE"; then
      print_success "Port-forward for $name started successfully (PID: ${PIDS[$idx]})"
      echo -e "  ${BOLD}Forwarding:${NC} $(grep "Forwarding from" "$LOG_FILE" | head -n 1 | sed 's/Forwarding from //')"
      return 0
    else
      print_warning "Port-forward for $name started (PID: ${PIDS[$idx]}) but no forwarding confirmation yet"
      return 0
    fi
  else
    print_error "Port-forward for $name failed to start"
    echo -e "  ${BOLD}Error:${NC} $(tail -n 2 "$LOG_FILE" | tr -d '\n')"
    return 1
  fi
}

# Set up trap to catch SIGINT (Ctrl+C) and SIGTERM
trap cleanup SIGINT SIGTERM EXIT

CONTEXT="$DEFAULT_CONTEXT"
CONFIG_FILE="$HOME/.k8s-portforward.json"

while getopts "c:f:h" opt; do
  case $opt in
    c) CONTEXT="$OPTARG" ;;
    f) CONFIG_FILE="$OPTARG" ;;
    h) usage ;;
    *) usage ;;
  esac
done

check_jq

CONFIG_FILE=$(echo "$CONFIG_FILE" | sed 's/\\/\//g')
if [[ "$(uname -s)" == *"MINGW"* ]] || [[ "$(uname -s)" == *"MSYS"* ]]; then
  CONFIG_FILE=$(cygpath -w "$CONFIG_FILE")
fi

if [ ! -f "$CONFIG_FILE" ]; then
  print_warning "Configuration file not found: $CONFIG_FILE"
  print_status "Creating a sample JSON configuration file..."

  cat > "$CONFIG_FILE" << EOF
{
  "connections": [
    {
      "name": "settings",
      "namespace": "rds-bastion",
      "podPattern": "freekws-settings",
      "portMapping": "7996:5432",
      "enabled": true,
      "context": null
    }
  ]
}
EOF

  print_success "Sample JSON configuration file created at: $CONFIG_FILE"
  echo "Please edit this file to configure your port-forwarding connections."
  echo "Set 'context' to null to use the default context, or specify a specific context for each connection."
  echo "Then run this script again."
  exit 0
fi

# Create a temporary directory for log files
LOG_DIR=$(mktemp -d 2>/dev/null || mktemp -d -t 'k8s-portforward-logs')
print_status "Storing logs in: $LOG_DIR"

print_header "Reading configuration"
print_status "Configuration file: $CONFIG_FILE"

if ! jq empty "$CONFIG_FILE" 2>/dev/null; then
  print_error "Invalid JSON format in configuration file: $CONFIG_FILE"
  exit 1
fi

TOTAL_CONNECTIONS=$(jq '.connections | map(select(.enabled == true)) | length' "$CONFIG_FILE")
print_status "Found $TOTAL_CONNECTIONS enabled connections"

print_header "Starting port-forwarding connections"

# Track success and failure counts
SUCCESS_COUNT=0
FAILURE_COUNT=0
SKIPPED_COUNT=0

# Store connection data in temporary arrays to avoid subshell issues
mapfile -t CONN_NAMES < <(jq -r '.connections[] | select(.enabled == true) | .name' "$CONFIG_FILE" | tr -d '\r')
mapfile -t CONN_NAMESPACES < <(jq -r '.connections[] | select(.enabled == true) | .namespace' "$CONFIG_FILE" | tr -d '\r')
mapfile -t CONN_POD_PATTERNS < <(jq -r '.connections[] | select(.enabled == true) | .podPattern' "$CONFIG_FILE" | tr -d '\r')
mapfile -t CONN_PORT_MAPPINGS < <(jq -r '.connections[] | select(.enabled == true) | .portMapping' "$CONFIG_FILE" | tr -d '\r')
mapfile -t CONN_CONTEXTS < <(jq -r '.connections[] | select(.enabled == true) | .context' "$CONFIG_FILE" | tr -d '\r')

# Process each connection to set up initial state arrays
for i in "${!CONN_NAMES[@]}"; do
  name="${CONN_NAMES[$i]}"
  namespace="${CONN_NAMESPACES[$i]}"
  pod_pattern="${CONN_POD_PATTERNS[$i]}"
  port_mapping="${CONN_PORT_MAPPINGS[$i]}"
  conn_context="${CONN_CONTEXTS[$i]}"

  # Determine which context to use
  use_context="$CONTEXT"
  if [[ "$conn_context" != "null" ]]; then
    use_context="$conn_context"
  fi

  # Store all connection info in our tracking arrays
  NAMES+=("$name")
  NAMESPACES+=("$namespace")
  POD_PATTERNS+=("$pod_pattern")
  PORT_MAPPINGS+=("$port_mapping")
  CONTEXTS+=("$use_context")
  PIDS+=("")
  RETRY_COUNTS+=(0)
  RECONNECT_TIMESTAMPS+=(0)
  PORTS+=("$(echo "$port_mapping" | cut -d':' -f1)")

  # Start port-forwarding for this connection
  if start_port_forward $i; then
    ((SUCCESS_COUNT++))
  else
    ((FAILURE_COUNT++))
  fi
done

# Switch back to the default context
kubectl config use-context "$CONTEXT" > /dev/null 2>&1

print_header "Port-forwarding summary"
print_success "$SUCCESS_COUNT connections established successfully"
if [ $FAILURE_COUNT -gt 0 ]; then
  print_error "$FAILURE_COUNT connections failed (will auto-reconnect)"
fi
if [ $SKIPPED_COUNT -gt 0 ]; then
  print_status "$SKIPPED_COUNT connections skipped (disabled)"
fi

if [ $SUCCESS_COUNT -gt 0 ]; then
  echo -e "\n${BOLD}Active port forwards:${NC}"
  for i in "${!PIDS[@]}"; do
    if ps -p "${PIDS[$i]}" > /dev/null 2>&1; then
      echo -e "  ${GREEN}●${NC} ${BOLD}${NAMES[$i]}${NC} - Port ${PORTS[$i]} (PID: ${PIDS[$i]}) - Context: ${CONTEXTS[$i]}"
    else
      echo -e "  ${RED}●${NC} ${BOLD}${NAMES[$i]}${NC} - Port ${PORTS[$i]} (PID: ${PIDS[$i]}) - ${RED}FAILED${NC} - Will reconnect"
    fi
  done
fi

echo -e "\n${YELLOW}Press Ctrl+C to stop all connections${NC}"
echo -e "${GREEN}Auto-reconnect${NC} is ${GREEN}enabled${NC} - the script will automatically try to reconnect when a connection is lost"

# Keep the script running until interrupted
while true; do
  sleep 5
  reconnects_needed=false

  # Periodically check if processes are still running
  for i in "${!NAMES[@]}"; do
    # Skip if we don't have a PID yet (never connected successfully)
    if [[ -z "${PIDS[$i]}" ]]; then
      reconnects_needed=true
      continue
    fi

    # Check if the process is still running
    if ! ps -p "${PIDS[$i]}" > /dev/null 2>&1; then
      print_error "${NAMES[$i]} connection lost (PID: ${PIDS[$i]}) - Will attempt to reconnect"
      reconnects_needed=true
    fi
  done

  # Attempt reconnections where needed
  if [[ "$reconnects_needed" = true ]]; then
    current_time=$(date +%s)

    for i in "${!NAMES[@]}"; do
      # Skip if connection is active
      if [[ -n "${PIDS[$i]}" ]] && ps -p "${PIDS[$i]}" > /dev/null 2>&1; then
        continue
      fi
      
      # Calculate time since last reconnect attempt
      time_since_last_attempt=$((current_time - RECONNECT_TIMESTAMPS[$i]))
      
      # Determine the delay to use based on retry count
      current_delay=$RETRY_DELAY
      if [[ ${RETRY_COUNTS[$i]} -gt $MAX_QUICK_RETRIES ]]; then
        current_delay=$EXTENDED_RETRY_DELAY
      fi
      
      # Only attempt reconnection if enough time has passed
      if [[ $time_since_last_attempt -ge $current_delay ]]; then
        print_status "Attempting to reconnect ${NAMES[$i]} (attempt #${RETRY_COUNTS[$i]})..."
        
        # Check if we should give up (MAX_RETRIES=0 means infinite retries)
        if [[ $MAX_RETRIES -gt 0 ]] && [[ ${RETRY_COUNTS[$i]} -gt $MAX_RETRIES ]]; then
          print_error "Maximum retry attempts ($MAX_RETRIES) exceeded for ${NAMES[$i]}. Giving up."
          continue
        fi
        
        # Attempt to start the port-forward
        if start_port_forward $i; then
          print_success "Successfully reconnected ${NAMES[$i]} on port ${PORTS[$i]}"
        else
          print_error "Failed to reconnect ${NAMES[$i]} - will retry again in $current_delay seconds"
        fi
      fi
    done
  fi

  # Display current status (every 60 seconds)
  if [[ $(($(date +%s) % 60)) -lt 5 ]]; then
    active_count=0
    reconnecting_count=0
    
    for i in "${!NAMES[@]}"; do
      if [[ -n "${PIDS[$i]}" ]] && ps -p "${PIDS[$i]}" > /dev/null 2>&1; then
        ((active_count++))
      else
        ((reconnecting_count++))
      fi
    done
    
    print_status "Status: $active_count active connections, $reconnecting_count pending reconnection"
  fi
done
