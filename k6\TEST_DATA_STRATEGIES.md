# Test Data Management Strategies for K6 Load Testing

This document outlines different approaches for handling test data dependencies in K6 load tests, particularly for endpoints that require fresh data or have state constraints.

## 🎯 **The Problem**

Some endpoints have state dependencies that make load testing challenging:

- **User Activation**: A user can only be activated once per app
- **Unique Constraints**: Usernames, emails must be unique
- **State Changes**: Operations that modify data permanently
- **Resource Limits**: Limited test data available

## 🛠 **Strategy 1: Create On-Demand (Recommended)**

**Best for**: Most scenarios, especially when creation is fast

### Implementation

```javascript
activateUser: (token: string) => {
  // Create fresh user first
  const userId = createFreshUser(token);
  
  if (!userId) {
    return false;
  }

  // Now activate the fresh user
  const payload = { /* activation data */ };
  const response = http.post(`/v1/users/${userId}/apps`, JSON.stringify(payload), { headers });
  
  return check(response, {
    'activate-user status is 200 or 201': (r) => r.status === 200 || r.status === 201,
  });
}
```

### ✅ **Pros**
- Always fresh data
- No pre-setup required
- Scales with load
- Realistic test scenario

### ❌ **Cons**
- Slower (extra API call)
- Higher load on creation endpoints
- May hit rate limits

## 🛠 **Strategy 2: Pre-populate Data Pool**

**Best for**: When creation is slow or expensive

### Implementation

```javascript
// In setup() function
export function setup() {
  const token = getOAuthToken(CLIENT_ID, CLIENT_SECRET);
  
  // Pre-create a pool of users
  const userPool = [];
  for (let i = 0; i < 1000; i++) {
    const userId = createFreshUser(token);
    if (userId) {
      userPool.push(userId);
    }
  }
  
  return { token, userPool };
}

// In test function
activateUser: (token: string, userPool: string[]) => {
  if (userPool.length === 0) {
    console.error('No users available for activation');
    return false;
  }
  
  // Pop a user from the pool (thread-safe in K6)
  const userId = userPool.pop();
  
  // Activate the user
  const response = http.post(`/v1/users/${userId}/apps`, payload, { headers });
  return check(response, { /* checks */ });
}
```

### ✅ **Pros**
- Faster test execution
- Predictable performance
- No creation overhead during test

### ❌ **Cons**
- Limited by pool size
- Complex setup
- May run out of data
- Not realistic (no creation load)

## 🛠 **Strategy 3: Hybrid Approach**

**Best for**: High-load scenarios with fallback

### Implementation

```javascript
let userPool = [];
let poolExhausted = false;

activateUser: (token: string) => {
  let userId;
  
  // Try to use pre-created user first
  if (userPool.length > 0) {
    userId = userPool.pop();
  } else if (!poolExhausted) {
    // Pool empty, create on-demand
    userId = createFreshUser(token);
    if (!userId) {
      poolExhausted = true;
      return false;
    }
  } else {
    return false; // No users available
  }
  
  // Activate the user
  const response = http.post(`/v1/users/${userId}/apps`, payload, { headers });
  return check(response, { /* checks */ });
}
```

### ✅ **Pros**
- Best of both worlds
- Graceful degradation
- Handles high load

### ❌ **Cons**
- More complex logic
- Harder to debug

## 🛠 **Strategy 4: External Data Source**

**Best for**: Large datasets, complex test scenarios

### Implementation

```javascript
import { SharedArray } from 'k6/data';

// Load from CSV/JSON file
const userData = new SharedArray('users', function () {
  return JSON.parse(open('./test-users.json'));
});

let userIndex = 0;

activateUser: (token: string) => {
  // Get next user (with wraparound)
  const user = userData[userIndex % userData.length];
  userIndex++;
  
  const response = http.post(`/v1/users/${user.id}/apps`, payload, { headers });
  return check(response, { /* checks */ });
}
```

### ✅ **Pros**
- Large datasets
- Memory efficient (SharedArray)
- Consistent test data
- Can include complex scenarios

### ❌ **Cons**
- Requires external data management
- May have stale data
- Users might already be activated

## 🛠 **Strategy 5: Database Reset/Cleanup**

**Best for**: Controlled test environments

### Implementation

```javascript
export function setup() {
  // Reset test data via API or direct DB access
  http.post(`${BASE_URL}/test/reset-activations`);
  
  return { token: getOAuthToken() };
}

export function teardown() {
  // Clean up after test
  http.post(`${BASE_URL}/test/cleanup`);
}
```

### ✅ **Pros**
- Clean slate for each test
- Predictable state
- Can reuse same test data

### ❌ **Cons**
- Requires test-specific endpoints
- Not suitable for production-like testing
- May affect other tests

## 📊 **Recommendation Matrix**

| Scenario | Strategy | Reason |
|----------|----------|--------:|
| **Fast creation** | Create On-Demand | Simple, realistic |
| **Slow creation** | Pre-populate Pool | Better performance |
| **High load** | Hybrid | Handles scale |
| **Complex data** | External Source | Rich test scenarios |
| **Test environment** | Database Reset | Clean, predictable |

## 🎯 **Current Implementation**

We've implemented **Strategy 1 (Create On-Demand)** for the `activateUser` endpoint:

```javascript
// Helper function for creating fresh users
function createFreshUser(token: string): string | null {
  // Creates a new user and returns the ID
}

// Activate user with fresh data
activateUser: (token: string) => {
  const userId = createFreshUser(token);
  if (!userId) return false;
  
  // Activate the fresh user
  // ...
}
```

## 🔧 **Best Practices**

### **1. Error Handling**
- Always check if data creation succeeded
- Have fallback strategies
- Log meaningful error messages

### **2. Performance Considerations**
- Monitor creation endpoint performance
- Consider rate limits
- Balance realism vs performance

### **3. Data Cleanup**
- Clean up test data if possible
- Use unique identifiers to avoid conflicts
- Consider data retention policies

### **4. Monitoring**
- Track data creation success rates
- Monitor pool depletion
- Alert on data availability issues

## 🚀 **Next Steps**

1. **Monitor** the current on-demand approach performance
2. **Implement pool strategy** if creation becomes a bottleneck
3. **Add cleanup** endpoints for test data management
4. **Consider external data** for more complex scenarios

The key is to start simple (on-demand) and evolve based on actual performance needs and constraints.
