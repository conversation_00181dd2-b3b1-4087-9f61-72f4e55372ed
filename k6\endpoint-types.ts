export type WebhookPayload<T = unknown> = {
    name: string;
    time: number;
    orgId: string;
    productId: string;
    payload: T;
};

export type SettingsUserGraduatedPayloadDTO = {
    userId: string;
};

export type SettingsEffectiveValuesChangedPayloadDTO = {
    userId: string;
    settings: Array<{
        namespace: string,
        settingName: string;
        effectiveValue: boolean;
        parentLimitedUpdatedAt?: string;
        definition: {
            ageBracket: {
                consentType: string;
            };
        };
    }>;
};

export type FamiliesUserRemovedFromFamilyDTO = {
    userId: string;
};

export type FamiliesUserAddedToFamilyDTO = {
    userId: string;
};

export type FamiliesGuardianRequestExpiredPayloadDto = {
    userId: string;
};

export type FamiliesGroupDeletedPayloadDto = {
    members: Array<{
        userId: string;
        role: 'MANAGER' | 'SUPERVISED';
    }>;
};