﻿import { Modu<PERSON> } from '@nestjs/common';
import { SALogger } from '@superawesome/freekws-common-logger';
import { FreeKWSEmailModule } from '@superawesome/freekws-email-builder';
import { EmailMessageService, EmailQueueMessageModule } from '@superawesome/freekws-queue-messages/email';

import { EmailEnqueueService } from './email-enqueue.service';
import { ConfigModule } from '../config/config.module';
import { KafkaProducerModule } from '../kafka/kafka-producer.module';

const logger = new SALogger();

@Module({
  imports: [ConfigModule, EmailQueueMessageModule, KafkaProducerModule, FreeKWSEmailModule],
  providers: [
    EmailMessageService,
    EmailEnqueueService,
    {
      provide: SALogger,
      useValue: logger,
    },
  ],
  exports: [EmailEnqueueService, EmailMessageService],
})
export class EmailEnqueueModule {}
