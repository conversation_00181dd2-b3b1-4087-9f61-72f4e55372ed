import { TestingModule } from '@nestjs/testing';
import { createClientMock } from '@superawesome/freekws-clients-base/src/test-utils/mock';
import { EUserContext } from '@superawesome/freekws-nestjs-guards';
import {
  PREVERIFICATION_SERVICE_CLIENT_INJECT_KEY,
  preverificationBackendPlugin,
  VerificationDTO,
} from '@superawesome/freekws-preverification-service-common';
import { AxiosError } from 'axios';

import { PreVerificationService } from './pre-verification.service';
import { GetParentVerificationsError } from './types';
import { Testing } from '../../utils';

const mockPreverificationApi = createClientMock(preverificationBackendPlugin, jest.fn);

describe('PreVerificationService', () => {
  let service: PreVerificationService;

  beforeEach(async () => {
    const module: TestingModule = await Testing.createModule({
      providers: [
        PreVerificationService,
        { provide: PREVERIFICATION_SERVICE_CLIENT_INJECT_KEY, useValue: mockPreverificationApi },
      ],
    });

    service = module.get<PreVerificationService>(PreVerificationService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getParentStatus', () => {
    it('sohuld return parent status from response', async () => {
      mockPreverificationApi.getModule('verification').getVerificationStatus.mockResolvedValueOnce({
        data: {
          response: {
            emailHash: '',
            verifications: [
              {
                userContext: EUserContext.Parent,
                expiresAt: undefined,
              } as unknown as VerificationDTO,
            ],
            consents: [],
          },
          meta: {
            timestamp: '',
            requestId: '',
          },
        },
        status: 200,
      });

      await expect(service.getVerificationsByEmail({ email: '<EMAIL>', orgId: '' })).resolves.toEqual([
        {
          userContext: EUserContext.Parent,
          expiresAt: undefined,
        },
      ]);
    });

    it('sohuld return parent status if email not used', async () => {
      mockPreverificationApi.getModule('verification').getVerificationStatus.mockRejectedValueOnce(
        new AxiosError(
          'Forbidden Error',
          '403',
          {},
          {},
          {
            data: {
              error: {
                message: 'The requested endpoint/resource was not found',
              },
              meta: {
                timestamp: '',
                requestId: '',
              },
            },
            status: 404,
            statusText: 'NOT_FOUND',
            config: {},
            headers: {},
          },
        ),
      );

      await expect(service.getVerificationsByEmail({ email: '<EMAIL>', orgId: '' })).resolves.toEqual([]);
    });

    it('sohuld throw not axios error', async () => {
      mockPreverificationApi
        .getModule('verification')
        .getVerificationStatus.mockRejectedValueOnce(new Error('ECONRESET'));

      await expect(service.getVerificationsByEmail({ email: '<EMAIL>', orgId: '' })).rejects.toThrow('ECONRESET');
    });

    it('should throw unhandled service response error', async () => {
      mockPreverificationApi.getModule('verification').getVerificationStatus.mockRejectedValueOnce(
        new AxiosError(
          'Forbidden Error',
          '403',
          {},
          {},
          {
            data: {
              error: {
                message: 'Access denied',
              },
              meta: {
                timestamp: '',
                requestId: '',
              },
            },
            status: 403,
            statusText: 'FORBIDDEN',
            config: {},
            headers: {},
          },
        ),
      );

      await expect(service.getVerificationsByEmail({ email: '<EMAIL>', orgId: '' })).rejects.toThrow(
        GetParentVerificationsError,
      );
    });
  });
});
