import { MigrationInterface, QueryRunner } from 'typeorm';

export class DropParentsFlagsForUsers1734629964008 implements MigrationInterface {
  name = 'DropParentsFlagsForUsers1734629964008';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "user"
            DROP COLUMN "parentVerified",
            DROP COLUMN "parentExpired",
            DROP COLUMN "parentRejected",
            DROP COLUMN "parentIdVerified",
            DROP COLUMN "parentDeleted"
        `);
    await queryRunner.query(`
            ALTER TABLE ONLY "user"
            ALTER COLUMN "language" SET DEFAULT 'en',
            ALTER COLUMN "language" SET NOT NULL
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "user"
            ADD "parentVerified" boolean NOT NULL DEFAULT false,
            ADD "parentExpired" boolean NOT NULL DEFAULT false,
            ADD "parentRejected" boolean NOT NULL DEFAULT false,
            ADD "parentIdVerified" boolean NOT NULL DEFAULT false,
            ADD "parentDeleted" boolean NOT NULL DEFAULT false
        `);
    await queryRunner.query(`
            ALTER TABLE "user"
            ALTER COLUMN "language" DROP NOT NULL,
            ALTER COLUMN "language" DROP DEFAULT
        `);
  }
}
