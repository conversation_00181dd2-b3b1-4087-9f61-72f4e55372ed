import { Module } from '@nestjs/common';
import { HttpServiceModule } from '@superawesome/freekws-http-nestjs-service';
import { MetricsServiceModule } from '@superawesome/freekws-metrics-nestjs-service';

import { BadgerService } from './badger.service';
import { ConfigModule } from '../common/services/config/config.module';

@Module({
  imports: [HttpServiceModule, MetricsServiceModule, ConfigModule],
  providers: [BadgerService],
  exports: [BadgerService],
})
export class BadgerModule {}
