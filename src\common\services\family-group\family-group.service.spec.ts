import { TestingModule } from '@nestjs/testing';
import { createClientMock } from '@superawesome/freekws-clients-base/src/test-utils/mock';
import {
  EMemberRole,
  FAMILY_SERVICE_CLIENT_INJECT_KEY,
  familyServicePlugin,
} from '@superawesome/freekws-family-service-common';

import { FamilyGroupService } from './family-group.service';
import { Testing } from '../../utils';

const mockFamilyApi = createClientMock(familyServicePlugin, jest.fn);

describe('FamilyGroupService', () => {
  let service: FamilyGroupService;

  beforeEach(async () => {
    const module: TestingModule = await Testing.createModule({
      providers: [FamilyGroupService, { provide: FAMILY_SERVICE_CLIENT_INJECT_KEY, useValue: mockFamilyApi }],
    });

    service = module.get<FamilyGroupService>(FamilyGroupService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('isUserManagedByFamily', () => {
    it('should not be managed by parent for empty families list', async () => {
      mockFamilyApi.getModule('familyGroup').listFamilyGroups.mockResolvedValueOnce({
        data: {
          response: {
            familyGroups: [],
          },
          meta: {
            timestamp: '',
            requestId: '',
          },
        },
        status: 200,
      });

      await expect(service.isUserManagedByFamily(1, { clientId: '', secret: '' })).resolves.toEqual(false);
    });

    it('should not be managed by parent if family has not a manager', async () => {
      mockFamilyApi.getModule('familyGroup').listFamilyGroups.mockResolvedValueOnce({
        data: {
          response: {
            familyGroups: [
              {
                id: '1',
                members: [
                  {
                    id: '1',
                    role: EMemberRole.Supervised,
                    userId: '1',
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString(),
                  },
                ],
                allowedToAddManagers: false,
                allowedToAddSupervisedUnsupervised: false,
                allowedToAddSupervisors: false,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
              },
            ],
          },
          meta: {
            timestamp: '',
            requestId: '',
          },
        },
        status: 200,
      });

      await expect(service.isUserManagedByFamily(1, { clientId: '', secret: '' })).resolves.toEqual(false);
    });

    it('should be managed by parent if family has a manager', async () => {
      mockFamilyApi.getModule('familyGroup').listFamilyGroups.mockResolvedValueOnce({
        data: {
          response: {
            familyGroups: [
              {
                id: '1',
                members: [
                  {
                    id: '1',
                    role: EMemberRole.Supervised,
                    userId: '1',
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString(),
                  },
                  {
                    id: '2',
                    role: EMemberRole.Manager,
                    email: '<EMAIL>',
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString(),
                  },
                ],
                allowedToAddManagers: false,
                allowedToAddSupervisedUnsupervised: false,
                allowedToAddSupervisors: false,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
              },
            ],
          },
          meta: {
            timestamp: '',
            requestId: '',
          },
        },
        status: 200,
      });

      await expect(service.isUserManagedByFamily(1, { clientId: '', secret: '' })).resolves.toEqual(true);
    });
  });

  describe('getParentInfo', () => {
    it('should be empty without family group', async () => {
      mockFamilyApi.getModule('familyGroup').listFamilyGroups.mockResolvedValueOnce({
        data: {
          response: {
            familyGroups: [],
          },
          meta: {
            timestamp: '',
            requestId: '',
          },
        },
        status: 200,
      });

      await expect(service.getParentInfo(1, { clientId: '1', secret: 'top secret' })).resolves.toBeUndefined();
    });

    it('should be empty for family without manager', async () => {
      mockFamilyApi.getModule('familyGroup').listFamilyGroups.mockResolvedValueOnce({
        data: {
          response: {
            familyGroups: [
              {
                id: '1',
                members: [],
                allowedToAddManagers: false,
                allowedToAddSupervisors: false,
                allowedToAddSupervisedUnsupervised: false,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
              },
            ],
          },
          meta: {
            timestamp: '',
            requestId: '',
          },
        },
        status: 200,
      });

      await expect(service.getParentInfo(1, { clientId: '1', secret: 'top secret' })).resolves.toBeUndefined();
    });

    it('should return parent from family', async () => {
      mockFamilyApi.getModule('familyGroup').listFamilyGroups.mockResolvedValueOnce({
        data: {
          response: {
            familyGroups: [
              {
                id: '',
                members: [
                  {
                    id: '1',
                    familyGroupId: '1',
                    email: '<EMAIL>',
                    role: EMemberRole.Manager,
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString(),
                  },
                ],
                allowedToAddManagers: false,
                allowedToAddSupervisors: false,
                allowedToAddSupervisedUnsupervised: false,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
              },
            ],
          },
          meta: {
            timestamp: '',
            requestId: '',
          },
        },
        status: 200,
      });

      await expect(service.getParentInfo(1, { clientId: '1', secret: 'top secret' })).resolves.toEqual({
        id: '1',
        familyGroupId: '1',
        email: '<EMAIL>',
      });
    });
  });
});
