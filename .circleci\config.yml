version: 2.1
orbs:
  sonarcloud: sonarsource/sonarcloud@2.0.0
  slack: circleci/slack@4.12.5
  token-gen: superawesomeltd/sa-gha-token-gen-orb@0.0.6
jobs:
  download_tools:
    machine: true
    resource_class: superawesomeltd/machine-runner
    steps:
      - checkout
      - token-gen/generate
      - run:
          name: Pull sa-continuous-integration
          command: git clone https://x-access-token:${GITHUB_TOKEN}@github.com/SuperAwesomeLTD/sa-continuous-integration.git
      - run:
          name: Pull sa-kubernetes-templates
          command: git clone https://x-access-token:${GITHUB_TOKEN}@github.com/SuperAwesomeLTD/sa-kubernetes-templates.git
      - persist_to_workspace:
          root: "."
          paths:
            - "*"
  build_docker:
    machine: true
    resource_class: superawesomeltd/machine-runner
    environment:
      BUILDKIT_PROGRESS: plain
    steps:
      - attach_workspace:
          at: .
      - run:
          name: Docker Auth
          command: sa-continuous-integration/circleci/docker-auth.sh
      - run:
          name: Build Builder Image
          command: sa-continuous-integration/circleci/docker-build.sh builder
      - run:
          name: Build Prod Image
          command: sa-continuous-integration/circleci/docker-build.sh
      - run:
          name: Push Builder Image
          command: sa-continuous-integration/circleci/docker-push.sh builder
      - run:
          name: Push Prod Docker Image
          command: sa-continuous-integration/circleci/docker-push.sh
  build_helm:
    machine: true
    resource_class: superawesomeltd/machine-runner
    steps:
      - attach_workspace:
          at: .
      - run: sa-kubernetes-templates/circleci.sh build
      - persist_to_workspace:
          root: "."
          paths:
            - ci.yaml
  trigger-docs-build:
    machine: true
    resource_class: superawesomeltd/machine-runner
    steps:
      - attach_workspace:
          at: .
      - run:
          name: Docker Auth
          command: sa-continuous-integration/circleci/docker-auth.sh
      - run:
          name: Trigger Docs Build Script
          command: |
            NEW_BRANCH="feat/${CIRCLE_PROJECT_REPONAME}-${CIRCLE_SHA1}" \
            && sa-continuous-integration/circleci/trigger-circle-ci-repo-workflow.sh sa-kws-docs '{"repo": "'$CIRCLE_PROJECT_REPONAME'", "source_branch": "'$CIRCLE_BRANCH'", "new_branch": "'$NEW_BRANCH'", "run_workflow_build": true, "run_workflow_publish": false, "commit_hash": "'$CIRCLE_SHA1'" }'
  sonarcloud:
    docker:
      - image: 'cimg/node:lts'
    steps:
      - attach_workspace:
          at: .
      - sonarcloud/scan
  lint:
    machine: true
    resource_class: superawesomeltd/machine-runner
    steps:
      - attach_workspace:
          at: .
      - run:
          name: Docker Auth
          command: sa-continuous-integration/circleci/docker-auth.sh
      - run:
          name: Run lint
          command: COMPOSE_EXTRA_ARGS=--no-deps ./scripts/docker-npm-run.sh lint
  test:
    machine: true
    resource_class: superawesomeltd/machine-runner
    steps:
      - attach_workspace:
          at: .
      - run:
          name: Docker Auth
          command: sa-continuous-integration/circleci/docker-auth.sh
      - run:
          name: Run tests
          command: COMPOSE_EXTRA_ARGS=--no-deps ./scripts/docker-npm-run.sh test:cov
      - persist_to_workspace:
          root: '.'
          paths:
            - coverage
      - store_test_results:
          path: ./reports/junit/
  test-e2e:
    machine: true
    resource_class: superawesomeltd/machine-runner
    parallelism: 6
    steps:
      - attach_workspace:
          at: .
      - run:
          name: Docker Auth
          command: sa-continuous-integration/circleci/docker-auth.sh
      - run:
          name: Run tests
          command: ./scripts/docker-npm-run.sh test:e2e
      - store_test_results:
          path: ./reports/junit/
  test-acceptance:
    machine: true
    resource_class: superawesomeltd/machine-runner
    parallelism: 4
    steps:
      - attach_workspace:
          at: .
      - run:
          name: Docker Auth
          command: sa-continuous-integration/circleci/docker-auth.sh
      - run:
          name: Run tests
          command: ./scripts/docker-npm-uat-run.sh test:acceptance
      - store_test_results:
          path: ./reports/junit/
      - store_artifacts:
          path: ./reports/cucumber-report.html
  # this will ensure that there are no pending migrations that should have been generated
  test-migration:
    machine: true
    resource_class: superawesomeltd/machine-runner
    steps:
      - attach_workspace:
          at: .
      - run:
          name: Docker Auth
          command: sa-continuous-integration/circleci/docker-auth.sh
      - run:
          name: Run tests
          command: ./scripts/docker-npm-run.sh test:migration
  staging-eu-west-1:
    machine: true
    resource_class: superawesomeltd/machine-runner
    environment:
      CHART: superawesome/freekws-classic-wrapper-backend
      RELEASE: freekws-classic-wrapper-backend
      VARIABLE_KEY: freekws-classic-wrapper-backend-sa-deployment.containers.freekws-classic-wrapper-backend-api.image.tag
      CLUSTER_NAME: 0.eu-west-1.v2.kws.k8s.staging.superawesome.tv
      AWS_ACCOUNT: KWS_V2_STAGING
      USE_HELM_CUSTOM_VALUES_FROM_VCS: true
    steps:
      - attach_workspace:
          at: .
      - run: sa-kubernetes-templates/circleci.sh deploy
  staging-us-east-1:
    machine: true
    resource_class: superawesomeltd/machine-runner
    environment:
      CHART: superawesome/freekws-classic-wrapper-backend
      RELEASE: freekws-classic-wrapper-backend
      VARIABLE_KEY: freekws-classic-wrapper-backend-sa-deployment.containers.freekws-classic-wrapper-backend-api.image.tag
      CLUSTER_NAME: 0.us-east-1.v2.kws.k8s.staging.superawesome.tv
      AWS_ACCOUNT: KWS_V2_STAGING
      USE_HELM_CUSTOM_VALUES_FROM_VCS: true
    steps:
      - attach_workspace:
          at: .
      - run: sa-kubernetes-templates/circleci.sh deploy
  production-eu-west-1:
    machine: true
    resource_class: superawesomeltd/machine-runner
    environment:
      CHART: superawesome/freekws-classic-wrapper-backend
      RELEASE: freekws-classic-wrapper-backend
      VARIABLE_KEY: freekws-classic-wrapper-backend-sa-deployment.containers.freekws-classic-wrapper-backend-api.image.tag
      CLUSTER_NAME: 0.eu-west-1.v2.kws.k8s.superawesome.tv
      AWS_ACCOUNT: KWS_V2_PRODUCTION
      USE_HELM_CUSTOM_VALUES_FROM_VCS: true
    steps:
      - attach_workspace:
          at: .
      - run: sa-kubernetes-templates/circleci.sh deploy
  production-us-east-1:
    machine: true
    resource_class: superawesomeltd/machine-runner
    environment:
      CHART: superawesome/freekws-classic-wrapper-backend
      RELEASE: freekws-classic-wrapper-backend
      VARIABLE_KEY: freekws-classic-wrapper-backend-sa-deployment.containers.freekws-classic-wrapper-backend-api.image.tag
      CLUSTER_NAME: 0.us-east-1.v2.kws.k8s.superawesome.tv
      AWS_ACCOUNT: KWS_V2_PRODUCTION
      USE_HELM_CUSTOM_VALUES_FROM_VCS: true
    steps:
      - attach_workspace:
          at: .
      - run: sa-kubernetes-templates/circleci.sh deploy
  packages_test:
    machine: true
    resource_class: superawesomeltd/machine-runner
    steps:
      - attach_workspace:
          at: .
      - run:
          name: Docker Auth
          command: sa-continuous-integration/circleci/docker-auth.sh
      - run:
          name: Run Packages Test
          command: COMPOSE_FILE=docker-compose.packages.yml ./scripts/docker-npm-run.sh packages:test
  packages_publish_dry_run:
    machine: true
    resource_class: superawesomeltd/machine-runner
    steps:
      - attach_workspace:
          at: .
      - run:
          name: Docker Auth
          command: sa-continuous-integration/circleci/docker-auth.sh
      - run:
          name: Run Packages Publish Dry Run
          command: COMPOSE_FILE=docker-compose.packages.yml ./scripts/docker-semantic-release-run.sh packages:publish:dryrun
  packages_publish:
    machine: true
    resource_class: superawesomeltd/machine-runner
    steps:
      - attach_workspace:
          at: .
      - run:
          name: Docker Auth
          command: sa-continuous-integration/circleci/docker-auth.sh
      - run:
          name: Run Packages Publish
          command: COMPOSE_FILE=docker-compose.packages.yml ./scripts/docker-semantic-release-run.sh packages:publish
  generate_and_publish_docs_staging:
    machine: true
    resource_class: superawesomeltd/machine-runner
    steps:
      - attach_workspace:
          at: .
      - run:
          name: Docker Auth
          command: sa-continuous-integration/circleci/docker-auth.sh
      - run:
          name: Generate swagger docs
          command: |
            AWS_ACCESS_KEY_ID=${KWS_V2_STAGING_AWS_ACCESS_KEY_ID} \
            AWS_SECRET_ACCESS_KEY=${KWS_V2_STAGING_AWS_SECRET_ACCESS_KEY} \
            PUBLISH_S3_BUCKET=freekws-devportal-frontend-kws-v2-staging \
            PUBLISH_S3_PREFIX=openapi/ \
            PUBLISH_S3_REGION=eu-west-1 \
            ./scripts/docker-npm-run.sh docs:generate
  generate_and_publish_docs_production:
    machine: true
    resource_class: superawesomeltd/machine-runner
    steps:
      - attach_workspace:
          at: .
      - run:
          name: Docker Auth
          command: sa-continuous-integration/circleci/docker-auth.sh
      - run:
          name: Generate swagger docs
          command: |
            AWS_ACCESS_KEY_ID=${KWS_V2_PRODUCTION_AWS_ACCESS_KEY_ID} \
            AWS_SECRET_ACCESS_KEY=${KWS_V2_PRODUCTION_AWS_SECRET_ACCESS_KEY} \
            PUBLISH_S3_BUCKET=freekws-devportal-frontend-kws-v2-production \
            PUBLISH_S3_PREFIX=openapi/ \
            PUBLISH_S3_REGION=eu-west-1 \
            ./scripts/docker-npm-run.sh docs:generate
      - run:
          name: Commit if changes
          command: scripts/commit-if-changes.sh
  # can be removed when we have continuous delivery
  force_generate_and_publish_docs_production:
    machine: true
    resource_class: superawesomeltd/machine-runner
    steps:
      - attach_workspace:
          at: .
      - run:
          name: Docker Auth
          command: sa-continuous-integration/circleci/docker-auth.sh
      - run:
          name: Generate swagger docs
          command: |
            AWS_ACCESS_KEY_ID=${KWS_V2_PRODUCTION_AWS_ACCESS_KEY_ID} \
            AWS_SECRET_ACCESS_KEY=${KWS_V2_PRODUCTION_AWS_SECRET_ACCESS_KEY} \
            PUBLISH_S3_BUCKET=freekws-devportal-frontend-kws-v2-production \
            PUBLISH_S3_PREFIX=openapi/ \
            PUBLISH_S3_REGION=eu-west-1 \
            ./scripts/docker-npm-run.sh docs:generate
      - run:
          name: Commit if changes
          command: scripts/commit-if-changes.sh
workflows:
  workflow:
    jobs:
      - download_tools:
          context: Build
      - build_helm:
          context: Build
          requires:
            - download_tools
      - build_docker:
          context: Build
          requires:
            - download_tools
      - lint:
          context: Build
          requires:
            - build_docker
      - test:
          context: Build
          requires:
            - build_docker
      - sonarcloud:
          context: Build
          requires:
            - test
            - test-migration
      - test-e2e:
          context: Build
          requires:
            - build_docker
      - test-acceptance:
          context: Build
          requires:
            - build_docker
      - test-migration:
          context: Build
          requires:
            - build_docker
      - trigger-docs-build:
          context: Build
          requires:
            - test
            - test-e2e
            - test-acceptance
            - lint
          filters:
            branches:
              only:
                - main
      - approve_staging:
          type: approval
          requires:
            - build_helm
            - build_docker
      - staging-eu-west-1:
          context: Build
          requires:
            - approve_staging
      - approve_us-east-1_staging:
          type: approval
          requires:
            - staging-eu-west-1
      - staging-us-east-1:
          context: Build
          requires:
            - approve_us-east-1_staging
            - staging-eu-west-1
      - approve_production:
          type: approval
          requires:
            - test
            - test-e2e
            - test-acceptance
            - test-migration
            - lint
            - sonarcloud
            - staging-us-east-1
            - staging-eu-west-1
          filters:
            branches:
              only:
                - main
      - production-us-east-1:
          context: Build
          requires:
            - approve_production
          filters:
            branches:
              only:
                - main
      - production-eu-west-1:
          context: Build
          requires:
            - production-us-east-1
          filters:
            branches:
              only:
                - main
      - packages_test:
          context: Build
          requires:
            - build_docker
      - packages_publish_dry_run:
          context: Build
          requires:
            - build_docker
      - approve_publish_dry_run:
          type: approval
          requires:
            - packages_test
            - packages_publish_dry_run
          filters:
            branches:
              only:
                - main
      - packages_publish:
          context: Build
          requires:
            - approve_publish_dry_run
          filters:
            branches:
              only:
                - main
      - generate_and_publish_docs_staging:
          context: Build
          requires:
            - staging-eu-west-1
      - approve_force_generate_and_publish_docs:
          type: approval
          requires:
            - trigger-docs-build
          filters:
            branches:
              only:
                - main
      - generate_and_publish_docs_production:
          context: Build
          requires:
            - production-eu-west-1
            - production-us-east-1
          filters:
            branches:
              only:
                - main
      - force_generate_and_publish_docs_production:
          context: Build
          requires:
            - approve_force_generate_and_publish_docs
          filters:
            branches:
              only:
                - main
