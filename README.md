# freekws-classic-wrapper

## Description

FreeKWS Classic Wrapper API (publicly exposed). It proves an API that is backwards compatible with Classic KWS but using CM2 services. It is meant to help in the migration of customers from Classic KWS to FreeKWS

## Guide to work with KWS Backend Repositories

Read
our [General Guide For KWS Backend Repositories](https://superawesomeltd.atlassian.net/wiki/spaces/KWS/pages/4825710705/A+Guide+to+General+KWS+Backend+Repositories)
to learn all about our system's architecture, best practices, deployments, debugging and more.
<br>
Note: Pull Requests to this repository assume that you've read the document above.

## Code structure

The folder structure follows NestJs domain based convention where there is a folder for every resource/entity and that folder will contain its corresponding module that contains a service and a controller (more things like model/repository can be then added too)

There is also a common folder with elements like middlewares, interceptors, adapters, guards, etc.

Shared services are also in a services subfolder inside common. Every service has its own folder and module to be able to import specific services as dependencies into any other module. The common module will contain them all.

```bash
|——common
|  |——middlewares
|  |  |——metrics.middleware.ts
|  |  |——(...)
|  |——guards
|  |  |——(...)
|  |——(...)
|  |——services
|  |  |——config
|  |  |  |——config.module.ts
|  |  |  |——config.service.ts
|  |  |  |——config.service.spec.ts
|  |  |——(...)
|  |——common.module.ts
|——foo (resource)
|  |——foo.module.ts
|  |——foo.controller.ts
|  |——foo.controller.spec.ts
|  |——foo.service.ts
|  |——foo.service.spec.ts
|——bar (resource)
|——(...)
```

## Running npm scripts in docker
docker-compose run uses aws sts assumerole.(refer to /scripts/docker-npm-run.sh)

```bash
  ./scripts/docker-npm-run.sh {scriptToRun}
```
Local debugging
```bash
  ./scripts/docker-npm-run.sh start:debug
```
For example:
- Running server locally with watch
```bash
  ./scripts/docker-npm-run.sh start:dev
```
- Running e2e tests with watch
```bash
  ./scripts/docker-npm-run.sh test:e2e:watch
```

Note that unit tests (`npm run test:watch`) do not need to be run in docker.

## npm scripts reference

### Installation

```bash
$ npm ci
```

### Running the app

```bash
# development
$ npm run start

# watch mode
$ npm run start:dev

# production mode
$ npm run start:prod
```

TIP: if you're running the app locally, and require connectivity to other stacks, then you can use port forwarding to connect to those stacks:

```
$ kubectl port-forward <pod-name> <local-port>:<destination-port>
```

### Test

```bash
# unit tests
$ npm run test

# e2e tests
$ npm run test:e2e

# test cov
$ npm run test:cov

# unit tests watch
$ npm run test:watch

# e2e tests watch
$ npm run test:e2e:watch
```

### Jobs

Jobs can be found in `src/jobs`, they can be run manually like so:

```bash
# run job in dry mode
$ npm run job:dry <job-name>

# run job
$ npm run job <job-name>
```

## Docs generation

There are two npm scripts to generate docs. The best way to run them is with docker like shown below:

### Generate docs to see them in the browser
```bash
  ./scripts/docker-npm-run.sh docs:start
```
After running this, swagger docs can be seen in this URL: http://localhost:8080/api

### Generate doc files for final documentation
```bash
  ./scripts/docker-npm-run.sh docs:generate
```
This will generate the swagger and apiblueprint files that we need for the final documentation. This needs to be run manually at the moment when we do documentation changes, but will eventually be moved to CI

## Packages
* Client wrapper package (`packages/common`) built with https://github.com/SuperAwesomeLTD/freekws-clients
* Packages managed by `NPM Workspaces` - Requires NPM >= 9.4.1
[NPM Workspaces documentation](https://docs.npmjs.com/cli/v8/using-npm/workspaces)

### Running locally
Steps:
1. `npm ci` - Installs all dependencies (including the packages ones) and links dependencies
2. `npm run packages:build` - Run build for all packages
3. `npm run packages:test` - Run tests for all packages

### Semantic Release Publishing to NPM Registry
* `npm run packages:publish`
