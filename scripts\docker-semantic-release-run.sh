#!/bin/bash -ex

export GIT_AUTHOR_NAME=free-kws-release-bot
export GIT_AUTHOR_EMAIL=<EMAIL>
export GIT_COMMITTER_NAME=free-kws-release-bot
export GIT_COMMITTER_EMAIL=<EMAIL>

echo "Getting github app token..."
unset GITHUB_TOKEN
unset GITHUB_USERNAME
curl -L https://github.com/SuperAwesomeLTD/gha-token-generator/releases/download/v1.0.3/gha-token-generator_1.0.3_Linux_x86_64.tar.gz | tar xz
export GITHUB_TOKEN=$( ./gha-token-generator -app-id 150262 -org-name SuperAwesomeLTD -pem-key $SA_RELEASE_BOT_GITHUB_PEM )
export GITHUB_ACTION=true

docker-compose kill
if [ -z "${CIRCLE_SHA1}" ]; then
  export TAG=local
  docker-compose build
else
  export TAG=${CIRCLE_SHA1}-builder
  docker-compose pull
fi

./scripts/docker-compose-volume-perms.sh

docker-compose run -p 3000:80 project npm run "$@"