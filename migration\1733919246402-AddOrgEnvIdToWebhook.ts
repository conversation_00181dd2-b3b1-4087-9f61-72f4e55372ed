import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddOrgEnvIdToWebhook1733919246402 implements MigrationInterface {
  name = 'AddOrgEnvIdToWebhook1733919246402';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "webhook"
            ADD "orgEnvId" character varying
        `);
    await queryRunner.query(`
            ALTER TABLE "webhook"
            ADD CONSTRAINT "FK_7ef492f8898bb08cd663e4fa15e" FOREIGN KEY ("orgEnvId") REFERENCES "org_env"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "webhook" DROP CONSTRAINT "FK_7ef492f8898bb08cd663e4fa15e"
        `);
    await queryRunner.query(`
            ALTER TABLE "webhook" DROP COLUMN "orgEnvId"
        `);
  }
}
