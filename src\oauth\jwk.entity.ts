import type { Algorithm } from 'jsonwebtoken';
import {
  Column,
  CreateDateColumn,
  Entity,
  ManyToOne,
  PrimaryColumn,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

import { OrgEnv } from '../org-env/org-env.entity';

@Entity()
export class JWK {
  @PrimaryGeneratedColumn()
  id: number;

  @PrimaryColumn({ type: 'character varying' })
  orgEnvId: string;

  @ManyToOne(() => OrgEnv, (orgEnv) => orgEnv.jwks, { onDelete: 'CASCADE' })
  orgEnv: OrgEnv;

  @Column()
  algorithm: Algorithm;

  @Column()
  keyType: string;

  @Column()
  use: string;

  @Column()
  certThumbprint: string;

  @Column()
  keyId: string;

  @Column()
  publicPem: string;

  @Column({ nullable: true })
  privatePem: string;

  @Column()
  exponent: string;

  @Column()
  modulus: string;

  @CreateDateColumn()
  createdAt?: Date;

  @UpdateDateColumn()
  updatedAt?: Date;
}
