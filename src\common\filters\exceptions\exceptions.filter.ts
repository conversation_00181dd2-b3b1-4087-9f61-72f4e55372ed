import { ArgumentsHost, BadRequestException, Catch, ExceptionFilter, HttpException, HttpStatus } from '@nestjs/common';
import { HttpAdapterHost } from '@nestjs/core';
import { ERROR_CODES, ErrorResponse } from '@superawesome/freekws-classic-wrapper-common';
import { SALogger } from '@superawesome/freekws-common-logger';
import { obfuscate } from '@superawesome/freekws-nestjs-http-interceptors';
import axios, { AxiosError, AxiosRequestHeaders } from 'axios';
import { ValidationError } from 'class-validator';
import { FastifyRequest } from 'fastify';
import { omit } from 'lodash';
import { EntityNotFoundError, EntityTarget } from 'typeorm';

@Catch()
export class ExceptionsFilter implements ExceptionFilter {
  constructor(private readonly httpAdapterHost: HttpAdapterHost, private readonly logger: SALogger) {}

  catch(exception: <PERSON>rror, host: ArgumentsHost) {
    const context = host.switchToHttp();
    const statusCode = ExceptionsFilter.getStatusCode(exception);
    const errorBody = ExceptionsFilter.getResponseBody(exception, statusCode);

    this.logError(exception, statusCode, context.getRequest());
    this.httpAdapterHost.httpAdapter.reply(context.getResponse(), errorBody, statusCode);
  }

  static getStatusCode(exception: Error | ValidationError): HttpStatus {
    if (exception instanceof HttpException) {
      return exception.getStatus();
    }
    if (exception instanceof ValidationError) {
      return HttpStatus.BAD_REQUEST;
    }
    if (exception instanceof EntityNotFoundError) {
      return HttpStatus.NOT_FOUND;
    }
    return HttpStatus.INTERNAL_SERVER_ERROR;
  }

  static getCodeForValidationError(error: ValidationError): ErrorResponse['code'] {
    if (Object.values(error.constraints || {}).some((constraint) => constraint.includes('be one of'))) {
      return 12;
    }
    if (error.target && (error.target as Record<string, unknown>)[error.property] === undefined) {
      return 6;
    }
    return 5;
  }

  static mapExceptionToErrorCode(
    exception: Error | ValidationError,
    statusCode: HttpStatus,
  ): Pick<ErrorResponse, 'code' | 'codeMeaning'> {
    let code: ErrorResponse['code'];
    switch (statusCode) {
      case HttpStatus.FORBIDDEN: {
        code = 1;
        break;
      }
      case HttpStatus.NOT_FOUND: {
        code = 2;
        break;
      }
      case HttpStatus.BAD_REQUEST: {
        code = exception instanceof ValidationError ? this.getCodeForValidationError(exception) : 3;
        break;
      }
      case HttpStatus.CONFLICT: {
        code = 10;
        break;
      }
      default: {
        code = 11;
      }
    }

    return {
      code,
      codeMeaning: ERROR_CODES[code],
    };
  }

  static transformValidationErrors(errors: ValidationError[], prefix = ''): ErrorResponse['invalid'] {
    return errors.reduce(
      (acc, error: ValidationError) => ({
        ...acc,
        ...(error.children?.length
          ? this.transformValidationErrors(error.children, `${error.property}.`)
          : this.transformValidationError(error, prefix)),
      }),
      {} as ErrorResponse['invalid'],
    );
  }

  private static transformValidationError(error: ValidationError, prefix: string) {
    return Object.values(error.constraints || {}).reduce(
      (acc, constraint) => ({
        ...acc,
        [`${prefix}${error.property}`]: {
          errorMessage: constraint,
          code: this.getCodeForValidationError(error),
          codeMeaning: ERROR_CODES[this.getCodeForValidationError(error)],
        },
      }),
      {} as ErrorResponse['invalid'],
    );
  }

  static getErrorMessage(exception: Error | ValidationError) {
    if (exception instanceof ValidationError) {
      return {
        errorMessage: exception.toString(false, false, undefined, true),
        invalid: this.transformValidationErrors(exception.children || []),
      };
    }
    if (exception instanceof EntityNotFoundError) {
      return {
        errorMessage: `Could not find requested ${this.getEntityName(exception.entityClass)}`,
        invalid: undefined,
      };
    }
    let detail = this.getDetail(exception);
    if (detail === exception.message) {
      detail = undefined;
    }
    return {
      errorMessage: exception.message,
      invalid: undefined,
      detail,
    };
  }

  private static getDetail(exception: Error | BadRequestException) {
    let detail: unknown;
    if (exception instanceof BadRequestException) {
      const response = exception.getResponse();
      // Gets DTO validation errors and returns them in the response
      if (typeof response === 'object' && 'message' in response) {
        detail = response.message;
      } else {
        detail = response;
      }
    }
    return detail;
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  static getEntityName(entityClass: EntityTarget<any>): string {
    if (typeof entityClass === 'string') {
      return entityClass;
    } else if (typeof entityClass === 'function') {
      return entityClass.name;
    } else if ('name' in entityClass) {
      return entityClass.name;
    }
    return '[unknown]';
  }

  static getResponseBody(exception: Error, statusCode: HttpStatus): ErrorResponse {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const exceptionAsAny = exception as any;
    if (exceptionAsAny.response?.customResponseBody) {
      const customResponse = omit(exceptionAsAny.response, 'customResponseBody');
      return customResponse as ErrorResponse;
    }

    return {
      ...this.mapExceptionToErrorCode(exception, statusCode),
      ...this.getErrorMessage(exception),
    };
  }

  processAxiosError(error: AxiosError) {
    const baseUrl = error.config?.baseURL;
    const url = error.config?.url;
    let fullUrl: string | undefined;
    if (baseUrl && url) {
      fullUrl = `${baseUrl}${url}`;
    }
    return {
      name: error.name,
      message: error.message,
      code: error.code,
      status: error.response?.status,
      statusText: error.response?.statusText,
      baseUrl: error.config?.baseURL,
      url: error.config?.url,
      fullUrl,
      method: error.config?.method,
      responseData: error.response?.data,
      headers: error.config?.headers as AxiosRequestHeaders,
    };
  }

  logError(exception: Error, status: HttpStatus, request: FastifyRequest) {
    let errorToLog = exception;
    if (axios.isAxiosError(exception) || exception?.name === 'AxiosError') {
      errorToLog = this.processAxiosError(exception as AxiosError);
    }
    this.logger[status >= 500 ? 'error' : 'warn'](`HttpException code ${status} response being sent to API client.`, {
      error: {
        exception: errorToLog,
        request: {
          method: request.method,
          url: request.url,
          queryParams: request.query,
          headers: request.headers,
          body: obfuscate(request.body),
        },
      },
    });
  }
}
