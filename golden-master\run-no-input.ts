import { AxiosError, AxiosResponse } from 'axios';
import chalk from 'chalk';

import { config, StepData, EndpointConfig, WorkflowConfig, BodyFieldConditions } from './config';
import { endPointString, getFullUrl, printTestSummary } from './log-utils';
import { compareResponses, makeRequest, sanitizeResponse } from './test-utils';

interface TestResult {
    passed: boolean;
    error?: Error;
}

async function handleAxiosError(error: AxiosError): Promise<void> {
    if (!error.status) {
        console.log(chalk.red(`✗ Error testing endpoint: ${error.message}\n`), error);
        return;
    }
    const { status, statusText, data } = error.response!;
    console.log(chalk.red(`Error at endpoint: ${getFullUrl(error.request)} -- ${status} ${statusText}`));
    console.log(chalk.red(JSON.stringify(data, null, 2)));
}

async function testSingleEndpoint(endpoint: EndpointConfig): Promise<TestResult> {
    try {
        console.log(chalk.cyan(`Testing ${endPointString(endpoint)}`));
        if (endpoint.name) {
            console.log(chalk.cyan(`🧪 Name: ${endpoint.name}`));
        }

        const sourceResponse = await makeRequest(config.classicApi, endpoint);
        const targetResponse = await makeRequest(config.wrapperApi, endpoint);

        const sourceSanitized = sanitizeResponse(sourceResponse, endpoint.bodyFieldConditions, endpoint.skipHeaders, endpoint.sortKey);
        const targetSanitized = sanitizeResponse(targetResponse, endpoint.bodyFieldConditions, endpoint.skipHeaders, endpoint.sortKey);

        const isMatch = await compareResponses(sourceSanitized, targetSanitized);

        if (!isMatch) {
            console.log(chalk.red(`🔗 Full target URL: ${getFullUrl(targetResponse.request)}\n`));
        }

        return { passed: isMatch };
    } catch (error) {
        if (error instanceof AxiosError) {
            await handleAxiosError(error);
        } else {
            console.error(chalk.red(`✗ Error testing endpoint: ${(error as Error).message}\n`), error);
        }
        return { passed: false, error: error as Error };
    }
}

async function testWorkflow(endpoint: WorkflowConfig): Promise<TestResult> {
    console.log(chalk.cyan(`⚙️ Starting workflow: ${endpoint.name || 'Unnamed workflow'}`));
    const steps = endpoint.workflow;
    let previousClassicResponse: StepData = { resBody: {}, params: {}, prevWorkflowState: {} };
    let previousWrapperResponse: StepData = { resBody: {}, params: {}, prevWorkflowState: {} };

    let classicWorkflowState: any = {};
    let wrapperWorkflowState: any = {};
    for (const step of steps) {
        try {
            const sourceEndpoint = step(previousClassicResponse);
            const targetEndpoint = step(previousWrapperResponse);

            console.log(chalk.cyan(`Testing step ${endPointString(sourceEndpoint)}`));
            if (endpoint.name) {
                console.log(chalk.cyan(`🧪 Name: ${endpoint.name}`));
            }

            const sourceResponse = await makeRequest(config.classicApi, sourceEndpoint);
            const targetResponse = await makeRequest(config.wrapperApi, targetEndpoint);

            const sourceSanitized = sanitizeResponse(sourceResponse, sourceEndpoint.bodyFieldConditions, sourceEndpoint.skipHeaders, sourceEndpoint.sortKey);
            const targetSanitized = sanitizeResponse(targetResponse, targetEndpoint.bodyFieldConditions, targetEndpoint.skipHeaders, targetEndpoint.sortKey);

            const isMatch = await compareResponses(sourceSanitized, targetSanitized);

            if (!isMatch) {
                console.log(chalk.red(`❌  Step ${step.name || 'unnamed'} failed`));
                return { passed: false };
            }

            console.log(chalk.green(`✅  Step ${endPointString(sourceEndpoint)} passed\n`));


            classicWorkflowState = {
                ...classicWorkflowState,
                ...sourceEndpoint.workflowState ?? {}
            }
            wrapperWorkflowState = {
                ...wrapperWorkflowState,
                ...targetEndpoint.workflowState ?? {}
            }
            previousClassicResponse = {
                resBody: sourceResponse.data,
                params: sourceEndpoint.params || {},
                prevWorkflowState: classicWorkflowState
            };
            previousWrapperResponse = {
                resBody: targetResponse.data,
                params: targetEndpoint.params || {},
                prevWorkflowState: wrapperWorkflowState
            };
        } catch (error) {
            if (error instanceof AxiosError) {
                await handleAxiosError(error);
            } else {
                console.error(chalk.red(`✗ Error testing endpoint: ${(error as Error).message}\n`), error);
            }
            return { passed: false, error: error as Error };
        }
    }

    console.log(chalk.green(`✅  Workflow passed`));
    return { passed: true };
}

async function run() {
    console.log(chalk.blue('\nStarting Golden Master API Tests\n'));
    let passCount = 0;
    let failCount = 0;

    for (const endpoint of config.endpoints) {
        const result = await (isWorkflowConfig(endpoint) ? testWorkflow(endpoint) : testSingleEndpoint(endpoint));
        result.passed ? passCount++ : failCount++;
    }

    printTestSummary(passCount, failCount);

    if (failCount > 0) {
        process.exit(1);
    }
}

function isWorkflowConfig(endpoint: EndpointConfig | WorkflowConfig): endpoint is WorkflowConfig {
    return 'workflow' in endpoint && Array.isArray(endpoint.workflow);
}

if (require.main === module) {
    run().catch(console.error);
}
