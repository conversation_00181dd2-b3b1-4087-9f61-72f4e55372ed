# Classic ID Extraction Script

This script extracts user IDs from the database based on specific criteria and outputs them to a JSON file.

## Overview

The script performs the following operations:

1. Connects to the PostgreSQL database using connection details from environment variables
2. Executes a query to fetch user IDs that match the specified criteria
3. Processes the results in batches of 10,000 records
4. Writes the IDs to a JSON file in the `data` directory

## Query Criteria

The script extracts user IDs based on the following criteria:

```sql
SELECT u.id
FROM users u
JOIN activations a ON u.id = a."userId"
JOIN apps sa ON a."appId" = sa.id
WHERE
   a."deletedAt" IS NULL
  AND u."deletedAt" IS NULL
  AND sa."deletedAt" IS NULL
  AND sa."mode" IS NOT NULL
  AND sa."productId" IS NOT NULL
  AND sa."productEnvId" IS NOT NULL
  AND u."createdAt" <= TIMESTAMP '2025-05-15 23:59:59';
```

## Configuration

The script uses the following environment variables:

- `DATABASE_URL`: PostgreSQL connection string (required)
- `NAME`: Prefix for the output file name (defaults to 'default' if not provided)

## Output

The script outputs a JSON file containing an array of user IDs. The file is saved in the `data` directory with the name pattern `{NAME}-classic-ids.json`.

## Running the Script

```bash
# Make sure environment variables are set in .env file
npm run extract-classic-ids
```

## Batch Processing

The script processes records in batches of 10,000 to avoid memory issues with large result sets. It uses the last ID from each batch to paginate through the results.

## Resume Functionality

If the script is interrupted or crashes, it can be restarted and will automatically resume from where it left off:

1. When restarted, the script checks if the output file exists
2. If it exists, it reads the file to find the highest ID
3. The script then continues processing from that ID
4. New results are appended to the existing file

This ensures that no data is lost if the script is interrupted, and you don't need to start from scratch.
