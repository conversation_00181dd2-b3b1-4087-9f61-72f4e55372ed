import { Inject, Injectable } from '@nestjs/common';
import { AuthLibraryUtils, KeycloakService } from '@superawesome/freekws-auth-library';
import { NestJsClient } from '@superawesome/freekws-clients-nestjs';
import {
  DEVPORTAL_BACKEND_API_CLIENT_INJECT_KEY,
  devportalApiPlugin,
  EServiceID,
} from '@superawesome/freekws-devportal-common';
import { TServiceID } from '@superawesome/freekws-service-activation-service-common';
import { Span } from 'nestjs-ddtrace';

import { KEYCLOAK_PROVIDER } from '../keycloak/keycloak.module';

@Injectable()
@Span()
export class DevPortalService {
  private serviceAccessToken: string;
  private waitForServiceAccessTokenPromise?: Promise<void>;

  constructor(
    @Inject(DEVPORTAL_BACKEND_API_CLIENT_INJECT_KEY)
    private readonly devPortalApiClient: NestJsClient<typeof devportalApiPlugin>,
    @Inject(KEYCLOAK_PROVIDER)
    private readonly keycloakService: KeycloakService,
  ) {}

  async getWebhookSecret(
    orgId: string,
    orgEnvId: string,
    serviceId: TServiceID,
    webhookName: string,
  ): Promise<string | undefined> {
    if (!AuthLibraryUtils.isTokenValid(this.serviceAccessToken)) {
      await this.setUpServiceToken();
    }

    const {
      data: {
        response: { webhooks },
      },
    } = await this.devPortalApiClient.getModule('webhook').listOrgWebhooks({
      params: {
        orgId: orgId,
        orgEnvId: orgEnvId,
        serviceId: serviceId as EServiceID,
      },
    });

    return webhooks.find((webhook) => webhook.name === webhookName)?.secretKey;
  }

  private async setUpServiceToken(): Promise<void> {
    if (this.waitForServiceAccessTokenPromise) {
      return this.waitForServiceAccessTokenPromise;
    }

    try {
      if (!this.serviceAccessToken || !AuthLibraryUtils.isTokenValid(this.serviceAccessToken)) {
        this.waitForServiceAccessTokenPromise = this.updateServiceAccessToken();
        return await this.waitForServiceAccessTokenPromise;
      }
    } finally {
      this.waitForServiceAccessTokenPromise = undefined;
    }
  }

  private async updateServiceAccessToken(): Promise<void> {
    this.serviceAccessToken = await this.keycloakService.getServiceAccessToken();
  }
}
