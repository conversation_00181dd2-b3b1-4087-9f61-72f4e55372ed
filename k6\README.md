# K6 Load Testing

This directory contains K6 load tests written in TypeScript for performance testing APIs.

## Prerequisites

- K6 version 0.47.0 or higher installed ([Installation Guide](https://k6.io/docs/get-started/installation/))
- Node.js and npm installed
- TypeScript dependencies installed (`npm install`)

## Environment Setup

Before running the tests, ensure you have the following environment variables configured:

```bash
BASE_URL - The base URL of the API to test (default: http://localhost:7001)
```

## Running Tests

1. Install dependencies:
```bash
npm install
```

2. Run the load test using npm script (uses default environment):
```bash
npm run load-test
```

3. Run with custom BASE_URL:
```bash
k6 run load-testing.ts --env BASE_URL=https://your-api-url
```

4. Run with additional K6 options:
```bash
k6 run load-testing.ts --env BASE_URL=http://localhost:7001 --vus 10 --duration 30s
```

## Common Options

- `--vus`: Number of virtual users
- `--duration`: Test duration
- `--iterations`: Number of iterations to run
- `-e` or `--env`: Set environment variables

## Troubleshooting

If you encounter any issues:

1. Verify K6 is installed correctly: `k6 version`
2. Ensure environment variables are set correctly
3. Check that all dependencies are installed: `npm install`
4. Make sure you're running K6 version 0.47.0 or higher