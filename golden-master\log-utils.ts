import chalk from 'chalk';
import { diff } from 'jest-diff';

import { EndpointConfig } from './config';

export function formatDiff(source: any, target: any): string {
  return (
      diff(source, target, {
        expand: false,
        contextLines: 3,
      }) || 'No differences found'
  );
}

export function endPointString(endpoint: EndpointConfig) {
  let paramLog = '';
  if (endpoint.params) {
    paramLog = `with params ${Object.entries(endpoint.params)
      .map(([key, value]) => `${key}=${value}`)
      .join('&')}`;
  }

  return `[${endpoint.method}] '${endpoint.path}' ${paramLog}`;
}

export function printTestSummary(passCount: number, failCount: number) {
  console.log(chalk.blue('📊 Test Summary:\n'));
  console.log(chalk.green(`✅  Passed: ${passCount}`));
  console.log(chalk.red(`🔴 Failed: ${failCount}`));
  console.log(chalk.whiteBright(`📁 Total: ${passCount + failCount}`));
  console.log('\n');
}

export function getFullUrl(req: { protocol: string; host: string; path: string; method: string }) {
  const { method, path, protocol, host } = req;
  return `${method} ${protocol}://${host}${path}`;
}
