import {
  ArgumentsHost,
  BadRequestException,
  ConflictException,
  ForbiddenException,
  HttpException,
  NotFoundException,
} from '@nestjs/common';
import { HttpAdapterHost } from '@nestjs/core';
import { SALogger } from '@superawesome/freekws-common-logger';
import { AxiosError } from 'axios';
import { EntityNotFoundError } from 'typeorm';

import { ExceptionsFilter } from './exceptions.filter';
import { User } from '../../../user/user.entity';

const mockHttp = {
  httpAdapter: {
    reply: jest.fn(),
  },
} as unknown as HttpAdapterHost;

const mockLogger = {
  error: jest.fn(),
  warn: jest.fn(),
} as unknown as SALogger;

const mockReq = () => ({
  method: 'POST',
  url: '/v1/test',
  query: {},
  headers: {},
  body: {},
});
const mockRes = {};

const mockHost = {
  switchToHttp: () => ({
    getRequest: mockReq,
    getResponse: jest.fn().mockReturnValue(mockRes),
  }),
} as unknown as ArgumentsHost;

describe('ExceptionsFilter', () => {
  let filter: ExceptionsFilter;
  let spyReply: jest.SpyInstance;

  beforeAll(() => {
    filter = new ExceptionsFilter(mockHttp, mockLogger);
    spyReply = jest.spyOn(mockHttp.httpAdapter, 'reply');
  });

  beforeEach(() => {
    jest.resetAllMocks();
  });

  it('should catch forbidden error', () => {
    expect(filter.catch(new ForbiddenException('resource not allowed'), mockHost)).toBeUndefined();
    expect(spyReply).toHaveBeenCalledWith(
      mockRes,
      {
        code: 1,
        codeMeaning: 'forbidden',
        errorMessage: 'resource not allowed',
      },
      403,
    );
  });

  it('should catch not found error', () => {
    expect(filter.catch(new NotFoundException('resource not found'), mockHost)).toBeUndefined();
    expect(spyReply).toHaveBeenCalledWith(
      mockRes,
      {
        code: 2,
        codeMeaning: 'notFound',
        errorMessage: 'resource not found',
      },
      404,
    );
  });

  it('should catch not found TypeORM exception', () => {
    expect(filter.catch(new EntityNotFoundError(User, {}), mockHost)).toBeUndefined();
    expect(spyReply).toHaveBeenCalledWith(
      mockRes,
      {
        code: 2,
        codeMeaning: 'notFound',
        errorMessage: 'Could not find requested User',
      },
      404,
    );
  });

  it('should catch bad request error', () => {
    expect(filter.catch(new BadRequestException('invalid data'), mockHost)).toBeUndefined();
    expect(spyReply).toHaveBeenCalledWith(
      mockRes,
      {
        code: 3,
        codeMeaning: 'badRequest',
        errorMessage: 'invalid data',
      },
      400,
    );
  });

  it('should catch conflict error', () => {
    expect(filter.catch(new ConflictException('invalid entity state'), mockHost)).toBeUndefined();
    expect(spyReply).toHaveBeenCalledWith(
      mockRes,
      {
        code: 10,
        codeMeaning: 'conflict',
        errorMessage: 'invalid entity state',
      },
      409,
    );
  });

  it('should catch random error', () => {
    expect(filter.catch(new Error('some error'), mockHost)).toBeUndefined();
    expect(spyReply).toHaveBeenCalledWith(
      mockRes,
      {
        code: 11,
        codeMeaning: 'unexpected',
        errorMessage: 'some error',
      },
      500,
    );
  });

  it('processes axios error correctly', () => {
    const mockAxiosError = {
      name: 'AxiosError',
      message: 'Request failed with status code 404',
      code: 'ERR_BAD_REQUEST',
      config: {
        baseURL: 'https://api.example.com',
        url: '/users',
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      },
      response: {
        status: 404,
        statusText: 'Not Found',
        data: { message: 'User not found' },
      },
    } as unknown as AxiosError;

    const processedError = filter.processAxiosError(mockAxiosError);

    expect(processedError).toEqual({
      name: 'AxiosError',
      message: 'Request failed with status code 404',
      code: 'ERR_BAD_REQUEST',
      status: 404,
      statusText: 'Not Found',
      baseUrl: 'https://api.example.com',
      url: '/users',
      fullUrl: 'https://api.example.com/users',
      method: 'GET',
      responseData: { message: 'User not found' },
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });

  it('should return custom response body', () => {
    expect(
      filter.catch(
        new HttpException(
          {
            customResponseBody: true,
            propOne: 'one',
            propTwo: 'two',
          },
          500,
        ),
        mockHost,
      ),
    ).toBeUndefined();

    expect(spyReply).toHaveBeenCalledWith(
      mockRes,
      {
        propOne: 'one',
        propTwo: 'two',
      },
      500,
    );
  });

  describe('getErrorMessage', () => {
    it('returns undefined detail for regular Error', () => {
      const error = new Error('test error');

      const result = ExceptionsFilter.getErrorMessage(error);

      expect(result).toEqual({
        errorMessage: 'test error',
        invalid: undefined,
        detail: undefined,
      });
    });

    it('returns detail for BadRequestException with string response', () => {
      const error = new BadRequestException({
        message: 'validation details',
      });

      const result = ExceptionsFilter.getErrorMessage(error);

      expect(result).toEqual({
        errorMessage: 'validation details',
        invalid: undefined,
        detail: undefined,
      });
    });

    it('returns detail for BadRequestException with object message', () => {
      const error = new BadRequestException({
        message: ['validation error 1', 'validation error 2'],
      });

      const result = ExceptionsFilter.getErrorMessage(error);

      expect(result).toEqual({
        errorMessage: 'Bad Request Exception',
        invalid: undefined,
        detail: ['validation error 1', 'validation error 2'],
      });
    });

    it('returns undefined detail when response message matches error message', () => {
      const msg = 'Bad Request Exception';
      const error = new BadRequestException(msg);

      const result = ExceptionsFilter.getErrorMessage(error);

      expect(result).toEqual({
        errorMessage: msg,
        invalid: undefined,
        detail: undefined,
      });
    });
  });
});
