---
description: When implementing any end to end tests
globs: 
alwaysApply: false
---
- When writing end to end tests do not use jest. 
- Ensure tests remain consistent with other tests in that file- 
- Pay careful attention to using nock to mock endpoints in end to end tests.- 
    - Check the implementation and all calling methods to ensure you understand all endpoints called before implementing the end to end test.  
- Use the `makeRequest` function where possible located in [request-helper.ts](mdc:test/utils/request-helper.ts)  it will give you better debug logs in the console, if test fail
- Run end to end tests with the following command `C:/Users/<USER>/git/Codurance/EpicGames/freekws-classic-wrapper-backend/scripts/docker-npm-run.sh test:e2e --no-clean` --no-clean makes it run faster, so the containers do not completely restart.
- Look at how other end ot end tests do things, if you get stuck on a particular problem