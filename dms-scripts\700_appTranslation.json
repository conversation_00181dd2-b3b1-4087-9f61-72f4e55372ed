[{"rule-type": "selection", "rule-id": "700", "rule-name": "Include appsTranslations table", "object-locator": {"schema-name": "public", "table-name": "appsTranslations"}, "rule-action": "include", "filters": [{"filter-type": "source", "column-name": "deletedAt", "filter-conditions": [{"filter-operator": "null"}]}]}, {"rule-type": "transformation", "rule-id": "701", "rule-name": "Rename appsTranslations to app_translation", "rule-action": "rename", "rule-target": "table", "object-locator": {"schema-name": "public", "table-name": "appsTranslations"}, "value": "app_translation"}, {"rule-type": "transformation", "rule-id": "702", "rule-name": "Add orgEnvId column to appsTranslations", "rule-target": "column", "object-locator": {"schema-name": "public", "table-name": "appsTranslations"}, "rule-action": "add-column", "value": "orgEnvId", "expression": "'org-env-id-value'", "data-type": {"type": "string", "length": 255}}, {"rule-type": "transformation", "rule-id": "710", "rule-name": "Remove deletedAt column from appsTranslations", "rule-action": "remove-column", "rule-target": "column", "object-locator": {"schema-name": "public", "table-name": "appsTranslations", "column-name": "deletedAt"}}, {"rule-type": "transformation", "rule-id": "711", "rule-name": "Remove ssoTextSignupPage column from appsTranslations", "rule-action": "remove-column", "rule-target": "column", "object-locator": {"schema-name": "public", "table-name": "appsTranslations", "column-name": "ssoTextSignupPage"}}, {"rule-type": "transformation", "rule-id": "712", "rule-name": "Remove ssoTextParentEmailPage column from appsTranslations", "rule-action": "remove-column", "rule-target": "column", "object-locator": {"schema-name": "public", "table-name": "appsTranslations", "column-name": "ssoTextParentEmailPage"}}, {"rule-type": "transformation", "rule-id": "713", "rule-name": "Remove ssoTextSuccessPage column from appsTranslations", "rule-action": "remove-column", "rule-target": "column", "object-locator": {"schema-name": "public", "table-name": "appsTranslations", "column-name": "ssoTextSuccessPage"}}]