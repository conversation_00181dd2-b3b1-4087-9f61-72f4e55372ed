import { MigrationInterface, QueryRunner } from 'typeorm';

export class FixAppOauthClientIdIndex1738327839553 implements MigrationInterface {
  name = 'FixAppOauthClientIdIndex1738327839553';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        DROP INDEX "public"."app_oauthclient_idx"
    `);
    await queryRunner.query(`
        CREATE UNIQUE INDEX "app_oauthclient_orgenv_idx" ON "app" ("oauthClientId", "orgEnvId")
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        DROP INDEX "public"."app_oauthclient_orgenv_idx"
    `);
    await queryRunner.query(`
        CREATE UNIQUE INDEX "app_oauthclient_idx" ON "app" ("oauthClientId")
    `);
  }
}
