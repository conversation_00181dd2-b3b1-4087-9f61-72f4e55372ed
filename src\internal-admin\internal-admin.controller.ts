import {
  BadRequestException,
  Body,
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiOkResponse, ApiOperation, ApiTags } from '@nestjs/swagger';
import { UpdateUserDateOfBirthDTO } from '@superawesome/freekws-classic-wrapper-common';
import { CheckPolicies, KeycloakAuthGuard, PoliciesGuard } from '@superawesome/freekws-nestjs-guards';
import { And, HasAzp } from '@superawesome/freekws-nestjs-guards/policies';
import { UserSettingValueDTO } from '@superawesome/freekws-settings-common';

import { InternalAdminService } from './internal-admin.service';
import { EAPITags } from '../common/types';
import { UpdateUserDOBSchema } from '../org-env/types';
import { UserDeletionDTO, UserSearchResponseDTO } from '../user/user.dto';
import { UserService } from '../user/user.service';

@Controller('/internal-admin')
@UseGuards(KeycloakAuthGuard)
export class InternalAdminController {
  constructor(private readonly internalAdminService: InternalAdminService, private readonly userService: UserService) {}

  @ApiOperation({
    summary: "Internal Admin Endpoint: Update the user's dob",
    description: `Intended only for classic customers. Update the user's DOB by requesting settings with a provided dob and location.`,
  })
  @ApiTags(EAPITags.InternalAdmin)
  @ApiOkResponse({ schema: UpdateUserDOBSchema })
  @Put('/org-envs/:orgEnvId/users/:userId/dob')
  @HttpCode(HttpStatus.OK)
  @UseGuards(PoliciesGuard)
  @CheckPolicies({
    useClass: And,
    inject: [new HasAzp('devportal-api')],
  })
  async updateUserDOB(
    @Param('orgEnvId') orgEnvId: string,
    @Param('userId', new ParseIntPipe()) userId: number,
    @Body() { dateOfBirth, location }: UpdateUserDateOfBirthDTO,
  ): Promise<UserSettingValueDTO[]> {
    return await this.internalAdminService.getUserSettingsToUpdateUserDOB(orgEnvId, {
      userId,
      dateOfBirth,
      location,
    });
  }

  @ApiOperation({
    summary: "Internal Admin Endpoint: Delete a user's account",
    description: `Intended only for classic customers. Delete a user's account.`,
  })
  @ApiTags(EAPITags.InternalAdmin)
  @ApiOkResponse()
  @Post('/org-envs/:orgEnvId/users/:userId/delete-account')
  @HttpCode(HttpStatus.OK)
  @UseGuards(PoliciesGuard)
  @CheckPolicies({
    useClass: And,
    inject: [new HasAzp('devportal-api')],
  })
  async deleteUserAccount(
    @Param('orgEnvId') orgEnvId: string,
    @Param('userId', new ParseIntPipe()) userId: number,
    @Body() body: UserDeletionDTO,
  ) {
    if (body.password) {
      throw new BadRequestException('Password is deprecated and should not be used');
    }

    await this.internalAdminService.deleteUserAccount(orgEnvId, userId);
  }

  @ApiOperation({
    summary: 'Internal Admin Endpoint: Search users by username',
    description: 'Search for users by username prefix. Returns up to 10 matching users with their parent emails.',
  })
  @ApiTags(EAPITags.InternalAdmin)
  @ApiOkResponse({ type: [UserSearchResponseDTO] })
  @Get('/users')
  @HttpCode(HttpStatus.OK)
  @UseGuards(PoliciesGuard)
  @CheckPolicies({
    useClass: And,
    inject: [new HasAzp('devportal-api')],
  })
  async searchUsersByUsername(@Query('username') username: string): Promise<UserSearchResponseDTO[]> {
    return await this.userService.searchUsersByUsername(username);
  }
}
