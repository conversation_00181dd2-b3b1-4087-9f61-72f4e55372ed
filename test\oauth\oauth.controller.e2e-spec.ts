import { INestApplication } from '@nestjs/common';
import { createHash, randomBytes } from 'crypto';
import nock from 'nock';

import { Utils } from '../utils';
import { APP_BASIC_AUTH, MOBILE_APP_SECRET, TEST_APP_CLIENT_ID } from '../utils/constants';
import { makeRequest } from '../utils/request-helper';

interface TokenResponse {
  token_type: string;
  access_token: string;
  refresh_token: string;
  expires_in: number;
}

function verifyTokenResponse(
  body: TokenResponse,
  previousToken?: {
    access_token: string;
    refresh_token: string;
  },
) {
  expect(body.token_type).toEqual('bearer');
  expect(body.access_token).toBeDefined();
  expect(body.refresh_token).toBeDefined();
  expect(body.expires_in).toBeDefined();

  if (previousToken) {
    expect(body.access_token).not.toEqual(previousToken.access_token);
    expect(body.refresh_token).not.toEqual(previousToken.refresh_token);
  }
}

async function requestToken(
  app: INestApplication,
  params: Record<string, string>,
  useAuth = true,
): Promise<{ body: TokenResponse }> {
  return makeRequest(app, 'post', '/oauth/token', {
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
      'x-forwarded-host': 'test.com',
      ...(useAuth ? { Authorization: APP_BASIC_AUTH } : {}),
    },
    body: params,
    expectedStatus: 200,
  });
}

function generatePKCEChallenge(method: 'S256' | 'plain' = 'S256') {
  const codeVerifier = randomBytes(32).toString('base64url');
  let codeChallenge = codeVerifier;

  if (method === 'S256') {
    codeChallenge = createHash('sha256')
      .update(codeVerifier)
      .digest('base64')
      .replaceAll('+', '-')
      .replaceAll('/', '_')
      .replaceAll('=', '');
  }

  return { codeVerifier, codeChallenge };
}

describe('OauthController (e2e)', () => {
  let app: INestApplication;

  beforeAll(async () => {
    app = await Utils.createTestServer();
    await Utils.cleanDb();
    await Utils.loadFixtures();
  });

  jest.setTimeout(100000);

  afterAll(async () => {
    await Utils.stopTestServer(app);
    jest.clearAllMocks();
  });

  describe('POST /oauth/token', () => {
    beforeEach(() => {
      nock.cleanAll();

      Utils.mockSettingsBackendAPI();
      Utils.mockFamilyAdminGroups();
      Utils.mockAgeGateAPI();
      Utils.mockPreverificationServiceAPI();
    });

    it('returns app token for valid credentials', async () => {
      const { body } = await requestToken(app, {
        scope: 'app',
        grant_type: 'client_credentials',
      });

      verifyTokenResponse(body);
    });

    it('returns mobile app token for valid credentials', async () => {
      const { body } = await requestToken(
        app,
        {
          client_id: TEST_APP_CLIENT_ID,
          client_secret: MOBILE_APP_SECRET,
          scope: 'mobileApp',
          grant_type: 'client_credentials',
        },
        false,
      );

      verifyTokenResponse(body);
    });

    it('returns user scoped token for password grant type', async () => {
      Utils.mockFamilyServiceGuardianRequest();
      const { body } = await requestToken(app, {
        grant_type: 'password',
        username: 'testuser',
        password: 'toastjam2',
        scope: 'user',
      });

      verifyTokenResponse(body);
    });

    it('returns new tokens for refresh token grant type with user scope', async () => {
      Utils.mockFamilyServiceGuardianRequest();
      const initialResponse = await requestToken(app, {
        grant_type: 'password',
        username: 'testuser',
        password: 'toastjam2',
        scope: 'user',
      });

      Utils.mockFamilyServiceGuardianRequest();
      const { body } = await requestToken(app, {
        grant_type: 'refresh_token',
        refresh_token: initialResponse.body.refresh_token,
        scope: 'user',
      });

      verifyTokenResponse(body, initialResponse.body);
    });

    it('returns new tokens for refresh token grant type with app scope', async () => {
      const initialResponse = await requestToken(app, {
        scope: 'app',
        grant_type: 'client_credentials',
      });

      const { body } = await requestToken(app, {
        grant_type: 'refresh_token',
        refresh_token: initialResponse.body.refresh_token,
        scope: 'app',
      });

      verifyTokenResponse(body, initialResponse.body);
    });
  });

  describe('POST /oauth/authorise', () => {
    const TEST_REDIRECT_URI = 'https://jellymar.io/';
    let userToken: string;

    beforeEach(async () => {
      nock.cleanAll();

      Utils.mockSettingsBackendAPI();
      Utils.mockAgeGateAPI();
      Utils.mockPreverificationServiceAPI();
      Utils.mockFamilyAdminGroups();
      Utils.mockFamilyServiceGuardianRequest();

      // Get a user token for the tests
      const response = await requestToken(app, {
        grant_type: 'password',
        username: 'testuser',
        password: 'toastjam2',
        scope: 'user',
      });
      userToken = response.body.access_token;
    });

    it('should return an authorization code when provided valid credentials', async () => {
      const response = await makeRequest(app, 'post', '/oauth/authorise', {
        headers: {
          Authorization: `Bearer ${userToken}`,
          'x-forwarded-host': 'test.com',
        },
        body: {
          client_id: TEST_APP_CLIENT_ID,
          redirect_uri: TEST_REDIRECT_URI,
          response_type: 'code',
          state: 'test-state-123',
        },
        expectedStatus: 200,
      });

      expect(response.body.code).toBeDefined();
      expect(response.body.redirectUri).toEqual(TEST_REDIRECT_URI);
      expect(response.body.state).toEqual('test-state-123');
    });

    it('should support PKCE with S256 method', async () => {
      const { codeChallenge } = generatePKCEChallenge('S256');

      const { body } = await makeRequest(app, 'post', '/oauth/authorise', {
        headers: {
          Authorization: `Bearer ${userToken}`,
          'x-forwarded-host': 'test.com',
        },
        body: {
          client_id: TEST_APP_CLIENT_ID,
          redirect_uri: TEST_REDIRECT_URI,
          response_type: 'code',
          code_challenge: codeChallenge,
          code_challenge_method: 'S256',
          state: 'pkce-state-123',
        },
        expectedStatus: 200,
      });

      expect(body.code).toBeDefined();
      expect(body.redirectUri).toEqual(TEST_REDIRECT_URI);
      expect(body.state).toEqual('pkce-state-123');
    });

    it('should support PKCE with plain method', async () => {
      const { codeChallenge } = generatePKCEChallenge('plain');

      const { body } = await makeRequest(app, 'post', '/oauth/authorise', {
        headers: {
          Authorization: `Bearer ${userToken}`,
          'x-forwarded-host': 'test.com',
        },
        body: {
          client_id: TEST_APP_CLIENT_ID,
          redirect_uri: TEST_REDIRECT_URI,
          response_type: 'code',
          code_challenge: codeChallenge,
          code_challenge_method: 'plain',
          state: 'pkce-plain-123',
        },
        expectedStatus: 200,
      });

      expect(body.code).toBeDefined();
      expect(body.redirectUri).toEqual(TEST_REDIRECT_URI);
      expect(body.state).toEqual('pkce-plain-123');
    });

    it('should use query state parameter when no state is provided in body', async () => {
      const { body } = await makeRequest(app, 'post', '/oauth/authorise?state=query-state-123', {
        headers: {
          Authorization: `Bearer ${userToken}`,
          'x-forwarded-host': 'test.com',
        },
        body: {
          client_id: TEST_APP_CLIENT_ID,
          redirect_uri: TEST_REDIRECT_URI,
          response_type: 'code',
        },
        expectedStatus: 200,
      });

      expect(body.code).toBeDefined();
      expect(body.redirectUri).toEqual(TEST_REDIRECT_URI);
      expect(body.state).toEqual('query-state-123');
    });

    it('should support the complete authorization flow with code exchange', async () => {
      // Step 1: Get an authorization code
      const authResponse = await makeRequest(app, 'post', '/oauth/authorise', {
        headers: {
          Authorization: `Bearer ${userToken}`,
          'x-forwarded-host': 'test.com',
        },
        body: {
          client_id: TEST_APP_CLIENT_ID,
          redirect_uri: TEST_REDIRECT_URI,
          response_type: 'code',
          state: 'complete-flow-test',
        },
        expectedStatus: 200,
      });

      const authCode = authResponse.body.code;
      expect(authCode).toBeDefined();

      Utils.mockFamilyServiceGuardianRequest();
      // Step 2: Exchange the code for tokens
      const { body } = await makeRequest(app, 'post', '/oauth/token', {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          Authorization: APP_BASIC_AUTH,
          'x-forwarded-host': 'test.com',
        },
        body: {
          grant_type: 'authorization_code',
          code: authCode,
          redirect_uri: TEST_REDIRECT_URI,
        },
        expectedStatus: 200,
      });

      verifyTokenResponse(body);
    });

    it('should support the complete authorization flow with PKCE', async () => {
      // Step 1: Generate PKCE parameters
      const { codeVerifier, codeChallenge } = generatePKCEChallenge('S256');

      // Step 2: Get an authorization code with code_challenge
      const authResponse = await makeRequest(app, 'post', '/oauth/authorise', {
        headers: {
          Authorization: `Bearer ${userToken}`,
          'x-forwarded-host': 'test.com',
        },
        body: {
          client_id: TEST_APP_CLIENT_ID,
          redirect_uri: TEST_REDIRECT_URI,
          response_type: 'code',
          code_challenge: codeChallenge,
          code_challenge_method: 'S256',
          state: 'pkce-flow-test',
        },
        expectedStatus: 200,
      });

      const authCode = authResponse.body.code;
      expect(authCode).toBeDefined();
      expect(authResponse.body.redirectUri).toEqual(TEST_REDIRECT_URI);
      expect(authResponse.body.state).toEqual('pkce-flow-test');

      Utils.mockFamilyServiceGuardianRequest();
      // Step 3: Exchange the code for tokens using the code_verifier
      const { body } = await makeRequest(app, 'post', '/oauth/token', {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          Authorization: APP_BASIC_AUTH,
          'x-forwarded-host': 'test.com',
        },
        body: {
          grant_type: 'authorization_code',
          code: authCode,
          redirect_uri: TEST_REDIRECT_URI,
          code_verifier: codeVerifier,
        },
        expectedStatus: 200,
      });

      verifyTokenResponse(body);
    });
  });
});
