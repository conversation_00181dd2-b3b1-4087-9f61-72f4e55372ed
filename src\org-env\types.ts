import { ApiResponseSchemaHost } from '@nestjs/swagger';

export interface IClientCredentials {
  clientId: string;
  secret: string;
}

export const UpdateUserDOBSchema: ApiResponseSchemaHost['schema'] = {
  type: 'array',
  items: {
    type: 'object',
    properties: {
      namespace: { type: 'string' },
      settingName: { type: 'string' },
      preferredValue: { oneOf: [{ type: 'string' }, { type: 'number' }, { type: 'boolean' }] },
      effectiveValue: { oneOf: [{ type: 'string' }, { type: 'number' }, { type: 'boolean' }] },
      lastGraduatedAt: { type: 'number' },
      consentRequestedAt: { type: 'string' },
    },
  },
};
