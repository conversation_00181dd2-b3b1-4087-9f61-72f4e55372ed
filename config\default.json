{"http": {"port": "80", "cors": {"origin": "*"}}, "logger": {"project": {"name": "freekws-classic-wrapper-backend"}, "console": {"level": "info"}, "logEntries": {"level": "warn", "token": "the-le-token"}, "sentry": {"level": "error", "release": "sentry-release"}}, "metrics": {"global_tags": ["project:freekws-classic-wrapper-backend"], "elasticAPM": {"serviceName": "freekws-classic-wrapper-backend"}}, "intervalHealthCheck": {"intervalMs": "60000"}, "cacheable": {"enabled": "true", "defaultTtlSecs": "60", "maxCacheSizeEntries": "1000", "metricsNamespace": "classicWrapperBackend"}, "database": {"type": "postgres", "masterUrl": "", "slaveUrls": "", "synchronize": false, "logging": false, "entities": ["dist/**/*.entity.js"], "subscribers": [], "autoLoadEntities": true, "migrations": ["dist/migration/*.js"], "cli": {"migrationsDir": "migration"}, "maxConnectionLimit": "3"}, "keycloak": {"realm": "kws", "clientId": "classic-wrapper-api", "secret": "get-the-staging-secret-for-local-development", "authServerUrl": "https://auth.kws.staging.superawesome.com/auth", "timeoutMs": "10000", "expirationTime": "259200", "realmUrl": "http://auth.kws.staging.superawesome.com/auth", "additionalTrustedIssuers": "", "audience": "classic-wrapper"}, "ageGateCircuitBreakerConfig": {"timeoutMs": "15000", "errorThresholdPercentage": "50", "resetTimeoutMs": "20000"}, "ageGateService": {"baseURL": "http://host.docker.internal:1182", "timeoutMs": "300", "retries": "0", "initialRetryDelay": "0", "bailOnStatus": "400,401,403,404,429"}, "settingsBackendCircuitBreakerConfig": {"timeoutMs": "15000", "errorThresholdPercentage": "50", "resetTimeoutMs": "20000"}, "settingsBackend": {"baseURL": "http://host.docker.internal:1183", "timeoutMs": "3000", "retries": "0", "initialRetryDelay": "0", "bailOnStatus": "400,401,403,404,429"}, "familyServiceCircuitBreakerConfig": {"timeoutMs": "15000", "errorThresholdPercentage": "50", "resetTimeoutMs": "20000"}, "familyService": {"baseURL": "http://host.docker.internal:1184", "timeoutMs": "3000", "retries": "0", "initialRetryDelay": "0", "bailOnStatus": "400,401,403,404,429"}, "preVerificationServiceCircuitBreakerConfig": {"timeoutMs": "15000", "errorThresholdPercentage": "50", "resetTimeoutMs": "20000"}, "preVerificationService": {"baseURL": "http://host.docker.internal:1185", "timeoutMs": "3000", "retries": "0", "initialRetryDelay": "0", "bailOnStatus": "400,401,403,404,429"}, "analyticService": {"baseURL": "http://host.docker.internal:1186", "authHeader": ""}, "devPortalBackendCircuitBreakerConfig": {"timeoutMs": "15000", "errorThresholdPercentage": "50", "resetTimeoutMs": "20000"}, "devPortalBackend": {"baseURL": "http://host.docker.internal:1187", "timeoutMs": "3000", "retries": "0", "initialRetryDelay": "0", "bailOnStatus": "400,401,403,404,429"}, "appWebhooks": {"topicName": "default", "sendWebhooks": false}, "encryption": {"secrets": "0:80f90f75f38a86d93ff18b8a1f46409e423c6428ac74ee44b3c57c773e6a38e9", "secretVersion": "0"}, "callbackBackend": {"baseURL": "http://host.docker.internal:1188", "bailOnStatus": "400,401,403,404", "timeoutMs": "3000", "initialRetryDelay": "200", "retries": "2"}, "callbackServiceCircuitBreakerConfig": {"timeoutMs": "15000", "errorThresholdPercentage": "50", "resetTimeoutMs": "20000"}, "badger": {"apiKey": "Mr.<PERSON>", "baseURL": "https://moderation-ingest-service-prod-external.ecbc.live.use1a.on.epicgames.com"}, "talon": {"apiKey": "kazooie", "flowId": "kws_registration_dev"}, "environment": "test", "emailService": {"kafkaTopic": "", "footer": {"logoSrc": ""}, "header": {"headerSrc": ""}}, "links": {"kwsFaqLink": "https://parents.kidswebservices.com/parent-verification-faqs/", "kwsPrivacyPolicyLink": "https://www.kidswebservices.com/privacy-policy/", "kwsTermsLink": "https://www.kidswebservices.com/terms-of-use/", "kwsHelpCentreLink": "https://parents.kidswebservices.com/"}, "brandingApiClient": {"baseURL": "", "bailOnStatus": "400,401,403,404", "timeoutMs": "3000", "initalRetryDelay": "200", "retries": "2"}}