import { Module } from '@nestjs/common';
import { NestJsClient, TNestJsHttpClientConfig } from '@superawesome/freekws-clients-nestjs';
import {
  DEVPORTAL_BACKEND_API_CLIENT_INJECT_KEY,
  DEVPORTAL_BACKEND_API_CLIENT_UPSTREAM,
  devportalApiPlugin,
} from '@superawesome/freekws-devportal-common';
import { MetricsService, MetricsServiceModule } from '@superawesome/freekws-metrics-nestjs-service';
import type { AxiosError } from 'axios';
import CircuitBreaker from 'opossum';

import { DevPortalService } from './dev-portal.service';
import { ConfigModule } from '../config/config.module';
import { ConfigService } from '../config/config.service';
import { KeycloakModule } from '../keycloak/keycloak.module';

@Module({
  imports: [KeycloakModule, ConfigModule, MetricsServiceModule],
  providers: [
    DevPortalService,
    {
      provide: DEVPORTAL_BACKEND_API_CLIENT_INJECT_KEY,
      useFactory: (configService: ConfigService, metricsService: MetricsService) => {
        const circuitBreakerConfig = configService.getDevPortalServiceCircuitBreakerConfig();
        const { baseURL, ...devPortalServiceClientConfig } = configService.getDevPortalService();
        const clientConfig: TNestJsHttpClientConfig = {
          timeout: devPortalServiceClientConfig.timeoutMs,
          upstream: DEVPORTAL_BACKEND_API_CLIENT_UPSTREAM,
          retry: {
            retries: devPortalServiceClientConfig.retries,
            initialRetryDelay: devPortalServiceClientConfig.initialRetryDelay,
            bailOnStatus: devPortalServiceClientConfig.bailOnStatus,
          },
          circuitBreaker: {
            timeout: circuitBreakerConfig.timeoutMs,
            errorThresholdPercentage: circuitBreakerConfig.errorThresholdPercentage,
            resetTimeout: circuitBreakerConfig.resetTimeoutMs,
            errorFilter: (error: AxiosError) => {
              return error.response?.status && error.response.status >= 400 && error.response.status < 500;
            },
          } as CircuitBreaker.Options,
        };
        return new NestJsClient(devportalApiPlugin, baseURL, metricsService.metrics, clientConfig);
      },
      inject: [ConfigService, MetricsService],
    },
  ],
  exports: [DevPortalService],
})
export class DevPortalModule {}
