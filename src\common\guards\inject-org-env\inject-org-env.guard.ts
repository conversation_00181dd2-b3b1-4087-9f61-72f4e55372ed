import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import { Span } from 'nestjs-ddtrace';

import { UnauthenticatedFastifyRequest } from './types';
import { OrgEnvService } from '../../../org-env/org-env.service';

@Injectable()
@Span()
export class InjectOrgEnvGuard implements CanActivate {
  constructor(private readonly orgEnvService: OrgEnvService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest<UnauthenticatedFastifyRequest>();

    request.raw.orgEnv = await this.orgEnvService.getOrgEnvFromRequest(request);

    return true;
  }
}
