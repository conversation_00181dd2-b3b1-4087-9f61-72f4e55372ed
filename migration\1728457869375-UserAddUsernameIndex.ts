import { MigrationInterface, QueryRunner } from 'typeorm';

export class UserAddUsernameIndex1728457869375 implements MigrationInterface {
  name = 'UserAddUsernameIndex1728457869375';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            CREATE UNIQUE INDEX "user_username_orgEnvId" ON "user" (LOWER("username"), "orgEnvId")
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            DROP INDEX "public"."user_username_orgEnvId"
        `);
  }
}
