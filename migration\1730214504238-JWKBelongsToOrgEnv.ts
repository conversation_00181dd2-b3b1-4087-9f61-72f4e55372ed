import { MigrationInterface, QueryRunner } from 'typeorm';

export class JWKBelongsToOrgEnv1730214504238 implements MigrationInterface {
  name = 'JWKBelongsToOrgEnv1730214504238';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "jwk" DROP CONSTRAINT "FK_ac120b7c6b7bba4a098323fb26a"
        `);
    await queryRunner.query(`
            ALTER TABLE "jwk" DROP COLUMN "appId"
        `);
    await queryRunner.query(`
            ALTER TABLE "jwk" DROP COLUMN "appOrgEnv"
        `);
    await queryRunner.query(`
            ALTER TABLE "jwk"
            ADD "orgEnvId" character varying
        `);
    await queryRunner.query(`
            ALTER TABLE "jwk"
            ADD CONSTRAINT "FK_94f6862e99cb2f6b181e65c0a1d" FOREIGN KEY ("orgEnvId") REFERENCES "org_env"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
    await queryRunner.query(`
        ALTER TABLE "jwk"
        ALTER COLUMN "publicPem" DROP NOT NULL
    `);
    await queryRunner.query(`
        CREATE UNIQUE INDEX "app_oauthclient_idx" ON "app" ("oauthClientId")
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "jwk" DROP CONSTRAINT "FK_94f6862e99cb2f6b181e65c0a1d"
        `);
    await queryRunner.query(`
            ALTER TABLE "jwk" DROP COLUMN "orgEnvId"
        `);
    await queryRunner.query(`
            ALTER TABLE "jwk"
            ADD "appOrgEnv" character varying
        `);
    await queryRunner.query(`
            ALTER TABLE "jwk"
            ADD "appId" integer
        `);
    await queryRunner.query(`
            ALTER TABLE "jwk"
            ADD CONSTRAINT "FK_ac120b7c6b7bba4a098323fb26a" FOREIGN KEY ("appId", "appOrgEnv") REFERENCES "app"("id", "orgEnvId") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
    await queryRunner.query(`
        ALTER TABLE "jwk"
        ALTER COLUMN "publicPem"
        SET NOT NULL
    `);
    await queryRunner.query(`
        DROP INDEX "public"."app_oauthclient_idx"
    `);
  }
}
