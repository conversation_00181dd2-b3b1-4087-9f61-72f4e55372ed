WITH selected_apps AS (SELECT id
                       FROM apps
                       WHERE "deletedAt" IS NULL
                         AND "mode" IS NOT NULL
                         AND "productId" IS NOT NULL
                         AND "productEnvId" IS NOT NULL),
     selected_users AS (SELECT DISTINCT u.id
                        FROM users u
                                 INNER JOIN activations a ON u.id = a."userId"
                                 INNER JOIN selected_apps sa ON a."appId" = sa.id
                        WHERE u."deletedAt" IS NULL
                          AND a."deletedAt" IS NULL),
     selected_activations AS (SELECT id
                              FROM activations
                              WHERE "appId" IN (SELECT id FROM selected_apps)
                                AND "userId" IN (SELECT id FROM selected_users)
                                AND "deletedAt" IS NULL),
     selected_app_translations AS (SELECT id
                                   FROM "appsTranslations"
                                   WHERE "appId" IN (SELECT id FROM selected_apps)
                                     AND "deletedAt" IS NULL),
     selected_jwks AS (SELECT id
                       FROM "jwks"
                       WHERE "deletedAt" IS NULL),
     selected_webhooks AS (SELECT id
                           FROM "webhooks"
                           WHERE ("appId" IN (SELECT id FROM selected_apps) OR ("appId" IS NULL))
                             AND "deletedAt" IS NULL
                             AND "secretKey" IS NOT NULL),
     selected_refreshTokens AS (SELECT id
                                FROM "refreshTokens"
                                WHERE "expires" >= '2025-02-28' -- update this for today!
                                  AND "deletedAt" IS NULL),
     record_counts AS (SELECT 'apps' AS table_name, COUNT(*) AS count
                       FROM selected_apps
                       UNION ALL
                       SELECT 'users' AS table_name, COUNT(*) AS count
                       FROM selected_users
                       UNION ALL
                       SELECT 'activations' AS table_name, COUNT(*) AS count
                       FROM selected_activations
                       UNION ALL
                       SELECT 'appTranslations' AS table_name, COUNT(*) AS count
                       FROM selected_app_translations
                       UNION ALL
                       SELECT 'jwks' AS table_name, COUNT(*) AS count
                       FROM selected_jwks
                       UNION ALL
                       SELECT 'refreshTokens' AS table_name, COUNT(*) AS count
                       FROM selected_refreshTokens
                       UNION ALL
                       SELECT 'webhooks' AS table_name, COUNT(*) AS count
                       FROM selected_webhooks)
SELECT table_name || ': ' || count AS summaryClassic
FROM record_counts;

