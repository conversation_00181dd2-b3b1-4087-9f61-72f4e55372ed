import { TestingModule } from '@nestjs/testing';
import { HttpService } from '@superawesome/freekws-http-nestjs-service';

import { TalonService } from './talon.service';
import { ConfigService } from '../common/services/config/config.service';
import { Testing } from '../common/utils';

jest.mock('@superawesome/freekws-common-logger', () => ({
  SALogger: jest.fn().mockImplementation(() => ({
    info: jest.fn(),
    error: jest.fn(),
  })),
}));

describe('TalonService', () => {
  let service: TalonService;
  let httpService: HttpService;

  const mockConfig = {
    config: {
      environment: 'staging',
      talon: {
        apiKey: 'mock-api-key',
        flowId: 'mock-flow-id',
      },
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Testing.createModule({
      providers: [
        TalonService,
        {
          provide: ConfigService,
          useValue: mockConfig,
        },
        {
          provide: HttpService,
          useValue: {
            init: jest.fn(),
            request: jest.fn(),
          },
        },
      ],
    });

    service = module.get<TalonService>(TalonService);
    httpService = module.get<HttpService>(HttpService);
  });

  describe('verify', () => {
    const mockSolveToken = 'mock-solve-token';
    const mockHeaders = {
      'user-agent': 'test-agent',
      'x-forwarded-for': '***********, ********',
    };
    const mockParentEmail = '<EMAIL>';

    it('should bypass Talon verification in staging environment', async () => {
      const result = await service.verify(
        mockSolveToken,
        { ...mockHeaders, 'x-kws-bypass-talon': 'true' },
        mockParentEmail,
      );

      expect(result).toBe(true);
      expect(service['logger'].info).toHaveBeenCalledWith('Bypassing Talon in staging environment');
      expect(httpService.request).not.toHaveBeenCalled();
    });

    it('should call Talon API and return true when solved is true', async () => {
      jest.spyOn(httpService, 'request').mockResolvedValueOnce({
        data: { solved: true },
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {},
      });

      const result = await service.verify(mockSolveToken, mockHeaders, mockParentEmail);

      expect(httpService.request).toHaveBeenCalledWith({
        method: 'POST',
        url: 'https://talon-service-prod.ol.epicgames.com/v1/verify',
        data: {
          flow_id: 'mock-flow-id',
          solve_token: mockSolveToken,
          user_ip: '***********',
          headers: expect.arrayContaining([
            { name: 'user-agent', value: 'test-agent' },
            { name: 'x-forwarded-for', value: '***********, ********' },
          ]),
          form_data: [
            {
              name: 'email',
              value: mockParentEmail,
            },
          ],
        },
        headers: {
          'X-Epic-Auth-Key': 'mock-api-key',
          'Content-Type': 'application/json',
        },
      });
      expect(result).toBe(true);
      expect(service['logger'].info).toHaveBeenCalled();
    });

    it('should call Talon API and return false when solved is false', async () => {
      jest.spyOn(httpService, 'request').mockResolvedValueOnce({
        data: { solved: false },
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {},
      });

      const result = await service.verify(mockSolveToken, mockHeaders, mockParentEmail);

      expect(result).toBe(false);
      expect(service['logger'].info).toHaveBeenCalled();
    });

    it('should return true (fail open) when Talon API call fails', async () => {
      jest.spyOn(httpService, 'request').mockRejectedValueOnce(new Error('API error'));

      const result = await service.verify(mockSolveToken, mockHeaders, mockParentEmail);

      expect(result).toBe(true);
      expect(service['logger'].error).toHaveBeenCalledWith('Failed to call Talon', expect.any(Error));
    });

    it('should handle missing x-forwarded-for header', async () => {
      jest.spyOn(httpService, 'request').mockResolvedValueOnce({
        data: { solved: true },
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {},
      });

      const headersWithoutForwarded = { 'user-agent': 'test-agent' };

      await service.verify(mockSolveToken, headersWithoutForwarded, mockParentEmail);

      expect(httpService.request).toHaveBeenCalledWith(
        expect.objectContaining({
          data: expect.objectContaining({
            user_ip: null,
          }),
        }),
      );
    });

    it('should handle missing parentEmail', async () => {
      jest.spyOn(httpService, 'request').mockResolvedValueOnce({
        data: { solved: true },
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {},
      });

      await service.verify(mockSolveToken, mockHeaders);

      expect(httpService.request).toHaveBeenCalledWith(
        expect.objectContaining({
          data: expect.objectContaining({
            form_data: [
              {
                name: 'email',
                value: undefined,
              },
            ],
          }),
        }),
      );
    });
  });
});
