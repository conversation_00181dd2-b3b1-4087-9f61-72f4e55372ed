import { TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { EOAuthScope } from '@superawesome/freekws-classic-wrapper-common';
import { Repository, TypeORMError } from 'typeorm';

import { JWK } from './jwk.entity';
import { JWKService } from './jwk.service';
import { TJwtPayload } from './types';
import {
  JWT_TOKEN_WITHOUT_KID,
  RS256_PRIVATE_KEY,
  RS256_PUBLIC_KEY,
  HS256_PRIVATE_KEY,
  VALID_RS_JWT_TOKEN,
  VALID_HS_JWT_TOKEN,
} from '../../test/utils/constants';
import { Testing } from '../common/utils';

const orgId = 'charge-blade';

describe('JWKService', () => {
  let service: JWKService;
  let repository: Repository<JWK>;

  beforeEach(async () => {
    const module: TestingModule = await Testing.createModule({ providers: [JWKService] });

    service = module.get<JWKService>(JWKService);
    repository = module.get(getRepositoryToken(JWK));
  });

  jest.setTimeout(50000);

  it('should be defined', () => {
    expect(service).toBeDefined();
    expect(repository).toBeDefined();
  });

  describe('sign', () => {
    const appTokenPayload = {
      clientId: 'client-id',
      appId: 123456,
      scope: EOAuthScope.APP,
    } as TJwtPayload;

    it('should return signed JWT string', async () => {
      jest.spyOn(repository, 'findOneOrFail').mockResolvedValueOnce({
        privatePem: RS256_PRIVATE_KEY,
        keyId: '123456',
        algorithm: 'RS256',
      } as JWK);

      const token = await service.sign(appTokenPayload, 'org-id');
      expect(token.startsWith('eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjEyMzQ1NiJ9.')).toBeTruthy();
    });

    it('should return HS256 signed JWT string', async () => {
      jest.spyOn(repository, 'findOneOrFail').mockResolvedValueOnce({
        privatePem: HS256_PRIVATE_KEY,
        keyId: '123456',
        algorithm: 'HS256',
      } as JWK);

      const token = await service.sign(appTokenPayload, 'org-id');

      expect(token.startsWith('eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjEyMzQ1NiJ9.')).toBeTruthy();
    });

    it('should throw an error for org without JWK', async () => {
      jest.spyOn(repository, 'findOneOrFail').mockRejectedValueOnce(new TypeORMError('Not Found'));
      await expect(service.sign(appTokenPayload, 'org-id')).rejects.toThrow('Not Found');
    });

    it('should throw an error if signature fails', async () => {
      jest.spyOn(repository, 'findOneOrFail').mockResolvedValueOnce({
        privatePem: 'cHJpdmF0ZS1rZXk=',
        keyId: '123456',
        algorithm: 'RS256',
      } as JWK);

      await expect(service.sign(appTokenPayload, 'org-id')).rejects.toThrow(
        'secretOrPrivateKey must be an asymmetric key when using RS256',
      );
    });
  });

  describe('verify', () => {
    it('should return valid decoded data for assymmetric key', async () => {
      jest.spyOn(repository, 'findOneByOrFail').mockResolvedValueOnce({
        algorithm: 'RS256',
        publicPem: RS256_PUBLIC_KEY,
      } as JWK);

      await expect(service.verify(VALID_RS_JWT_TOKEN, orgId)).resolves.toEqual({
        exp: 1930468498,
        iat: 1730382098,
        id: 'token-id',
        iss: 'superawesome',
      });
    });

    it('should return valid decoded data for symmetric key', async () => {
      jest.spyOn(repository, 'findOneByOrFail').mockResolvedValueOnce({
        algorithm: 'HS256',
        privatePem: HS256_PRIVATE_KEY,
      } as JWK);

      await expect(service.verify(VALID_HS_JWT_TOKEN, orgId)).resolves.toEqual({
        exp: 1930891111,
        iat: 1730804711,
        id: 'token-id',
        iss: 'superawesome',
      });
    });

    it('should throw for missing key id', async () => {
      await expect(service.verify(JWT_TOKEN_WITHOUT_KID, orgId)).rejects.toThrow('Matching key not found');
    });

    it('should throw for invald key id', async () => {
      jest.spyOn(repository, 'findOneByOrFail').mockRejectedValueOnce(new TypeORMError('Not Found'));

      await expect(service.verify(VALID_RS_JWT_TOKEN, orgId)).rejects.toThrow('Not Found');
    });

    it('should throw for invald public key', async () => {
      jest.spyOn(repository, 'findOneByOrFail').mockResolvedValueOnce({
        algorithm: 'RS256',
        publicPem: 'cHJpdmF0ZS1rZXk=',
      } as JWK);

      await expect(service.verify(VALID_RS_JWT_TOKEN, orgId)).rejects.toThrow(
        'secretOrPublicKey must be an asymmetric key when using RS256',
      );
    });
  });

  describe('getValidationKey', () => {
    it('should throw an error for symmetric jwk without publicPem', async () => {
      jest.spyOn(repository, 'findOneByOrFail').mockResolvedValueOnce({
        algorithm: 'RS256',
      } as JWK);

      await expect(service.getValidationKey('kid-symm', orgId)).rejects.toThrow(
        'Assymmetric algorithm should have public key for validation',
      );
    });
  });
});
