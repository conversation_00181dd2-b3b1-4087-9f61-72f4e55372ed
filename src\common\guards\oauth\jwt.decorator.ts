import { createParamDecorator, ExecutionContext } from '@nestjs/common';

import { OAuthFastifyRequest } from './types';
import { TJWT } from '../../../oauth/types';

export const JWT_TOKEN = 'freekws/inject-client-oauth';

export const JWT = createParamDecorator((_data: void, ctx: ExecutionContext): TJWT => {
  const request = ctx.switchToHttp().getRequest<OAuthFastifyRequest>();
  return request.raw.jwt;
});
