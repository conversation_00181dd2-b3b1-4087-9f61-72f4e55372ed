import { Controller, Get } from '@nestjs/common';

import { JwkRepository } from './jwk.repository';

interface JWKResponse {
  keys: Array<{
    alg: string;
    kty: string;
    use: string;
    n: string;
    e: string;
    kid: string;
  }>;
}

@Controller('v1/jwks')
export class JwkController {
  constructor(private readonly jwkRepository: JwkRepository) {}

  @Get()
  async getJwks(): Promise<JWKResponse> {
    const jwks = await this.jwkRepository.getRecentJwks();

    return {
      keys: jwks.map((jwk) => ({
        alg: jwk.algorithm,
        kty: jwk.keyType,
        use: jwk.use,
        n: jwk.modulus,
        e: jwk.exponent,
        kid: jwk.keyId,
      })),
    };
  }
}
