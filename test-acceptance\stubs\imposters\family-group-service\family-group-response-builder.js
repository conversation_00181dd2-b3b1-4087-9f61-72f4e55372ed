function familyGroupResponseBuilder(config) {
  const defaultFamily = {
    id: '0cf70cab-14a6-4678-a05d-e775c8ffb9b1',
    members: [
      {
        id: '1abc4f9e-6cba-4f0c-9e77-499d36dd7de1',
        familyGroupId: 'a0a8afa4-2f5c-4343-aa99-77d57aef626e',
        role: 'manager',
        email: '<EMAIL>',
      },
    ],
    allowedToAddManagers: true,
    allowedToAddSupervisors: true,
    allowedToAddSupervisedUnsupervised: true,
  };

  const { userId } = config.request.query;
  const familyGroups = userId === '1852416260' ? [] : [defaultFamily];

  return {
    statusCode: 200,
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      response: {
        familyGroups,
      },
    }),
  };
}
