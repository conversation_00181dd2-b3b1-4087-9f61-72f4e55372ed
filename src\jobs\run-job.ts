import { NestApplication, NestFactory } from '@nestjs/core';
import { SALogger } from '@superawesome/freekws-common-logger';

import { GenericJobRunner } from './generic-job-runner';
import { RootAppModule } from '../root-app.module';

const l = new SALogger();

(async () => {
  try {
    const app = await NestFactory.create<NestApplication>(RootAppModule);
    await app.init(); // necessary to trigger lifecycle events as listen is not called
    const dryMode = process.env.DRY_MODE !== 'false'; // true by default
    await new GenericJobRunner(app).runJob(process.argv[2], dryMode);

    process.exit(0);
  } catch (error) {
    l.error('Unexpected error:', error);
    process.exit(1);
  }
})();
