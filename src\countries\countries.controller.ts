import { Controller, Get, Query, Req, UseGuards } from '@nestjs/common';
import { ApiExtraModels, ApiOkResponse, ApiOperation, ApiTags } from '@nestjs/swagger';
import { ChildAgeQueryDTO, ChildAgeResponseDTO } from '@superawesome/freekws-classic-wrapper-common';
import { Public } from '@superawesome/freekws-nestjs-guards';
import { plainToInstance } from 'class-transformer';
import type { FastifyRequest } from 'fastify';

import { CountriesService } from './countries.service';
import { ExtractOrgEnv } from '../common/guards/inject-org-env/inject-org-env.decorator';
import { InjectOrgEnvGuard } from '../common/guards/inject-org-env/inject-org-env.guard';
import { EAPITags } from '../common/types';
import { OrgEnv } from '../org-env/org-env.entity';

@ApiExtraModels(ChildAgeResponseDTO)
@Controller('v1/countries')
export class CountriesController {
  constructor(private readonly countriesService: CountriesService) {}

  @ApiOperation({
    summary: 'Get age gate info',
    description: `Returns information on the age age of digital consent in the country the request is sent from
    (which we determine by looking up the IP address if country not specified in request). If provided an date
    of birth, it will return whether the user is considered a child or not in the country and their age. This
    endpoint is useful for building a dynamic age gate that takes into account age limits across countries.`,
  })
  @ApiTags(EAPITags.Countries)
  @ApiOkResponse({
    type: ChildAgeResponseDTO,
  })
  @Get(['child-age', 'childage'])
  @Public()
  @UseGuards(InjectOrgEnvGuard)
  async getChildAge(
    @Query() childAgeDto: ChildAgeQueryDTO,
    @Req() request: FastifyRequest,
    @ExtractOrgEnv() orgEnv: OrgEnv,
  ): Promise<ChildAgeResponseDTO> {
    const ip = request.ip;
    const response = await this.countriesService.childAge(childAgeDto, ip, {
      clientId: orgEnv.clientId,
      secret: orgEnv.clientSecret,
    });
    return plainToInstance(ChildAgeResponseDTO, response, { excludeExtraneousValues: true });
  }
}
