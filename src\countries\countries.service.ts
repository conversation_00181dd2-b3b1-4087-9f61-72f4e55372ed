import { Injectable } from '@nestjs/common';
import { ChildAgeQueryDTO, ChildAgeResponseDTO } from '@superawesome/freekws-classic-wrapper-common';
import { Span } from 'nestjs-ddtrace';

import { AgeGateService } from '../common/services/age-gate/age-gate.service';
import { IClientCredentials } from '../org-env/types';

@Injectable()
@Span()
export class CountriesService {
  constructor(private readonly ageGateService: AgeGateService) {}

  async childAge(
    { dob, country }: ChildAgeQueryDTO,
    ip: string,
    creds: IClientCredentials,
  ): Promise<ChildAgeResponseDTO> {
    const consentAgeResponse = await this.ageGateService.getConsentAgeForCountry({ ip, dob, location: country }, creds);

    return {
      country: consentAgeResponse.country,
      consentAgeForCountry: consentAgeResponse.consentAge,
      age: consentAgeResponse.userAge,
      isMinor: consentAgeResponse.underAgeOfDigitalConsent,
    };
  }
}
