import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { HealthcheckController } from './healthcheck.controller';
import { HealthcheckService } from './healthcheck.service';
import { AppTranslation } from '../app/app-translation.entity';
import { App } from '../app/app.entity';
import { CommonModule } from '../common/common.module';
import { JWK } from '../oauth/jwk.entity';
import { RefreshToken } from '../oauth/refresh-token.entity';
import { User } from '../user/user.entity';
import { Webhook } from '../webhook/webhook.entity';

@Module({
  imports: [TypeOrmModule.forFeature([App, JWK, RefreshToken, User, Webhook, AppTranslation]), CommonModule],
  providers: [HealthcheckService],
  controllers: [HealthcheckController],
})
export class HealthcheckModule {}
