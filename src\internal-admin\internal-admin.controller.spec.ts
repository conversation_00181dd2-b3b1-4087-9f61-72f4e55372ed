import { BadRequestException } from '@nestjs/common';
import { TestingModule } from '@nestjs/testing';
import { v4 as uuidv4 } from 'uuid';

import { InternalAdminController } from './internal-admin.controller';
import { InternalAdminService } from './internal-admin.service';
import { Testing } from '../common/utils';

describe('InternalAdminController', () => {
  let controller: InternalAdminController;
  let service: InternalAdminService;

  beforeEach(async () => {
    const module: TestingModule = await Testing.createModule({
      controllers: [InternalAdminController],
    });

    controller = module.get<InternalAdminController>(InternalAdminController);
    service = module.get(InternalAdminService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
    expect(service).toBeDefined();
  });

  it('updateUserDOB', async () => {
    const userId = **************;
    const dateOfBirth = '2020-10-01';
    const location = 'US';
    const orgEnvId = uuidv4();
    const spySettings = jest.spyOn(service, 'getUserSettingsToUpdateUserDOB').mockResolvedValueOnce([]);
    await expect(controller.updateUserDOB(orgEnvId, userId, { dateOfBirth, location })).resolves.toEqual([]);
    expect(spySettings).toHaveBeenCalledWith(orgEnvId, { userId, dateOfBirth, location });
  });

  describe('deleteUserAccount', () => {
    it('should call internalAdminService.deleteUserAccount with correct parameters', async () => {
      const orgEnvId = 'test-org-env-id';
      const userId = 123;
      const spy = jest.spyOn(service, 'deleteUserAccount').mockResolvedValue();

      await controller.deleteUserAccount(orgEnvId, userId, {});

      expect(spy).toHaveBeenCalledWith(orgEnvId, userId);
    });

    it('should throw BadRequestException when password is provided', async () => {
      const orgEnvId = 'test-org-env-id';
      const userId = 123;
      const spy = jest.spyOn(service, 'deleteUserAccount');

      await expect(controller.deleteUserAccount(orgEnvId, userId, { password: 'some-password' })).rejects.toThrow(
        BadRequestException,
      );

      expect(spy).not.toHaveBeenCalled();
    });
  });
});
