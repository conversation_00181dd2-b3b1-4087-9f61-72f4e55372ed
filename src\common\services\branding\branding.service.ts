import { Inject, Injectable } from '@nestjs/common';
import { KeycloakService } from '@superawesome/freekws-auth-library';
import {
  BRANDING_SERVICE_API_CLIENT_INJECT_KEY,
  BrandingDTO,
  brandingServicePlugin,
  CustomCopyDTO,
} from '@superawesome/freekws-branding-service-common';
import { Cacheable, paramsExtractor } from '@superawesome/freekws-cache-decorator';
import { NestJsClient } from '@superawesome/freekws-clients-nestjs';
import { isNil, omitBy } from 'lodash';

import { CACHEABLE_BRANDING_TTL_SECS, CACHEABLE_CUSTOMCOPY_TTL_SECS } from '../../types';

@Injectable()
export class BrandingService {
  constructor(
    @Inject(BRANDING_SERVICE_API_CLIENT_INJECT_KEY)
    private readonly brandingServiceClient: NestJsClient<typeof brandingServicePlugin>,
    private readonly keycloakService: KeycloakService,
  ) {}

  @Cacheable(CACHEABLE_BRANDING_TTL_SECS, 'getBrandings', paramsExtractor(0, 1, 2))
  public async getBrandings(
    environmentId: string,
    language: string | null,
    fallbackEnvironmentId?: string,
  ): Promise<BrandingDTO[]> {
    const token = await this.keycloakService.getUpToDateServiceAccessToken();
    const headers = {
      Authorization: `Bearer ${token}`,
    };

    // Necessary to prevent issues with branding service where it seems to treat undefined as string
    const query = omitBy(
      {
        language,
        fallbackEnvironmentId,
      },
      isNil,
    );

    const res = await this.brandingServiceClient
      .getModule('branding')
      .getBrandings({ params: { environmentId }, query }, { headers });
    return res.data.response.brandings;
  }

  @Cacheable(CACHEABLE_CUSTOMCOPY_TTL_SECS, 'getCustomCopy', paramsExtractor(0, 1))
  public async getCustomCopy(environmentId: string, language: string): Promise<CustomCopyDTO> {
    const token = await this.keycloakService.getUpToDateServiceAccessToken();
    const headers = {
      Authorization: `Bearer ${token}`,
    };

    const res = await this.brandingServiceClient
      .getModule('customCopy')
      .getCustomCopy({ params: { environmentId }, query: { language } }, { headers });
    return res.data.response;
  }
}
