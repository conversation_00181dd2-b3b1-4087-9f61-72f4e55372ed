#!/usr/bin/env bash

# Set strict mode, but use pipefail only if supported
set -eu
if [[ "${BASH_VERSINFO[0]}" -ge 4 ]]; then
  set -o pipefail
fi

# ANSI color codes for bash (works in Git Bash for Windows)
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
MAGENTA='\033[0;35m'
CYAN='\033[0;36m'
LIGHTRED='\033[1;31m'
LIGHTGREEN='\033[1;32m'
LIGHTYELLOW='\033[1;33m'
LIGHTBLUE='\033[1;34m'
LIGHTMAGENTA='\033[1;35m'
LIGHTCYAN='\033[1;36m'
NC='\033[0m' # No Color

# Variables to track child processes
declare -a POD_LOG_PIDS=()
SCRIPT_INTERRUPTED=0
MAX_STREAMS=100

# Function to display usage information
show_usage() {
  echo -e "${YELLOW}Usage: $0 <kube-context> <service-name> [namespace] [tail-lines]${NC}"
  echo -e "${YELLOW}Example: $0 minikube my-service default 100${NC}"
}

# Function to get a random color
get_random_color() {
  local colors=("${GREEN}" "${YELLOW}" "${BLUE}" "${MAGENTA}" "${CYAN}" "${LIGHTRED}" "${LIGHTGREEN}" "${LIGHTYELLOW}" "${LIGHTBLUE}" "${LIGHTMAGENTA}" "${LIGHTCYAN}")
  local random_index=$(( RANDOM % ${#colors[@]} ))
  echo "${colors[$random_index]}"
}

# Function to clean up child processes when the script exits
cleanup() {
  echo -e "\n${YELLOW}Shutting down log streams...${NC}"
  # Kill all child processes
  for pid in "${POD_LOG_PIDS[@]}"; do
    if ps -p "$pid" > /dev/null 2>&1; then
      kill -9 "$pid" 2>/dev/null || true
    fi
  done

  # In Git Bash, more aggressively kill child processes
  if [[ "$(uname)" =~ MINGW|MSYS ]]; then
    # Kill all descendant processes more aggressively for Windows
    pkill -P $$ 2>/dev/null || true
  fi

  echo -e "${GREEN}All processes cleaned up. Exiting.${NC}"
  # Ensure we really exit
  kill -9 $$ 2>/dev/null || exit 0
}

# Handle interrupts
handle_interrupt() {
  SCRIPT_INTERRUPTED=1
  echo -e "\n${YELLOW}Interrupt received, cleaning up...${NC}"
  cleanup
  exit 0
}

# Register the cleanup function for various exit signals
trap handle_interrupt INT TERM
trap cleanup EXIT

# Function to get pods for a service
get_pods_for_service() {
  local context="$1"
  local service_name="$2"
  local namespace="${3:-default}"

  echo -e "${BLUE}🔍 Finding pods for service ${service_name} in namespace ${namespace}...${NC}"

  # Get selector from service
  local selector_json
  selector_json=$(kubectl --context "$context" get service "$service_name" -n "$namespace" -o jsonpath='{.spec.selector}' 2>/dev/null) || {
    echo -e "${RED}❌ Error: Service $service_name not found in namespace $namespace${NC}"
    return 1
  }

  if [ -z "$selector_json" ]; then
    echo -e "${RED}❌ Error: No selector found for service $service_name${NC}"
    return 1
  fi

  # Parse selector into label selector string
  # For Git Bash compatibility, using a simpler approach without jq
  local label_selector=""
  # Extract key=value pairs from the selector JSON manually
  # Remove { } characters and split by commas
  selector_json="${selector_json#\{}"
  selector_json="${selector_json%\}}"

  # Split the selector by commas and build the label selector
  IFS=',' read -ra SELECTOR_PARTS <<< "$selector_json"
  for part in "${SELECTOR_PARTS[@]}"; do
    # Extract key and value, handling quotes
    key=$(echo "$part" | sed -E 's/^[ ]*"([^"]+)":.*/\1/')
    value=$(echo "$part" | sed -E 's/^[ ]*"[^"]+"[ ]*:[ ]*"([^"]+)".*/\1/')

    # Append to label selector
    if [ -n "$label_selector" ]; then
      label_selector="${label_selector},"
    fi
    label_selector="${label_selector}${key}=${value}"
  done

  if [ -z "$label_selector" ]; then
    echo -e "${RED}❌ Error: Could not parse selector for service $service_name${NC}"
    return 1
  fi

  # Get pods with the specified labels
  local pods_output
  pods_output=$(kubectl --context "$context" get pods -n "$namespace" -l "$label_selector" -o jsonpath='{range .items[*]}{.metadata.name}{","}{.metadata.namespace}{"\n"}{end}' 2>/dev/null) || {
    echo -e "${YELLOW}⚠️ Error querying pods for service ${service_name}${NC}"
    return 1
  }

  if [ -z "$pods_output" ]; then
    echo -e "${YELLOW}⚠️ No pods found for service ${service_name} in namespace ${namespace}${NC}"
    return 0
  fi

  # Count the pods by counting lines
  local pod_count
  pod_count=$(echo "$pods_output" | wc -l)
  echo -e "${GREEN}✅ Found ${pod_count} pods for service ${service_name}${NC}"

  # Output the pod information
  echo "$pods_output"
}

# Function to stream logs from a pod
stream_pod_logs() {
  local context="$1"
  local pod_name="$2"
  local namespace="$3"
  local tail_lines="${4:-50}"
  local pod_color="$5"

  echo -e "${CYAN}📋 Starting log stream for pod ${pod_name}...${NC}"

  # Start kubectl logs in the background and capture its PID
  (
    kubectl --context "$context" logs -n "$namespace" -f "$pod_name" --tail="$tail_lines" 2>&1 | while read -r line; do
      if [ "$SCRIPT_INTERRUPTED" -eq 1 ]; then
        break
      fi
      echo -e "${pod_color}[${pod_name}]${NC} $line"
    done
  ) &

  local log_pid=$!
  POD_LOG_PIDS+=("$log_pid")
}

# Function to list currently monitored pods
list_monitored_pods() {
  local pod_info_array=("$@")

  echo -e "${CYAN}🏎️ Currently monitoring pods:${NC}"
  for pod_info in "${pod_info_array[@]}"; do
    IFS=',' read -r pod_name _ <<< "$pod_info"
    echo -e "${GREEN}   - ${pod_name}${NC}"
  done
}

# Function to find new pods that aren't already being monitored
find_new_pods() {
  local existing_pods=("$@")
  local new_pods_output="$1"
  shift

  local new_pods=()
  while IFS= read -r pod_info; do
    local is_monitored=false
    for existing_pod in "${existing_pods[@]}"; do
      if [[ "$pod_info" == "$existing_pod" ]]; then
        is_monitored=true
        break
      fi
    done

    if [[ "$is_monitored" == false ]]; then
      new_pods+=("$pod_info")
    fi
  done <<< "$new_pods_output"

  # Return the new pods as a newline-separated string
  printf "%s\n" "${new_pods[@]}"
}

# Add an exit command
exit_script() {
  echo -e "${YELLOW}Exiting script as requested...${NC}"
  SCRIPT_INTERRUPTED=1
  cleanup
  exit 0
}

# Main function
main() {
  # Check arguments
  if [ $# -lt 2 ]; then
    echo -e "${RED}❌ Missing required arguments!${NC}"
    show_usage
    exit 1
  fi

  local context="$1"
  local service_name="$2"
  local namespace="${3:-default}"
  local tail_lines="${4:-50}"

  echo -e "${CYAN}🚀 Starting log streaming for service ${service_name} in context ${context}...${NC}"

  # Get initial list of pods
  local pods_output
  pods_output=$(get_pods_for_service "$context" "$service_name" "$namespace") || {
    echo -e "${RED}❌ Error getting pods for service ${service_name}${NC}"
    exit 1
  }

  # If no pods found, exit
  if [ -z "$pods_output" ]; then
    echo -e "${YELLOW}⚠️ No pods found for service ${service_name}. Exiting.${NC}"
    exit 0
  fi

  # Convert pod output to array for easier handling
  local pod_info_array=()
  while IFS= read -r line; do
    if [ -n "$line" ]; then
      pod_info_array+=("$line")
    fi
  done <<< "$pods_output"

  echo -e "${GREEN}✅ Found ${#pod_info_array[@]} pods. Starting log streams (up to ${MAX_STREAMS})...${NC}"

  # Start streaming logs for up to MAX_STREAMS pods
  for idx in "${!pod_info_array[@]}"; do
    if [ "$idx" -ge "$MAX_STREAMS" ]; then
      break
    fi
    IFS=',' read -r pod_name pod_namespace <<< "${pod_info_array[$idx]}"
    pod_color=$(get_random_color)
    stream_pod_logs "$context" "$pod_name" "$pod_namespace" "$tail_lines" "$pod_color"
  done

  # Interactive command loop
  echo -e "\n${CYAN}📢 Log streaming started! Press Ctrl+C to exit.${NC}"
  echo -e "${CYAN}💡 Type \"pods\" to list current pods, \"refresh\" to check for new pods, or \"exit\" to quit.${NC}"

  # Read commands with a timeout to check for interrupts
  while [ "$SCRIPT_INTERRUPTED" -eq 0 ]; do
    read -t 1 command || continue

    case "$command" in
      "pods")
        list_monitored_pods "${pod_info_array[@]}"
        ;;
      "refresh")
        echo -e "${CYAN}🔄 Refreshing pod list...${NC}"
        local new_pods_output
        new_pods_output=$(get_pods_for_service "$context" "$service_name" "$namespace") || {
          echo -e "${YELLOW}⚠️ Error during refresh${NC}"
          continue
        }

        if [ -z "$new_pods_output" ]; then
          echo -e "${YELLOW}⚠️ No pods found during refresh${NC}"
          continue
        fi

        # Find pods that aren't already being monitored
        local actually_new_pods
        actually_new_pods=$(find_new_pods "$new_pods_output" "${pod_info_array[@]}")

        if [ -n "$actually_new_pods" ]; then
          local new_pod_count
          new_pod_count=$(echo -e "$actually_new_pods" | wc -l)
          echo -e "${GREEN}✅ Found ${new_pod_count} new pods. Attempting log streams (up to ${MAX_STREAMS})...${NC}"

          # Stream new pods only while total streams < MAX_STREAMS
          while IFS= read -r pod_info; do
            if [ -z "$pod_info" ]; then
              continue
            fi

            # If we've already reached MAX_STREAMS, skip starting any more
            if [ "${#POD_LOG_PIDS[@]}" -ge "$MAX_STREAMS" ]; then
              echo -e "${YELLOW}⚠️ Already streaming from ${MAX_STREAMS} pods; skipping additional pods.${NC}"
              break
            fi

            IFS=',' read -r pod_name pod_namespace <<< "$pod_info"
            pod_color=$(get_random_color)
            stream_pod_logs "$context" "$pod_name" "$pod_namespace" "$tail_lines" "$pod_color"
            pod_info_array+=("$pod_info")
          done <<< "$actually_new_pods"
        else
          echo -e "${YELLOW}ℹ️ No new pods found.${NC}"
        fi
        ;;
      "exit")
        exit_script
        ;;
      "")
        # Do nothing on empty input
        ;;
      *)
        echo -e "${YELLOW}Unknown command: ${command}${NC}"
        echo -e "${CYAN}Available commands: \"pods\", \"refresh\", \"exit\"${NC}"
        ;;
    esac
  done
}

# Run the main function
main "$@"
