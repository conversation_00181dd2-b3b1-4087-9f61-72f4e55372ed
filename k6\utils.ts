// Common utility functions
import { Response } from 'k6/http';
import { check } from 'k6';

import crypto from 'k6/crypto';
import encoding from 'k6/encoding';


import { DEFAULT_HEADERS } from './endpoints';

export function addAuthHeader(token: string, headers = {}) {
    return {
        ...DEFAULT_HEADERS,
        ...headers,
        'Authorization': `Bearer ${token}`
    };
}

export function validateResponse(res: Response, options = {
    expectedStatus: 200,
    maxDuration: 500
}) {
    const { expectedStatus, maxDuration } = options;
    return check(res, {
        [`status is ${expectedStatus}`]: (r) => r.status === expectedStatus,
        [`response time is less than ${maxDuration}ms`]: (r) => r.timings.duration < maxDuration,
    });
}

export function generateKwsSignature(payload: any, secret: string): string {
    try {
        const timestamp = Date.now();
        const payloadStr = JSON.stringify(payload);
        const dataToSign = `${timestamp}.${payloadStr}`;

        const hmac = crypto.hmac('sha256', secret, dataToSign, 'hex');

        return `t=${timestamp},v1=${hmac}`;
    } catch (error) {
        console.error('Failed to generate KWS signature:', error);
        return 'invalid-signature-generation-failed';
    }
}