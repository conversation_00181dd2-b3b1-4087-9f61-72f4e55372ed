import { MigrationInterface, QueryRunner } from 'typeorm';

export class DropSingleColumnUniqueIndexesOnRefreshTokensAndUser1741007388246 implements MigrationInterface {
  name = 'DropSingleColumnUniqueIndexesOnRefreshTokensAndUser1741007388246';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            DROP INDEX "public"."IDX_c31d0a2f38e6e99110df62ab0a"
        `);
    await queryRunner.query(`
            CREATE UNIQUE INDEX "token_orgEnvId" ON "refresh_token" ("token", "orgEnvId")
        `);
    await queryRunner.query(`
            DROP INDEX "public"."IDX_bfb14a1cc741c1fb77d99e7110"
        `);
    await queryRunner.query(`
            CREATE UNIQUE INDEX "user_refreshPasswordToken_orgEnvId" ON "user" ("passwordResetToken", "orgEnvId")
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            DROP INDEX "public"."token_orgEnvId"
        `);
    await queryRunner.query(`
            CREATE UNIQUE INDEX "IDX_c31d0a2f38e6e99110df62ab0a" ON "refresh_token" ("token")
        `);
    await queryRunner.query(`
            DROP INDEX "public"."user_refreshPasswordToken_orgEnvId"
        `);
    await queryRunner.query(`
            CREATE UNIQUE INDEX "IDX_bfb14a1cc741c1fb77d99e7110" ON "user" ("passwordResetToken")
        `);
  }
}
