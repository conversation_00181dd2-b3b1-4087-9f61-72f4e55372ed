import { Test, TestingModule } from '@nestjs/testing';
import { EWebhookName } from '@superawesome/freekws-queue-messages/webhook/webhook.dto';

import { KwsSignatureService } from './kws-signature.service';
import { KwsSignatureServiceSignatureFormatError, KwsSignatureServiceSignatureVerificationError } from './types';
import { Utils } from '../../../../test/utils';
import { OrgLevelWebhookPayload } from '../../../webhook/types';
import { CallbackService } from '../callback/callback.service';
import { DevPortalService } from '../dev-portal/dev-portal.service';

describe('KwsSignatureService', () => {
  const mockDevPortalService = {
    getWebhookSecret: jest.fn(),
  };

  const mockCallbackService = {
    getWebhookAtOrgLevel: jest.fn(),
  };

  const MOCK_SECRET = 'mockSecret';
  const ORG_ENV_ID = 'orgEnv';
  const TIMESTAMP = 1734529731207;

  let defaultBody: OrgLevelWebhookPayload;
  let service: KwsSignatureService;

  beforeEach(async () => {
    mockDevPortalService.getWebhookSecret.mockResolvedValue(MOCK_SECRET);

    mockCallbackService.getWebhookAtOrgLevel.mockResolvedValue({
      secrets: [{ value: MOCK_SECRET }],
    });

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        KwsSignatureService,
        { provide: DevPortalService, useValue: mockDevPortalService },
        { provide: CallbackService, useValue: mockCallbackService },
      ],
    }).compile();

    service = module.get<KwsSignatureService>(KwsSignatureService) as KwsSignatureService;

    defaultBody = {
      name: EWebhookName.FAMILIES_GUARDIAN_REQUEST_EXPIRED,
      time: Date.now(),
      orgId: '111',
      payload: 'payload-data',
    };
  });

  it('should return true when signature is valid', async () => {
    const kwsSignature = `t=${TIMESTAMP},v1=${Utils.generateKwsSignature(TIMESTAMP, defaultBody, MOCK_SECRET)}`;

    const result = await service.verify(kwsSignature, defaultBody, ORG_ENV_ID);

    expect(result).toBe(true);
  });

  describe('should throw error when', () => {
    it('secret was not found', async () => {
      const kwsSignature = `t=${TIMESTAMP},v1=${Utils.generateKwsSignature(TIMESTAMP, defaultBody, 'secretKey')}`;

      // Mock to return undefined to trigger the error
      mockCallbackService.getWebhookAtOrgLevel.mockRejectedValueOnce(
        new KwsSignatureServiceSignatureVerificationError(),
      );

      const result = service.verify(kwsSignature, defaultBody, ORG_ENV_ID);

      await expect(result).rejects.toBeInstanceOf(KwsSignatureServiceSignatureVerificationError);
    });

    it('input is in incorrect format', async () => {
      const invalidSignature = `t=invalid,v1=invalid`;

      const result = service.verify(invalidSignature, defaultBody, ORG_ENV_ID);

      await expect(result).rejects.toBeInstanceOf(KwsSignatureServiceSignatureFormatError);
    });

    it('signatures do not match due to secret', async () => {
      const kwsSignature = `t=${TIMESTAMP},v1=${Utils.generateKwsSignature(TIMESTAMP, defaultBody, 'INVALID_SECRET')}`;

      mockCallbackService.getWebhookAtOrgLevel.mockResolvedValueOnce({
        secrets: [{ value: 'DIFFERENT_SECRET' }],
      });

      const result = await service.verify(kwsSignature, defaultBody, ORG_ENV_ID);
      expect(result).toBe(false);
    });

    it('signatures do not match due to timestamp', async () => {
      const kwsSignature = `t=2004529731500,v1=${Utils.generateKwsSignature(TIMESTAMP, defaultBody, MOCK_SECRET)}`;

      const result = await service.verify(kwsSignature, defaultBody, ORG_ENV_ID);
      expect(result).toBe(false);
    });
  });
});
