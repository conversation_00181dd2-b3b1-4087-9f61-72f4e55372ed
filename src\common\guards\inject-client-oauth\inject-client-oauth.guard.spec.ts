import { BadRequestException, ExecutionContext } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { TypeORMError } from 'typeorm';

import { InjectClientOauthGuard } from './inject-client-oauth.guard';
import { AppService } from '../../../app/app.service';
import { OrgEnvService } from '../../../org-env/org-env.service';

const mockReflector = {
  get: jest.fn(),
};

const mockAppRepo = {
  getAppOauthClient: jest.fn(),
};

const mockOrgEnvService = {
  getOrgEnvFromRequest: jest.fn(),
};

const mockGetRequest = jest.fn();

const context = {
  getHandler: jest.fn(),
  switchToHttp: () => ({
    getRequest: mockGetRequest,
  }),
} as unknown as ExecutionContext;

describe('InjectClientOauthGuard', () => {
  const guard = new InjectClientOauthGuard(
    mockReflector as unknown as Reflector,
    mockAppRepo as unknown as AppService,
    mockOrgEnvService as unknown as OrgEnvService,
  );

  beforeEach(() => {
    jest.resetAllMocks();
  });

  it('should return true for disabled ruard', async () => {
    await expect(guard.canActivate(context)).resolves.toBeTruthy();
  });

  it('should return true for valid params', async () => {
    mockOrgEnvService.getOrgEnvFromRequest.mockResolvedValueOnce({ id: '1' });
    mockReflector.get.mockReturnValueOnce(true);
    mockGetRequest.mockReturnValueOnce({
      headers: {
        authorization: 'Basic dXNlcjpwYXNz',
      },
      raw: {},
      body: {
        scope: 'app',
      },
    });
    mockAppRepo.getAppOauthClient.mockResolvedValueOnce({
      id: 123456,
      clientId: 'client-id',
      orgEnvId: 1233456,
    });
    await expect(guard.canActivate(context)).resolves.toBeTruthy();
  });

  it('should throw an error for request without client id', async () => {
    mockReflector.get.mockReturnValueOnce(true);
    mockGetRequest.mockReturnValueOnce({
      headers: {},
      body: {},
    });

    await expect(guard.canActivate(context)).rejects.toBeInstanceOf(BadRequestException);
  });

  it('should throw an error for request without client secret', async () => {
    mockReflector.get.mockReturnValueOnce(true);
    mockGetRequest.mockReturnValueOnce({
      headers: {},
      body: {
        client_id: 'client-id',
      },
    });

    await expect(guard.canActivate(context)).rejects.toBeInstanceOf(BadRequestException);
  });

  it('should throw an error for request with invalid client credentials', async () => {
    mockReflector.get.mockReturnValueOnce(true);
    mockGetRequest.mockReturnValueOnce({
      headers: {
        authorization: ['Basic dXNlcjpwYXNz'],
      },
      raw: {},
    });
    mockAppRepo.getAppOauthClient.mockRejectedValueOnce(new TypeORMError('Not Found'));
    await expect(guard.canActivate(context)).rejects.toBeInstanceOf(BadRequestException);
  });
});
