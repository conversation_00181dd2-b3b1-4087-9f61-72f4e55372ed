import { DataTable, setDefaultTimeout } from '@cucumber/cucumber';
import {
  RequestUserPermissionsResponseDTO,
  UserCreateResponseDTO,
  GetUserResponseDTO,
} from '@superawesome/freekws-classic-wrapper-common';
import { SALogger } from '@superawesome/freekws-common-logger';
import { HttpResponse } from '@superawesome/freekws-http-nestjs-service';
import { Tiso31662, Tiso6391 } from '@superawesome/freekws-regional-config';
import assert from 'assert';
import { binding, given, then, when } from 'cucumber-tsflow';

import { IAppOauthClientCredentials } from '../../src/app/types';
import { UATUtils } from '../utils';

setDefaultTimeout(60 * 1000);
@binding([SALogger])
export class AppUsersSteps {
  private credentials: IAppOauthClientCredentials;
  public appId: number;
  public oauthToken: string;

  private createUserResponse: HttpResponse<UserCreateResponseDTO>;

  private requestPermissionsParams: {
    parentEmail?: string;
    dateOfBirth?: string;
    permissions?: string[];
  };
  private requestPermissionsResponse: HttpResponse<RequestUserPermissionsResponseDTO>;

  private updateParentEmailResponse: HttpResponse;

  private getUserInfoResponse: HttpResponse<GetUserResponseDTO>;
  private reviewPermissionsResponse: HttpResponse;

  @given('app with credentials')
  setAppCreds(table: DataTable) {
    const dataHash = table.rowsHash() as IAppOauthClientCredentials & { appId: string };
    this.appId = Number(dataHash.appId);
    this.credentials = { clientId: dataHash.clientId, secret: dataHash.secret };
  }

  @when('developer requests oauth token')
  async getOAuthToken() {
    const response = await UATUtils.buildClassicWrapperClient().request({
      url: '/oauth/token',
      method: 'POST',
      data: {
        client_id: this.credentials.clientId,
        client_secret: this.credentials.secret,
        scope: 'app',
        grant_type: 'client_credentials',
      },
      headers: {
        'x-forwarded-host': 'test.com',
      },
    });

    assert.equal(response.status, 200);
    assert.notEqual(response.data.access_token, undefined);

    this.oauthToken = response.data.access_token;
  }

  @when(
    'developer create user for app with country {string}, language {string}, dob {string}, email {string}, parentEmail {string}, permissions {string}',
  )
  async createUser(
    country: Tiso31662,
    language: Tiso6391,
    dateOfBirth: string,
    email: string,
    parentEmail: string,
    permissions: string,
  ) {
    this.createUserResponse = await UATUtils.buildClassicWrapperClient().request({
      url: `/v2/apps/${this.appId}/users`,
      method: 'POST',
      headers: {
        Authorization: `Bearer ${this.oauthToken}`,
        'x-forwarded-host': 'test.com',
      },
      data: {
        country,
        language,
        dateOfBirth,
        parentEmail,
        permissions: permissions ? permissions.split(',') : undefined,
      },
    });
  }

  @when('developer requests permissions {string} for user with id {string} and parentEmail {string}')
  async requestPermissions(rawPermissions: string, userId: string, parentEmail: string) {
    this.requestPermissionsParams = {
      permissions: rawPermissions?.split(','),
      parentEmail: parentEmail,
    };

    this.requestPermissionsResponse = await UATUtils.buildClassicWrapperClient().request({
      url: `/v2/apps/${this.appId}/users/${userId}/request-permissions`,
      method: 'POST',
      headers: {
        Authorization: `Bearer ${this.oauthToken}`,
        'x-forwarded-host': 'test.com',
      },
      data: this.requestPermissionsParams,
    });
  }

  @when('developer requests update parent email for user {string} to {string}')
  async setParentUpdateParams(userId: string, newParentEmail: string) {
    this.updateParentEmailResponse = await UATUtils.buildClassicWrapperClient().request({
      url: `/v2/apps/${this.appId}/users/${userId}/update-parent-email`,
      method: 'POST',
      headers: {
        Authorization: `Bearer ${this.oauthToken}`,
        'x-forwarded-host': 'test.com',
      },
      data: { parentEmail: newParentEmail },
    });
  }

  @when('developer requests profile for user with id {string}')
  async getUserInfo(userId: string) {
    this.getUserInfoResponse = await UATUtils.buildClassicWrapperClient().request({
      url: `/v2/apps/${this.appId}/users/${userId}`,
      method: 'GET',
      headers: {
        Authorization: `Bearer ${this.oauthToken}`,
        'x-forwarded-host': 'test.com',
      },
    });
  }

  @when('developer reviews permissions for user with id {string}')
  async reviewPermissions(userId: string) {
    this.reviewPermissionsResponse = await UATUtils.buildClassicWrapperClient().request({
      url: `/v2/apps/${this.appId}/users/${userId}/review-permissions`,
      method: 'POST',
      headers: {
        Authorization: `Bearer ${this.oauthToken}`,
        'x-forwarded-host': 'test.com',
      },
    });
  }

  @then('new user values is returned')
  async checkUserCreateResult() {
    assert.equal(this.createUserResponse.status, 201);
    assert.equal(this.createUserResponse.data.id > 100000000, true);
  }

  @then('user is minor: {string}')
  checkMajority(isMinor: string) {
    assert.equal(this.createUserResponse.data.isMinor, isMinor === 'true' ? true : false);
  }

  @then('requested permissions returned')
  async checkRequestedPermissions() {
    assert.equal(this.requestPermissionsResponse.status, 200);
    assert.equal(
      this.requestPermissionsParams.permissions?.length,
      Object.keys(this.requestPermissionsResponse.data.permissions).length,
    );
  }

  @then('the parent email is updated')
  async checkEmailUpdateResponse() {
    assert.equal(this.updateParentEmailResponse.status, 204);
  }

  @then('user profile returned')
  checkUserProfileResponseCode() {
    assert.equal(this.getUserInfoResponse.status, 200);
    assert.equal(!!this.getUserInfoResponse.data.createdAt, true);
  }

  @then('user profile has verified parent state')
  checkUserParentState() {
    assert.deepStrictEqual(this.getUserInfoResponse.data.parentState, {
      deleted: false,
      expired: false,
      idVerified: false,
      rejected: false,
      verified: false,
    });
  }
}
