import { AxiosError } from 'axios';

import { isAxiosError } from './utils';

describe('isAxiosError', () => {
  it('should return true for a valid Axios error', () => {
    const axiosError = new AxiosError('Network Error', 'NETWORK_ERROR');
    expect(isAxiosError(axiosError)).toBe(true);
  });

  it('should return false for null', () => {
    expect(isAxiosError(null)).toBe(false);
  });

  it('should return false for undefined', () => {
    expect(isAxiosError(void 0)).toBe(false);
  });

  it('should return false for primitive values', () => {
    expect(isAxiosError(123)).toBe(false);
    expect(isAxiosError('string')).toBe(false);
    expect(isAxiosError(true)).toBe(false);
  });

  it('should return false for objects without name property', () => {
    const error = { message: 'Not an axios error' };
    expect(isAxiosError(error)).toBe(false);
  });

  it('should return false for objects with incorrect name value', () => {
    const error = { name: 'Error', message: 'Not an axios error' };
    expect(isAxiosError(error)).toBe(false);
  });

  it('should return true for objects with name="AxiosError"', () => {
    const mockAxiosError = {
      name: 'AxiosError',
      message: 'Error message',
      isAxiosError: true,
      config: {},
      response: {
        data: {},
        status: 400,
        statusText: 'Bad Request',
        headers: {},
        config: {},
      },
    };
    expect(isAxiosError(mockAxiosError)).toBe(true);
  });
});
