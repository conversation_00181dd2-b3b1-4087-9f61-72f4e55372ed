import { CanActivate, ExecutionContext, Type } from '@nestjs/common';
import { ModuleRef, Reflector } from '@nestjs/core';
import { FastifyRequest } from 'fastify';

import { CHECK_POLICIES_TOKEN } from './policies.decorator';
import { <PERSON><PERSON>y<PERSON>and<PERSON>, TPolicyHandler, TPolicyHandlerWithInjectables } from './types';

export class PoliciesGuard implements CanActivate {
  constructor(private readonly reflector: Reflector, private readonly moduleRef: ModuleRef) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const policyHandlers = this.reflector.get<TPolicyHandler[]>(CHECK_POLICIES_TOKEN, context.getHandler());

    if (!policyHandlers || policyHandlers.length === 0) {
      return true;
    }

    const request = context.switchToHttp().getRequest();

    for (const policyHandler of policyHandlers) {
      let result = false;
      for (const handler of Array.isArray(policyHandler) ? policyHandler : [policyHandler]) {
        result = (await this.execPolicyHandler(handler, request)) as boolean;
        if (result) {
          break;
        }
      }
      if (!result) {
        return false;
      }
    }

    return true;
  }

  private async execPolicyHandler(
    handler: TPolicyHandler,
    request: FastifyRequest,
    nested = false,
  ): Promise<boolean | TPolicyHandler> {
    if (typeof handler === 'function') {
      return handler(request);
    }
    if ((handler as TPolicyHandlerWithInjectables).useClass) {
      const handlerWithInjectables = handler as TPolicyHandlerWithInjectables;
      const injectables = handlerWithInjectables.inject;

      const services = [];
      for (const injectableOrPolicy of injectables) {
        if (typeof (injectableOrPolicy as IPolicyHandler).handle === 'function') {
          services.push(injectableOrPolicy);
        } else if ((injectableOrPolicy as TPolicyHandlerWithInjectables).useClass) {
          services.push(await this.execPolicyHandler(injectableOrPolicy as TPolicyHandler, request, true));
        } else {
          services.push(this.moduleRef.get(injectableOrPolicy as Type, { strict: false }));
        }
      }
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const classConstructor: any = handlerWithInjectables.useClass;
      const handlerInstance = new classConstructor(...services) as IPolicyHandler;
      if (nested) {
        return handlerInstance;
      } else {
        return handlerInstance.handle(request);
      }
    }
    return (handler as IPolicyHandler).handle(request);
  }
}
