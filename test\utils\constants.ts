import { AgeGateDTO } from '@superawesome/freekws-agegate-api-common';

export const UUID_REGEX = /^[\da-f]{8}(?:-[\da-f]{4}){3}-[\da-f]{12}$/;
export const TEST_DATABASE_URL = '***************************************/postgres';

export const ageGateResponse: AgeGateDTO = {
  country: 'US',
  consentAge: 14,
};

export const RS256_PRIVATE_KEY =
  '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';
export const RS256_PUBLIC_KEY =
  'LS0tLS1CRUdJTiBQVUJMSUMgS0VZLS0tLS0KTUlJQ0lUQU5CZ2txaGtpRzl3MEJBUUVGQUFPQ0FnNEFNSUlDQ1FLQ0FnQmQyWmtqL1NnYStaOCszOGlONHRDSApncnl2UHNyTmpQT2dvYkROWGpXRFZkVlF5VnB0T2tJNEdoZWNQa1hRME1hak9zQXBrakVGK21iUWk4ZG9URkNiCmhubHc3TWJuQ0FoRDJvS0RwcWd1MVcrSmpxRWlhV281eUtEMkdGYTJZVzVScmMvK1o2dU9JK2FHdzgxUlcxdC8KSHFOOGc1Tjh5N1RDQmZ4dkdTZURUdGJWWHhuM2hmYStvYm5ucUo5NWkwaWxDdnBvRXpvUENPVjNxdVBzVlJtaAo2MjRDWmZHYUhhL0hHd1FlallWaGdIb3pqWlJKQU9sTzFhdnlMMGVIbVlSb0JaaEFYK3VVN21vaStyY3J1NGZRClYrdHZ6bkt2cW45RTBvb2YxRU0wYy9OeWdIZmlmcWs1aXBZV0hoTzFnNUR5VWxqckI1aVdYMnFGYXZFdFE1VTkKMVlxZXo3RWk0Z1ZWRm5CK0RUREhKRTJzcGplNERtVHR2QXFRVTJ4Uk9yRlM5QUpiK2JhTFBVRlh0bWpobFFyKwpaUXczVnhxSzVqM3NVdytUOUswRG82UkczUGtFNG40eDNrY0RsdGdZSFcyYzYvZy9tMWRwUWU3NVU1N2ZSTXkyCjlvWEdLRmIwdXUrTElSdVFFYTZySTJPa3JBSkY0eWxyTklsZEpneTRtbGJubzVueUxiamdwRVV0SnVuTENvbDYKOXIzbmdyMTJOdU5JdkFERGNhTmczb2VrOWdvYmV2QTRaOHVVRGo3YzdlVDliVFMwc004Y2o1ckh0T2V5dUk2RwpmSTJZaTVEVTVUZ1J2bnBqSW5xREEzc0swQUcraVljZmQzNVMzeG82VDVUNnljV2xRcGV6Uit2a3Z1U1lxV1IzCmdsLytRUHlldGp1S2VacjVKU3dyc3dJREFRQUIKLS0tLS1FTkQgUFVCTElDIEtFWS0tLS0t';
export const VALID_RS_JWT_TOKEN =
  '*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************';
export const JWT_TOKEN_WITHOUT_KID =
  '*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************';
export const HS256_PRIVATE_KEY = 'dG9wLXNlY3JldA==';
export const VALID_HS_JWT_TOKEN =
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjEyMzQ1NiJ9.eyJpZCI6InRva2VuLWlkIiwiaWF0IjoxNzMwODA0NzExLCJleHAiOjE5MzA4OTExMTEsImlzcyI6InN1cGVyYXdlc29tZSJ9.G7heJ2aQuwWg-KV64NmwMre-_o8ckgtAbTM9DsGqdmI';

export const TEST_APP_ID = 1852416263;
export const TEST_APP_NAME = 'test-app';
export const OTHER_APP_NAME = 'another-app';
export const TEST_APP_HOST = 'test.com';
export const MOBILE_APP_SECRET = 'secret-mobile-morph-ball';
export const TEST_APP_CLIENT_ID = 'b3e00544-5b64-48ab-a7a9-48e1a1dfa062';
export const APP_BASIC_AUTH = 'Basic YjNlMDA1NDQtNWI2NC00OGFiLWE3YTktNDhlMWExZGZhMDYyOnRvcC1zZWNyZXQ=';
export const ORG_ENV_ID = '9376c19c-66d7-48d9-a3fe-06bd0de08693';
