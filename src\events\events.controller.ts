import * as Amplitude from '@amplitude/node';
import { Body, Controller, Headers, HttpCode, HttpStatus, Post, UnauthorizedException } from '@nestjs/common';
import { ApiOkResponse, ApiOperation, ApiTags } from '@nestjs/swagger';
import { ChildAgeResponseDTO } from '@superawesome/freekws-classic-wrapper-common';

import { AnalyticsEventsDataDTO } from './events.dto';
import { AmplitudeService } from '../amplitude/amplitude.service';
import { ConfigService } from '../common/services/config/config.service';
import { EAPITags } from '../common/types';

@Controller()
export class EventsController {
  constructor(private readonly configService: ConfigService, private readonly amplitudeService: AmplitudeService) {}

  @ApiOperation({
    summary: 'Forward events to the FreeKWS Analytics service',
    description: `Forwards events to the FreeKWS Analytics service`,
  })
  @ApiTags(EAPITags.Events)
  @ApiOkResponse({
    type: ChildAgeResponseDTO,
  })
  @HttpCode(HttpStatus.OK)
  @Post('/v2/events')
  async eventsV2(@Body() body: AnalyticsEventsDataDTO, @Headers('Authorization') authString: string) {
    if (this.configService.getConfig().analyticsServiceAuthString !== authString) {
      throw new UnauthorizedException('Invalid auth string');
    }

    const { events, session, time, geo } = body;
    const userSession = session ?? {};
    userSession.id = session?.id ?? 'FFFFFFFF-FFFF-FFFF-FFFF-FFFFFFFFFFFF';
    userSession.userId = session?.userId ?? 'NoUserIdProvided';
    userSession.user = session?.user ?? {};

    const eventTime = time ?? Date.now();
    const eventGeo = geo ?? 'NoLocationProvided';

    const amplitudeEvents = events.map<Amplitude.Event>((event) => ({
      user_id: userSession.userId + '',
      event_type: event.name,
      event_properties: event.data,
      user_properties: {
        ...userSession,
        repository: 'freekws-classic-wrapper-backend',
      },
      time: eventTime,
      country: eventGeo,
    }));

    for (const event of amplitudeEvents) {
      await this.amplitudeService.sendToAmplitude(event);
    }
  }
}
