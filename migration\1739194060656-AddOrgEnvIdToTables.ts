import { MigrationInterface, QueryRunner } from "typeorm";

/**
 * Migration that fixes the current state of the database in relation to the orgEnvId column.
 * Since it is in a very messy and incomprehensible state.
 */
export class AddOrgEnvIdToTables1739194060656 implements MigrationInterface {
    name = 'AddOrgEnvIdToTables1739194060656'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "app_translation" DROP CONSTRAINT "FK_287389edc365c4d1bcd1041ab24"
        `);
        await queryRunner.query(`
            ALTER TABLE "activation" DROP CONSTRAINT "FK_2c267e6a328184f8b1d5346fea1"
        `);
        await queryRunner.query(`
            ALTER TABLE "activation" DROP CONSTRAINT "FK_d7b6bac581603cff0accfd16c56"
        `);
        await queryRunner.query(`
            ALTER TABLE "webhook" DROP CONSTRAINT "FK_f49964ba6614aba451bc1dd027b"
        `);
        await queryRunner.query(`
            ALTER TABLE "refresh_token" DROP CONSTRAINT "FK_665a6d621b53a4479de3ff1237d"
        `);
        await queryRunner.query(`
            ALTER TABLE "refresh_token" DROP CONSTRAINT "FK_92f2908f07f30cc7641d6107e55"
        `);

        await queryRunner.query(`
            ALTER TABLE "activation" DROP COLUMN "userOrgEnv"
        `);
        await queryRunner.query(`
            ALTER TABLE "activation"
                RENAME COLUMN "appOrgEnv" TO "orgEnvId"
        `);
        await queryRunner.query(`
            ALTER TABLE "activation" DROP CONSTRAINT "PK_bb655734797dc39eddfa0fcca92"
        `);
        await queryRunner.query(`
            ALTER TABLE "activation"
            ADD CONSTRAINT "PK_7f8d537472523212bf095acb3ad" PRIMARY KEY ("id", "orgEnvId")
        `);

        await queryRunner.query(`
            ALTER TABLE "refresh_token" DROP COLUMN "appOrgEnv"
        `);
        await queryRunner.query(`
            ALTER TABLE "refresh_token"
                RENAME COLUMN "userOrgEnv" TO "orgEnvId"
        `);
        await queryRunner.query(`
            ALTER TABLE "refresh_token" DROP CONSTRAINT "PK_b575dd3c21fb0831013c909e7fe"
        `);
        await queryRunner.query(`
            ALTER TABLE "refresh_token"
            ADD CONSTRAINT "PK_9d9239e21736fef205b3e0ce37f" PRIMARY KEY ("id", "orgEnvId")
        `);

        await queryRunner.query(`
            ALTER TABLE "app_translation"
                RENAME COLUMN "appOrgEnv" TO "orgEnvId"
        `);
        await queryRunner.query(`
            ALTER TABLE "app_translation" DROP CONSTRAINT "PK_0a1a7d59f78dc64e527ab180e80"
        `);
        await queryRunner.query(`
            ALTER TABLE "app_translation"
            ADD CONSTRAINT "PK_c8d028ad447052a757287ae8d91" PRIMARY KEY ("id", "orgEnvId")
        `);

        await queryRunner.query(`
            ALTER TABLE "webhook" DROP COLUMN "appOrgEnv"
        `);
        await queryRunner.query(`
            ALTER TABLE "webhook" DROP CONSTRAINT "PK_e6765510c2d078db49632b59020"
        `);
        await queryRunner.query(`
            ALTER TABLE "webhook"
            ADD CONSTRAINT "PK_2b0218bbe54cf898a596645cdbc" PRIMARY KEY ("id", "orgEnvId")
        `);

        await queryRunner.query(`
            ALTER TABLE "app_translation"
            ALTER COLUMN "orgEnvId"
            SET NOT NULL
        `);
        await queryRunner.query(`
            ALTER TABLE "webhook" DROP CONSTRAINT "FK_7ef492f8898bb08cd663e4fa15e"
        `);
        await queryRunner.query(`
            ALTER TABLE "webhook"
            ALTER COLUMN "orgEnvId"
            SET NOT NULL
        `);
        await queryRunner.query(`
            ALTER TABLE "app_translation"
            ADD CONSTRAINT "FK_5af0040c7cadd700bc5413f6b08" FOREIGN KEY ("appId", "orgEnvId") REFERENCES "app"("id", "orgEnvId") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "activation"
            ADD CONSTRAINT "FK_fd3609535b844eec1cbc4f88c7f" FOREIGN KEY ("appId", "orgEnvId") REFERENCES "app"("id", "orgEnvId") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "activation"
            ADD CONSTRAINT "FK_1f69cdad74ce54d97a35ac89ec9" FOREIGN KEY ("userId", "orgEnvId") REFERENCES "user"("id", "orgEnvId") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "webhook"
            ADD CONSTRAINT "FK_701b67b8f1eac2cb20b3fa2cf53" FOREIGN KEY ("appId", "orgEnvId") REFERENCES "app"("id", "orgEnvId") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "webhook"
            ADD CONSTRAINT "FK_7ef492f8898bb08cd663e4fa15e" FOREIGN KEY ("orgEnvId") REFERENCES "org_env"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "refresh_token"
            ADD CONSTRAINT "FK_a8feec0e8cdd14dbf4ed6f4d768" FOREIGN KEY ("userId", "orgEnvId") REFERENCES "user"("id", "orgEnvId") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "refresh_token"
            ADD CONSTRAINT "FK_6622c272c93597f3048654d5425" FOREIGN KEY ("appId", "orgEnvId") REFERENCES "app"("id", "orgEnvId") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "refresh_token" DROP CONSTRAINT "FK_6622c272c93597f3048654d5425"
        `);
        await queryRunner.query(`
            ALTER TABLE "refresh_token" DROP CONSTRAINT "FK_a8feec0e8cdd14dbf4ed6f4d768"
        `);
        await queryRunner.query(`
            ALTER TABLE "webhook" DROP CONSTRAINT "FK_7ef492f8898bb08cd663e4fa15e"
        `);
        await queryRunner.query(`
            ALTER TABLE "webhook" DROP CONSTRAINT "FK_701b67b8f1eac2cb20b3fa2cf53"
        `);
        await queryRunner.query(`
            ALTER TABLE "activation" DROP CONSTRAINT "FK_1f69cdad74ce54d97a35ac89ec9"
        `);
        await queryRunner.query(`
            ALTER TABLE "activation" DROP CONSTRAINT "FK_fd3609535b844eec1cbc4f88c7f"
        `);
        await queryRunner.query(`
            ALTER TABLE "app_translation" DROP CONSTRAINT "FK_5af0040c7cadd700bc5413f6b08"
        `);

        await queryRunner.query(`
            ALTER TABLE "webhook"
                ALTER COLUMN "orgEnvId" DROP NOT NULL
        `);
        await queryRunner.query(`
            ALTER TABLE "app_translation"
                ALTER COLUMN "orgEnvId" DROP NOT NULL
        `);

        await queryRunner.query(`
            ALTER TABLE "webhook" DROP CONSTRAINT "PK_2b0218bbe54cf898a596645cdbc"
        `);
        await queryRunner.query(`
            ALTER TABLE "webhook"
                ADD CONSTRAINT "PK_e6765510c2d078db49632b59020" PRIMARY KEY ("id")
        `);

        await queryRunner.query(`
            ALTER TABLE "app_translation" DROP CONSTRAINT "PK_c8d028ad447052a757287ae8d91"
        `);
        await queryRunner.query(`
            ALTER TABLE "app_translation"
                ADD CONSTRAINT "PK_0a1a7d59f78dc64e527ab180e80" PRIMARY KEY ("id")
        `);

        await queryRunner.query(`
            ALTER TABLE "refresh_token" DROP CONSTRAINT "PK_9d9239e21736fef205b3e0ce37f"
        `);
        await queryRunner.query(`
            ALTER TABLE "refresh_token"
                ADD CONSTRAINT "PK_b575dd3c21736fef205b3e0ce7fe" PRIMARY KEY ("id")
        `);

        await queryRunner.query(`
            ALTER TABLE "activation" DROP CONSTRAINT "PK_7f8d537472523212bf095acb3ad"
        `);
        await queryRunner.query(`
            ALTER TABLE "activation"
                ADD CONSTRAINT "PK_bb655734797dc39eddfa0fcca92" PRIMARY KEY ("id")
        `);

        await queryRunner.query(`
            ALTER TABLE "webhook"
                ADD COLUMN "appOrgEnv" character varying
        `);
        await queryRunner.query(`
            ALTER TABLE "app_translation"
                RENAME COLUMN "orgEnvId" TO "appOrgEnv"
        `);
        await queryRunner.query(`
            ALTER TABLE "refresh_token"
                RENAME COLUMN "orgEnvId" TO "userOrgEnv"
        `);
        await queryRunner.query(`
            ALTER TABLE "refresh_token"
                ADD COLUMN "appOrgEnv" character varying
        `);
        await queryRunner.query(`
            ALTER TABLE "activation"
                RENAME COLUMN "orgEnvId" TO "appOrgEnv"
        `);
        await queryRunner.query(`
            ALTER TABLE "activation"
                ADD COLUMN "userOrgEnv" character varying
        `);

        await queryRunner.query(`
            ALTER TABLE "refresh_token"
                ADD CONSTRAINT "FK_92f2908f07f30cc7641d6107e55" FOREIGN KEY ("userId", "userOrgEnv") REFERENCES "user"("id", "orgEnvId") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "refresh_token"
                ADD CONSTRAINT "FK_665a6d621b53a4479de3ff1237d" FOREIGN KEY ("appId", "appOrgEnv") REFERENCES "app"("id", "orgEnvId") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "webhook"
                ADD CONSTRAINT "FK_f49964ba6614aba451bc1dd027b" FOREIGN KEY ("appId", "appOrgEnv") REFERENCES "app"("id", "orgEnvId") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "activation"
                ADD CONSTRAINT "FK_d7b6bac581603cff0accfd16c56" FOREIGN KEY ("userId", "userOrgEnv") REFERENCES "user"("id", "orgEnvId") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "activation"
                ADD CONSTRAINT "FK_2c267e6a328184f8b1d5346fea1" FOREIGN KEY ("appId", "appOrgEnv") REFERENCES "app"("id", "orgEnvId") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "app_translation"
                ADD CONSTRAINT "FK_287389edc365c4d1bcd1041ab24" FOREIGN KEY ("appId", "appOrgEnv") REFERENCES "app"("id", "orgEnvId") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
    }

}
