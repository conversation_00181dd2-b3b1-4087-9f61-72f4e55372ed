import { DataTable, setDefaultTimeout } from '@cucumber/cucumber';
import { HttpRequestConfig } from '@superawesome/freekws-common-http-client';
import { SALogger } from '@superawesome/freekws-common-logger';
import { EMemberRole } from '@superawesome/freekws-family-service-common';
import { FamiliesGroupDeletedPayloadDto } from '@superawesome/freekws-queue-messages/webhook';
import { EWebhookName } from '@superawesome/freekws-queue-messages/webhook/webhook.dto';
import assert from 'assert';
import { binding, given, then, when } from 'cucumber-tsflow';

import { OrgLevelWebhookPayload } from '../../src/webhook/types';
import { Utils } from '../../test/utils';
import { orgEnvHost } from '../fixtures/org-env.fixture';
import { UATUtils } from '../utils';

setDefaultTimeout(60 * 1000);
@binding([SALogger])
export class UserPermissionChangedSteps {
  userId: string;
  responseStatus: number;
  appId: string;
  secret: string;

  @given('the org exists with the expected secret')
  org_exists_with_the_expected_result(table: DataTable) {
    const data = table.rowsHash() as {
      appId: string;
      secret: string;
    };

    this.appId = data.appId;
    this.secret = data.secret;
  }

  @when('families-group-deleted for {string} is triggered')
  async user_permission_changed(parentId: string): Promise<void> {
    const timestamp = Date.now();
    const body: OrgLevelWebhookPayload<FamiliesGroupDeletedPayloadDto> = {
      name: EWebhookName.FAMILIES_FAMILY_GROUP_DELETED,
      time: timestamp,
      orgId: '123',
      payload: {
        familyGroupId: '',
        orgEnvId: '',
        orgId: '123',
        members: [
          {
            id: '4234',
            userId: parentId,
            role: EMemberRole.Manager,
            email: '<EMAIL>',
          },
          {
            id: '4234',
            userId: '4353543',
            role: EMemberRole.Supervised,
            email: '<EMAIL>',
          },
          {
            id: '4234',
            userId: '433347',
            role: EMemberRole.Supervised,
            email: '<EMAIL>',
          },
        ],
      },
    };
    const signature = Utils.generateKwsSignature(timestamp, body, this.secret);

    const request = {
      url: `/v1/webhooks/families-group-deleted`,
      method: 'POST',
      data: body,
      headers: {
        'x-kws-signature': `t=${timestamp},v1=${signature}`,
        'x-forwarded-host': orgEnvHost,
      },
    } as HttpRequestConfig;

    const response = await UATUtils.buildClassicWrapperClient().request(request);
    this.responseStatus = response.status;
  }

  @then('families-group-deleted returns http status code no-content')
  http_status_code_ok_is_returned() {
    assert.equal(this.responseStatus, 204);
  }
}
