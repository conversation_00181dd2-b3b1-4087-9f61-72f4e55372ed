{"logger": {"console": {"level": "LOG_LEVEL"}, "sentry": {"dsn": "SENTRY_DSN", "release": "SENTRY_RELEASE"}}, "http": {"cors": {"origin": "HTTP_CORS_ORIGIN"}}, "environment": "ENVIRONMENT", "currentRegion": "CURRENT_REGION", "cacheable": {"enabled": "CACHEABLE_ENABLED", "defaultTtlSecs": "CACHEABLE_DEFAULT_TTL_SECS", "maxCacheSizeEntries": "LRU_CACHE_MAX_ENTRIES"}, "database": {"masterUrl": "DATABASE_URL", "slaveUrls": "DATABASE_READ_URL", "maxConnectionLimit": "DATABASE_MAX_CONNECTION_LIMIT"}, "kafka": {"kafkaHost": "KAFKA_HOST", "outOfOrderTopic": "KAFKA_OUT_OF_ORDER_TOPIC"}, "keycloak": {"realm": "KEYCLOAK_REALM", "clientId": "KEYCLOAK_CLIENT_ID", "secret": "KEYCLOAK_CLIENT_SECRET", "authServerUrl": "KEYCLOAK_BASE_URL", "timeoutMs": "KEYCLOAK_TIMEOUT_MS", "expirationTime": "KEYCLOAK_TOKEN_EXPIRATION", "realmUrl": "KEYCLOAK_REALM_URL", "additionalTrustedIssuers": "KEYCLOAK_ADDITIONAL_TRUSTED_ISSUERS"}, "ageGateService": {"baseURL": "AGE_GATE_SERVICE_URL", "timeoutMs": "AGE_GATE_SERVICE_TIMEOUT_MS", "retries": "AGE_GATE_SERVICE_RETRIES", "initialRetryDelay": "AGE_GATE_SERVICE_INITIAL_RETRY_DELAY"}, "settingsBackend": {"baseURL": "SETTINGS_BACKEND_SERVICE_URL", "timeoutMs": "SETTINGS_BACKEND_SERVICE_TIMEOUT_MS", "retries": "SETTINGS_BACKEND_SERVICE_RETRIES", "initialRetryDelay": "SETTINGS_BACKEND_SERVICE_INITIAL_RETRY_DELAY"}, "familyService": {"baseURL": "FAMILY_SERVICE_SERVICE_URL", "timeoutMs": "FAMILY_SERVICE_SERVICE_TIMEOUT_MS", "retries": "FAMILY_SERVICE_SERVICE_RETRIES", "initialRetryDelay": "FAMILY_SERVICE_SERVICE_INITIAL_RETRY_DELAY"}, "preVerificationService": {"baseURL": "PREVERIFICATION_SERVICE_SERVICE_URL", "timeoutMs": "PREVERIFICATION_SERVICE_SERVICE_TIMEOUT_MS", "retries": "PREVERIFICATION_SERVICE_SERVICE_RETRIES", "initialRetryDelay": "PREVERIFICATION_SERVICE_SERVICE_INITIAL_RETRY_DELAY"}, "callbackBackend": {"baseURL": "CALLBACK_API_URL", "timeoutMs": "CALLBACK_SERVICE_SERVICE_TIMEOUT_MS", "retries": "CALLBACK_SERVICE_SERVICE_RETRIES", "initialRetryDelay": "CALLBACK_SERVICE_SERVICE_INITIAL_RETRY_DELAY"}, "analyticService": {"baseURL": "ANALYTICS_SERVICE_URL", "authHeader": "ANALYTICS_SERVICE_AUTH_HEADER"}, "appWebhooks": {"topicName": "APP_WEBHOOKS_KAFKA_TOPIC", "sendWebhooks": "APP_WEBHOOKS_SEND_WEBHOOKS"}, "encryption": {"secrets": "ENCRYPTION_SECRET_KEYS", "secretVersion": "ENCRYPTION_SECRET_VERSION"}, "badger": {"apiKey": "BADGER_API_KEY"}, "talon": {"apiKey": "TALON_API_KEY", "flowId": "TALON_FLOW_ID"}, "emailService": {"kafkaTopic": "EMAIL_KAFKA_TOPIC", "footer": {"logoSrc": "EMAIL_SA_LOGO_SRC"}, "header": {"headerSrc": "EMAIL_SA_DEFAULT_HEADER_SRC"}}, "brandingApiClient": {"baseURL": "BRANDING_API_URL", "timeoutMs": "BRANDING_API_TIMEOUT_MS", "retries": "BRANDING_API_RETRIES", "initalRetryDelay": "BRANDING_API_INITAL_DELAY", "bailOnStatus": "BRANDING_API_BAIL"}, "analyticsService": {"authString": "ANALYTICS_SERVICE_AUTH_STRING"}, "amplitude": {"apiKey": "AMPLITUDE_API_KEY", "serverUrl": "AMPLITUDE_SERVER_URL"}}