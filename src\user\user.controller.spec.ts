import { BadRequestException, ConflictException, ForbiddenException } from '@nestjs/common';
import { TestingModule } from '@nestjs/testing';
import { EOAuthScope } from '@superawesome/freekws-classic-wrapper-common';
import { FastifyReply } from 'fastify';

import { BadgerService, EbadgerDecision } from './badger.service';
import { TalonService } from './talon.service';
import { UserController } from './user.controller';
import { UserAppActivationDTO, UserRegisterDTO } from './user.dto';
import { UserService } from './user.service';
import { AppService } from '../app/app.service';
import { IAppInfo } from '../app/types';
import { Testing } from '../common/utils';
import { JwtUserPayload, TJwtPayload } from '../oauth/types';
import { OrgEnv } from '../org-env/org-env.entity';

describe('UserController', () => {
  let controller: UserController;
  let userService: UserService;
  let appService: AppService;
  let badgerService: BadgerService;
  let talonService: TalonService;

  beforeEach(async () => {
    const module: TestingModule = await Testing.createModule({
      controllers: [UserController],
    });

    controller = module.get<UserController>(UserController);
    userService = module.get<UserService>(UserService);
    appService = module.get<AppService>(AppService);
    badgerService = module.get<BadgerService>(BadgerService);
    talonService = module.get(TalonService);
  });

  describe('checkUsername', () => {
    const testOrgEnv = {
      id: 'test-org-env-id',
    } as OrgEnv;

    it('should return available true when username is available and passes moderation', async () => {
      const username = 'validUsername';
      const language = 'en-US';

      jest.spyOn(userService, 'isUsernameAvailable').mockResolvedValue(true);
      jest.spyOn(badgerService, 'moderateUsername').mockResolvedValue(EbadgerDecision.ALLOW);

      const result = await controller.checkUsername(username, testOrgEnv, language);

      expect(userService.isUsernameAvailable).toHaveBeenCalledWith(username, testOrgEnv.id);
      expect(badgerService.moderateUsername).toHaveBeenCalledWith(username, language);
      expect(result).toEqual({
        username,
        available: true,
        details: {
          isValid: true,
          isAvailable: true,
          reasons: undefined,
        },
      });
    });

    it('should return available false when username is not available', async () => {
      const username = 'takenUsername';
      const language = 'en-US';

      jest.spyOn(userService, 'isUsernameAvailable').mockResolvedValue(false);
      jest.spyOn(badgerService, 'moderateUsername').mockResolvedValue(EbadgerDecision.ALLOW);

      const result = await controller.checkUsername(username, testOrgEnv, language);

      expect(userService.isUsernameAvailable).toHaveBeenCalledWith(username, testOrgEnv.id);
      expect(badgerService.moderateUsername).toHaveBeenCalledWith(username, language);
      expect(result).toEqual({
        username,
        available: false,
        details: {
          isValid: true,
          isAvailable: false,
          reasons: ['Username is already taken.'],
        },
      });
    });

    it('should return available false when username fails moderation', async () => {
      const username = 'inappropriateUsername';
      const language = 'en-US';

      jest.spyOn(userService, 'isUsernameAvailable').mockResolvedValue(true);
      jest.spyOn(badgerService, 'moderateUsername').mockResolvedValue(EbadgerDecision.REJECT);

      const result = await controller.checkUsername(username, testOrgEnv, language);

      expect(userService.isUsernameAvailable).toHaveBeenCalledWith(username, testOrgEnv.id);
      expect(badgerService.moderateUsername).toHaveBeenCalledWith(username, language);
      expect(result).toEqual({
        username,
        available: false,
        details: {
          isValid: false,
          isAvailable: true,
          reasons: [`Username moderation judgement is '${EbadgerDecision.REJECT}'.`],
        },
      });
    });

    it('should return available false when username is not available and fails moderation', async () => {
      const username = 'takenAndInappropriateUsername';
      const language = 'en-US';

      jest.spyOn(userService, 'isUsernameAvailable').mockResolvedValue(false);
      jest.spyOn(badgerService, 'moderateUsername').mockResolvedValue(EbadgerDecision.REJECT);

      const result = await controller.checkUsername(username, testOrgEnv, language);

      expect(userService.isUsernameAvailable).toHaveBeenCalledWith(username, testOrgEnv.id);
      expect(badgerService.moderateUsername).toHaveBeenCalledWith(username, language);
      expect(result).toEqual({
        username,
        available: false,
        details: {
          isValid: false,
          isAvailable: false,
          reasons: ['Username is already taken.', `Username moderation judgement is '${EbadgerDecision.REJECT}'.`],
        },
      });
    });

    it('should handle undefined language parameter', async () => {
      const username = 'validUsername';

      jest.spyOn(userService, 'isUsernameAvailable').mockResolvedValue(true);
      jest.spyOn(badgerService, 'moderateUsername').mockResolvedValue(EbadgerDecision.ALLOW);

      const result = await controller.checkUsername(username, testOrgEnv);

      expect(userService.isUsernameAvailable).toHaveBeenCalledWith(username, testOrgEnv.id);
      expect(badgerService.moderateUsername).toHaveBeenCalledWith(username, undefined);
      expect(result).toEqual({
        username,
        available: true,
        details: {
          isValid: true,
          isAvailable: true,
          reasons: undefined,
        },
      });
    });
  });

  describe('activateUser', () => {
    const mockAppId = 123;
    const mockAppName = 'testApp';
    const mockOauthClientId = 'test-client';
    const mockBody: UserAppActivationDTO = {
      appName: 'testApp',
      permissions: ['read_profile'],
    };
    const mockUserId = 1;
    const mockJwt = {
      appId: mockAppId,
      clientId: mockOauthClientId,
      scope: EOAuthScope.USER,
      userId: mockUserId,
      appPermissions: ['chat.voice'],
      isMinor: true,
    } satisfies JwtUserPayload;
    const orgEnv = {
      id: '1',
    } as OrgEnv;

    const mockRes = {
      header: jest.fn().mockReturnThis(),
      send: jest.fn().mockReturnThis(),
    } as unknown as FastifyReply;

    it('should throw ForbiddenException when token is not a user token', async () => {
      const nonUserJwt = {
        ...mockJwt,
        scope: EOAuthScope.APP,
      } as unknown as TJwtPayload;

      await expect(controller.activateUser(mockBody, mockUserId, orgEnv, nonUserJwt, mockRes)).rejects.toThrow(
        'Token must be of scope "user"',
      );
    });

    it('should throw ForbiddenException when token userId does not match requested userId', async () => {
      const differentUserIdJwt = {
        ...mockJwt,
        userId: 999,
      };

      await expect(controller.activateUser(mockBody, mockUserId, orgEnv, differentUserIdJwt, mockRes)).rejects.toThrow(
        'Token does not belong to requested user',
      );
    });

    it('should throw ForbiddenException when token clientId does not match app oauthClientId', async () => {
      jest.spyOn(appService, 'getAppInfoBy').mockResolvedValueOnce({
        app: {
          id: mockAppId,
          name: mockAppName,
          oauthClientId: 'different-client-id',
        },
      } as IAppInfo);

      await expect(controller.activateUser(mockBody, mockUserId, orgEnv, mockJwt, mockRes)).rejects.toThrow(
        'Token does not belong to requested app',
      );
    });

    it('should throw ConflictException when user already has an activation for the app', async () => {
      jest.spyOn(appService, 'getAppInfoBy').mockResolvedValueOnce({
        app: {
          id: mockAppId,
          name: mockAppName,
          oauthClientId: mockOauthClientId,
        },
      } as IAppInfo);

      jest.spyOn(appService, 'userHasActivation').mockResolvedValueOnce(true);

      await expect(controller.activateUser(mockBody, mockUserId, orgEnv, mockJwt, mockRes)).rejects.toThrow(
        'App activation already exists',
      );
    });

    it('should throw ForbiddenException when token does not belong to requested app', async () => {
      jest.spyOn(appService, 'getAppInfoBy').mockResolvedValueOnce({
        app: {
          name: 'differentApp',
        },
      } as IAppInfo);

      await expect(controller.activateUser(mockBody, mockUserId, orgEnv, mockJwt, mockRes)).rejects.toBeInstanceOf(
        ForbiddenException,
      );
    });

    it('should throw ForbiddenException when app is already activated', async () => {
      jest.spyOn(appService, 'getAppInfoBy').mockResolvedValue({
        app: {
          name: 'testApp',
        },
      } as IAppInfo);

      jest.spyOn(appService, 'userHasActivation').mockImplementation(() => Promise.resolve(true));

      await expect(controller.activateUser(mockBody, mockUserId, orgEnv, mockJwt, mockRes)).rejects.toBeInstanceOf(
        ForbiddenException,
      );
    });

    it('should activate user when token belongs to requested app', async () => {
      jest.spyOn(appService, 'getAppInfoBy').mockResolvedValueOnce({
        app: {
          id: mockAppId,
          name: mockAppName,
          oauthClientId: mockOauthClientId,
        },
      } as IAppInfo);
      const activateUserToAppSpy = jest.spyOn(userService, 'activateUserToApp');

      await expect(controller.activateUser(mockBody, mockUserId, orgEnv, mockJwt, mockRes)).resolves.not.toThrow();

      expect(activateUserToAppSpy).toHaveBeenCalledWith(mockUserId, mockJwt.appId, mockBody, orgEnv.id);
      expect(mockRes.header).toHaveBeenCalledWith('Content-Type', 'application/json; charset=utf-8');
      expect(mockRes.send).toHaveBeenCalledWith('');
    });
  });

  describe('registerUser', () => {
    const mockRegisterDTO: UserRegisterDTO = {
      username: 'testuser',
      password: 'password123',
      dateOfBirth: '2010-01-01',
      token: 'valid-token',
      originAppId: 123,
      language: 'en',
    };

    const mockHeaders = {
      'user-agent': 'test-agent',
      'accept-language': 'en',
    };

    const orgEnv = {
      id: 'test-org-env-id',
    } as OrgEnv;

    it('should register a user successfully', async () => {
      jest.spyOn(talonService, 'verify').mockResolvedValue(true);
      jest.spyOn(userService, 'isUsernameAvailable').mockResolvedValue(true);
      jest.spyOn(badgerService, 'moderateUsername').mockResolvedValue(EbadgerDecision.ALLOW);
      jest.spyOn(userService, 'createUserV2').mockResolvedValue(999);

      const result = await controller.registerUser(mockRegisterDTO, orgEnv, mockHeaders, void 0);

      expect(result).toEqual({ id: 999 });
      expect(talonService.verify).toHaveBeenCalledWith(mockRegisterDTO.token, mockHeaders, mockRegisterDTO.parentEmail);
      expect(userService.isUsernameAvailable).toHaveBeenCalledWith(mockRegisterDTO.username, orgEnv.id);
      expect(badgerService.moderateUsername).toHaveBeenCalledWith(mockRegisterDTO.username, mockRegisterDTO.language);
      expect(userService.createUserV2).toHaveBeenCalledWith(mockRegisterDTO, 'ZZ', orgEnv.id);
    });

    it('should use provided country parameter', async () => {
      jest.spyOn(talonService, 'verify').mockResolvedValue(true);
      jest.spyOn(userService, 'isUsernameAvailable').mockResolvedValue(true);
      jest.spyOn(badgerService, 'moderateUsername').mockResolvedValue(EbadgerDecision.ALLOW);
      jest.spyOn(userService, 'createUserV2').mockResolvedValue(999);

      const mockRegisterDTOWithCountry = { ...mockRegisterDTO, country: 'US' };
      await controller.registerUser(mockRegisterDTOWithCountry, orgEnv, mockHeaders);

      expect(userService.createUserV2).toHaveBeenCalledWith(mockRegisterDTOWithCountry, 'US', orgEnv.id);
    });

    it('should use country param when DTO country is not provided', async () => {
      jest.spyOn(talonService, 'verify').mockResolvedValue(true);
      jest.spyOn(userService, 'isUsernameAvailable').mockResolvedValue(true);
      jest.spyOn(badgerService, 'moderateUsername').mockResolvedValue(EbadgerDecision.ALLOW);
      jest.spyOn(userService, 'createUserV2').mockResolvedValue(999);

      await controller.registerUser(mockRegisterDTO, orgEnv, mockHeaders, 'GB');

      expect(userService.createUserV2).toHaveBeenCalledWith(mockRegisterDTO, 'GB', orgEnv.id);
    });

    it('should throw ForbiddenException when talon verification fails', async () => {
      jest.spyOn(talonService, 'verify').mockResolvedValue(false);

      await expect(controller.registerUser(mockRegisterDTO, orgEnv, mockHeaders)).rejects.toThrow(
        new ForbiddenException('Suspected Bot from Talon'),
      );
    });

    it('should throw ConflictException when username is already taken', async () => {
      jest.spyOn(talonService, 'verify').mockResolvedValue(true);
      jest.spyOn(userService, 'isUsernameAvailable').mockResolvedValue(false);

      await expect(controller.registerUser(mockRegisterDTO, orgEnv, mockHeaders)).rejects.toThrow(
        new ConflictException('Username is already taken'),
      );
    });

    it('should throw BadRequestException when username fails moderation', async () => {
      jest.spyOn(talonService, 'verify').mockResolvedValue(true);
      jest.spyOn(userService, 'isUsernameAvailable').mockResolvedValue(true);
      jest.spyOn(badgerService, 'moderateUsername').mockResolvedValue(EbadgerDecision.REJECT);

      await expect(controller.registerUser(mockRegisterDTO, orgEnv, mockHeaders)).rejects.toThrow(
        new BadRequestException('Username rejected by moderation'),
      );
    });
  });
});
