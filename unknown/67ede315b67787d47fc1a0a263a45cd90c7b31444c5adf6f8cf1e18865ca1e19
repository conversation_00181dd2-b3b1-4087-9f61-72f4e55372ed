import { INestApplication } from '@nestjs/common';
import request from 'supertest';

import { Utils } from '../utils';

describe('EventsController (e2e)', () => {
  let app: INestApplication;

  beforeAll(async () => {
    app = await Utils.createTestServer();
    Utils.mockAnalyticServiceAPI();
  });

  beforeEach(async () => {
    await Utils.cleanDb();
    await Utils.loadFixtures();
  });

  afterAll(async () => {
    await Utils.stopTestServer(app);
    jest.clearAllMocks();
  });

  describe('POST /v2/events', () => {
    const validAuthString = 'SomeTestAuthString'; // From test config
    const invalidAuthString = 'InvalidAuthString';

    const validRequestBody = {
      events: [
        {
          name: 'user_login',
          data: { loginMethod: 'email' },
        },
        {
          name: 'page_view',
          data: { page: 'dashboard' },
        },
      ],
      session: {
        id: 'session-123',
        userId: 'user-456',
        user: { name: 'Test User' },
      },
      time: 1647123456789,
      geo: 'US',
    };

    it('should process events successfully with valid auth string', async () => {
      await request(app.getHttpServer())
        .post('/v2/events')
        .set('Authorization', validAuthString)
        .send(validRequestBody)
        .expect(200);
    });

    it('should return 401 with invalid auth string', async () => {
      await request(app.getHttpServer())
        .post('/v2/events')
        .set('Authorization', invalidAuthString)
        .send(validRequestBody)
        .expect(401);
    });

    it('should return 401 when auth header is missing', async () => {
      await request(app.getHttpServer()).post('/v2/events').send(validRequestBody).expect(401);
    });

    it('should process events with minimal data', async () => {
      const minimalRequestBody = {
        events: [
          {
            name: 'test_event',
            data: { test: true },
          },
        ],
      };

      await request(app.getHttpServer())
        .post('/v2/events')
        .set('Authorization', validAuthString)
        .send(minimalRequestBody)
        .expect(200);
    });

    it('should return 400 for invalid request body', async () => {
      const invalidRequestBody = {
        // Missing required 'events' field
        session: {
          id: 'session-123',
        },
      };

      await request(app.getHttpServer())
        .post('/v2/events')
        .set('Authorization', validAuthString)
        .send(invalidRequestBody)
        .expect(400);
    });

    it('should return 400 for invalid event structure', async () => {
      const invalidEventBody = {
        events: [
          {
            // Missing required 'name' field
            data: { test: true },
          },
        ],
      };

      await request(app.getHttpServer())
        .post('/v2/events')
        .set('Authorization', validAuthString)
        .send(invalidEventBody)
        .expect(400);
    });
  });
});
