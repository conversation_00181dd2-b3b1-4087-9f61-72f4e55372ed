import { UnauthorizedException } from '@nestjs/common';
import { TestingModule } from '@nestjs/testing';

import { EventsController } from './events.controller';
import { AnalyticsEventsDataDTO } from './events.dto';
import { AmplitudeService } from '../amplitude/amplitude.service';
import { ConfigService } from '../common/services/config/config.service';
import { Testing } from '../common/utils';

describe('EventsController', () => {
  let controller: EventsController;

  const mockAmplitudeService = {
    sendToAmplitude: jest.fn(),
  };

  const mockConfigService = {
    getConfig: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Testing.createModule({
      controllers: [EventsController],
      providers: [
        { provide: ConfigService, useValue: mockConfigService },
        { provide: AmplitudeService, useValue: mockAmplitudeService },
      ],
    });

    controller = module.get<EventsController>(EventsController);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('eventsV2', () => {
    const validAuthString = 'valid-auth-string';
    const invalidAuthString = 'invalid-auth-string';

    const mockRequestBody: AnalyticsEventsDataDTO = {
      events: [
        {
          name: 'user_login',
          data: { loginMethod: 'email' },
        },
        {
          name: 'page_view',
          data: { page: 'dashboard' },
        },
      ],
      session: {
        id: 'session-123',
        userId: 'user-456',
        user: { name: 'Test User' },
      },
      time: 1647123456789,
      geo: 'US',
    };

    beforeEach(() => {
      mockConfigService.getConfig.mockReturnValue({
        analyticsServiceAuthString: validAuthString,
      });
    });

    it('should process events successfully with valid auth string', async () => {
      await controller.eventsV2(mockRequestBody, validAuthString);

      expect(mockAmplitudeService.sendToAmplitude).toHaveBeenCalledTimes(2);

      // Verify first event
      expect(mockAmplitudeService.sendToAmplitude).toHaveBeenNthCalledWith(1, {
        user_id: 'user-456',
        event_type: 'user_login',
        event_properties: { loginMethod: 'email' },
        user_properties: {
          id: 'session-123',
          userId: 'user-456',
          user: { name: 'Test User' },
          repository: 'freekws-classic-wrapper-backend',
        },
        time: 1647123456789,
        country: 'US',
      });

      // Verify second event
      expect(mockAmplitudeService.sendToAmplitude).toHaveBeenNthCalledWith(2, {
        user_id: 'user-456',
        event_type: 'page_view',
        event_properties: { page: 'dashboard' },
        user_properties: {
          id: 'session-123',
          userId: 'user-456',
          user: { name: 'Test User' },
          repository: 'freekws-classic-wrapper-backend',
        },
        time: 1647123456789,
        country: 'US',
      });
    });

    it('should throw UnauthorizedException with invalid auth string', async () => {
      await expect(controller.eventsV2(mockRequestBody, invalidAuthString)).rejects.toThrow(UnauthorizedException);

      expect(mockAmplitudeService.sendToAmplitude).not.toHaveBeenCalled();
    });

    it('should use default values when session is missing', async () => {
      const requestWithoutSession: AnalyticsEventsDataDTO = {
        events: [{ name: 'test_event', data: { test: true } }],
      };

      await controller.eventsV2(requestWithoutSession, validAuthString);

      expect(mockAmplitudeService.sendToAmplitude).toHaveBeenCalledWith({
        user_id: 'NoUserIdProvided',
        event_type: 'test_event',
        event_properties: { test: true },
        user_properties: {
          id: 'FFFFFFFF-FFFF-FFFF-FFFF-FFFFFFFFFFFF',
          userId: 'NoUserIdProvided',
          user: {},
          repository: 'freekws-classic-wrapper-backend',
        },
        time: expect.any(Number),
        country: 'NoLocationProvided',
      });
    });

    it('should use default values when session fields are missing', async () => {
      const requestWithPartialSession: AnalyticsEventsDataDTO = {
        events: [{ name: 'test_event', data: { test: true } }],
        session: {},
      };

      await controller.eventsV2(requestWithPartialSession, validAuthString);

      expect(mockAmplitudeService.sendToAmplitude).toHaveBeenCalledWith({
        user_id: 'NoUserIdProvided',
        event_type: 'test_event',
        event_properties: { test: true },
        user_properties: {
          id: 'FFFFFFFF-FFFF-FFFF-FFFF-FFFFFFFFFFFF',
          userId: 'NoUserIdProvided',
          user: {},
          repository: 'freekws-classic-wrapper-backend',
        },
        time: expect.any(Number),
        country: 'NoLocationProvided',
      });
    });

    it('should use default time when time is missing', async () => {
      const requestWithoutTime: AnalyticsEventsDataDTO = {
        events: [{ name: 'test_event', data: { test: true } }],
        session: { userId: 'test-user' },
      };

      const dateSpy = jest.spyOn(Date, 'now').mockReturnValue(1234567890);

      await controller.eventsV2(requestWithoutTime, validAuthString);

      expect(mockAmplitudeService.sendToAmplitude).toHaveBeenCalledWith({
        user_id: 'test-user',
        event_type: 'test_event',
        event_properties: { test: true },
        user_properties: {
          id: 'FFFFFFFF-FFFF-FFFF-FFFF-FFFFFFFFFFFF',
          userId: 'test-user',
          user: {},
          repository: 'freekws-classic-wrapper-backend',
        },
        time: 1234567890,
        country: 'NoLocationProvided',
      });

      dateSpy.mockRestore();
    });

    it('should use default geo when geo is missing', async () => {
      const requestWithoutGeo: AnalyticsEventsDataDTO = {
        events: [{ name: 'test_event', data: { test: true } }],
        session: { userId: 'test-user' },
        time: 1647123456789,
      };

      await controller.eventsV2(requestWithoutGeo, validAuthString);

      expect(mockAmplitudeService.sendToAmplitude).toHaveBeenCalledWith({
        user_id: 'test-user',
        event_type: 'test_event',
        event_properties: { test: true },
        user_properties: {
          id: 'FFFFFFFF-FFFF-FFFF-FFFF-FFFFFFFFFFFF',
          userId: 'test-user',
          user: {},
          repository: 'freekws-classic-wrapper-backend',
        },
        time: 1647123456789,
        country: 'NoLocationProvided',
      });
    });
  });
});
